{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个耀光", "Id": "Anim_LightOfAzem_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Light,1,8,true,false)"]}, {"说明": "动画中引发闪光", "Id": "Anim_LightHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffCastById_StackToHpRecoverOnHit(Rogue_Light,0,0.02,1,false)", "RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,0,1,false,true)"]}, {"说明": "动画中无消耗引发闪光", "Id": "Anim_LightHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffCastById_StackToHpRecoverOnHit(Rogue_Light,0,0.02,1,false)", "RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,0,1,false,true,false)"]}, {"说明": "动画中概率引发闪光", "Id": "Anim_ChanceLightHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffCastById_StackToHpRecoverOnHit(Rogue_Light,0,0.02,1,false)", "RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,0,1,false,true)"]}, {"说明": "动画中产生复数光弹", "Id": "Anim_JustDodgeLight", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Azem11,1,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeLight,1,0,true)"]}, {"说明": "动画中生成光盾特效并减伤", "Id": "Anim_LightShield", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(LightShield,ArtResource/ProjectRogue/VFX/ParagonZ/p_Ult_Windzone_Shield_Yellow,Mesh,false,1)"], "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_<PERSON>,1,5,true,false)", "BuffUtils.HurtDamageUp(-0.3)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(LightShield)"]}, {"说明": "动画中生成光盾特效并反伤", "Id": "An<PERSON>_WindShield", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia8,256,root,NoTween,true)"], "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_<PERSON>,1,5,true,false)", "RogueBuff.CounterattackOnHurtByCarrierProp(15,0.7)"]}, {"分割": "-------------------------------------------Light-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个耀光", "Id": "LightOfAzem_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightOfAzem_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightOfAzem_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率给对方一个闪光", "Id": "LightOfAzem_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceLightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceLightHit,1,0,true)"]}, {"说明": "下砸技能时，产生一道光芒AOE，每秒向周遭的敌人进行一次攻击", "Id": "Smash_LightAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_3_AzemBullet,10,Root_Bullet,BulletScript.GoStraightAhead(5000))"]}, {"说明": "下砸技能时，被击中的目标引发“闪光”效果。", "Id": "Smash_LightHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“闪光”效果。", "Id": "Dash_LightCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "玩家蓄力时，在自身周围产生光盾，减伤，给敌人一层光耀。", "Id": "Power_LightShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightShield,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightShield,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“闪光”效果。", "Id": "Power_LightHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个光点尾迹aoe。", "Id": "LightDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Azem10,1.6,Root)"]}, {"说明": "玩家完美闪避时，在自身位置产生n个自动追踪的光弹，光弹命中给敌人一层“闪光”", "Id": "Anim_DogeCreateLightBullet", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Azem11,1,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeLight,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeLight,1,0,true)"]}, {"说明": "击飞技能时，击中有”耀光“层数的单位时，引发“闪光”效果。", "Id": "Rise_LightCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "造成光元素伤害的时候概率对光元素的主动道具进行额外充能", "Id": "AddItemRecover_Light", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Light)"]}, {"说明": "每过TickTime秒发射一个光剑", "Id": "Launch_LightTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Azem17,0,0.3,pelvis)"]}, {"说明": "受击时在敌人处产生一道圣光,CDxxx秒", "Id": "Hurt_LightAoe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.CreateAoeOnEnemySocketByCoolDown(Rogue_Aoe_Azem18,1,root,0,true)"]}]}