{"Class": [{"说明": "刺客-萤-Yin", "Id": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Cike", "BladeDancer", "Swordsman", "<PERSON><PERSON>_Elf"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Sola", "StateActions": {"Ground": {"Armed": "Dagger_Move", "Unarmed": "Da<PERSON>_Unarmed_Move"}, "Flying": {"Armed": "Dagger_Move", "Unarmed": "Da<PERSON>_Unarmed_Move"}, "Falling": {"Armed": "Dagger_Fall", "Unarmed": "<PERSON><PERSON>_Unarmed_Fall"}, "Attached": {"Armed": "Dagger_Ride", "Unarmed": "Dagger_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Da<PERSON>_<PERSON>", "UnArmed": "Da<PERSON>_<PERSON>"}, "Blow": {"Armed": "<PERSON><PERSON>_<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON><PERSON>"}, "Frozen": {"Armed": "<PERSON><PERSON>_Frozen", "UnArmed": "<PERSON><PERSON>_Frozen"}, "Bounced": {"Armed": "<PERSON><PERSON>_Bounced", "UnArmed": "<PERSON><PERSON>_Bounced"}, "Dead": {"Armed": "Dagger_Dead", "UnArmed": "Dagger_Dead"}, "Landing": {"Armed": "<PERSON><PERSON>_JustFall", "UnArmed": "<PERSON><PERSON>_<PERSON><PERSON>_JustFall"}, "SecondWind": {"Armed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, "GetUp": {"Armed": "Dagger_RevivedOnSecWind", "UnArmed": "Dagger_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 890], "BeStrikeRate": 1.0, "CriticalChance": 0.1, "CriticalRate": 1.5, "AirDodgePoint": 1}, "WeaponType": "PoleArm", "DefaultWeapons": ["Iron_Spear"], "ActionOnChangeTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________匕首徒手基础动作________________________________"}, {"说明": "匕首徒手走路站立", "Id": "Da<PERSON>_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_Unarmed_Jump", "<PERSON><PERSON>_<PERSON><PERSON>_Dodge", "<PERSON>ike_Aim", "<PERSON><PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive", "Da<PERSON>_DrawAttack", "<PERSON><PERSON>_<PERSON>ttack"], "1": ["<PERSON><PERSON>_Unarmed_Jump", "<PERSON><PERSON>_<PERSON><PERSON>_Dodge", "<PERSON>ike_Aim", "<PERSON><PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/UnarmedMove", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/BS_Throw_Aim"]}}, {"说明": "匕首徒手起跳", "Id": "<PERSON><PERSON>_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "<PERSON><PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["Da<PERSON>_Air_DrawAttack", "Dagger_AirSkillAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Movement/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "匕首徒手翻滚", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON><PERSON>_Dodge", "From": 0}], "BeCancelledTags": {"0": ["Da<PERSON>_Unarmed_Move", "<PERSON><PERSON>_Unarmed_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "匕首徒手下落", "Id": "<PERSON><PERSON>_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/UnarmedFall"]}, "Priority": 1}, {"说明": "匕首徒手下落着地", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "<PERSON><PERSON>_Unarmed_Jump", "Unarmed_Dagger_<PERSON>", "Interactive", "Dagger_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Movement/UnarmedJustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Movement/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "匕首收刀", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Action/SheathWeapon"]}}, {"说明": "匕首拔刀", "Id": "DrawSpear", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "<PERSON><PERSON>_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Action/DrawWeapon"]}}, {"Line": "_______________________________匕首(LevelSquencer)动作________________________________"}, {"说明": "Rogue大厅开场动作", "Id": "Rogue_Hall_Begin", "Cmds": ["Rogue_Hall_Begin"], "Tags": [{"Tag": "Rogue_Hall_Begin", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_Unarmed_Jump", "<PERSON><PERSON>_<PERSON><PERSON>_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_Yin/Action/Rogue_Begin_Hall"]}, "InitAction": true}, {"说明": "Rogue大厅重生动作", "Id": "<PERSON>_<PERSON>_Respawn", "Cmds": ["<PERSON>_<PERSON>_Respawn"], "Tags": [{"Tag": "<PERSON>_<PERSON>_Respawn", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_Unarmed_Jump", "<PERSON><PERSON>_<PERSON><PERSON>_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Action/Rogue_Begin_DeathRespawn_Hall"]}, "InitAction": true}, {"说明": "Rogue房间开始动作", "Id": "Rogue_Room_Start", "Cmds": ["Rogue_Room_Start"], "Tags": [{"Tag": "Rogue_Room_Start", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Action/Rogue_Begin_Dungeon"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_01", "Cmds": ["Rogue_SecondRoundEndSeq_01"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Sequence"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Action/Rogue_SecondRoundEndSeq_01"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_02", "Cmds": ["Rogue_SecondRoundEndSeq_02"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Action/Rogue_SecondRoundEndSeq_02"]}, "InitAction": true}, {"说明": "Rogue31关最终结束动画", "Id": "Rogue_FinalSeq", "Cmds": ["Rogue_FinalSeq"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Action/Rogue_FinalSeq"]}, "InitAction": true}, {"Line": "_______________________________匕首(持武器)基础动作________________________________"}, {"Id": "Dagger_Move", "Cmds": ["Dagger_Move"], "Tags": [{"Tag": "Dagger_Move", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["Dagger_InitAttack", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Ci<PERSON><PERSON>_<PERSON>/Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/Ci<PERSON><PERSON>_<PERSON>/Move_AimState"]}}, {"Id": "Da<PERSON>_<PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["<PERSON><PERSON>_<PERSON>"], "2": ["Dagger_QS_B"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON><PERSON><PERSON><PERSON>_<PERSON>/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>_<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_QS_B"], "1": ["Dagger_QS_F"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Blow_Front"]}}, {"Id": "Dagger_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Dead"]}, "InitAction": true}, {"Id": "Dagger_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "Dagger_Jump", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}, {"Tag": "<PERSON><PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_Air_Dodge_Step"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Movement/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "Dagger_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["Dagger_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Fall"]}, "Priority": 1}, {"Id": "<PERSON><PERSON>_JustFall", "BeCancelledTags": {"0": ["Dagger_Move", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>", "Interactive", "Dagger_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Movement/JustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Movement/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Dagger_Move", "Dagger_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>_AJ", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Move", "Dagger_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "<PERSON><PERSON>_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Dodge/Step_F"]}, "Cost": {"SP": 0}}, {"Id": "Da<PERSON>_Air_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "Da<PERSON>_Air_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_<PERSON>_Dodge_Step_Second"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Dodge/Air_Dodge_Dash"]}, "InitAction": true}, {"Id": "Da<PERSON>_<PERSON>_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "Da<PERSON>_<PERSON>_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Dodge/Air_Dodge_Dash_Second"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "Da<PERSON>_<PERSON>_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "Da<PERSON>_<PERSON>_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Dodge/Air_Dodge_Dash"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "Da<PERSON>_<PERSON>_Dodge_Success", "Cmds": ["_"], "Tags": [{"Tag": "Da<PERSON>_<PERSON>_Dodge_Success", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>ttack", "<PERSON><PERSON>_Dodge_Step", "Da<PERSON>_<PERSON>_Dodge_CounterAtk", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Dodge/Dodge_F_Just_Success"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "Da<PERSON>_<PERSON>_Dodge_Success_CounterAtk", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "Da<PERSON>_<PERSON>_Dodge_CounterAtk", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "<PERSON><PERSON>_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_JustDodge_CounterAtk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "Dagger_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Dagger_QS_B", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "<PERSON><PERSON>_<PERSON>", "Dagger_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身动作前翻", "Id": "Dagger_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Dagger_QS_F", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "<PERSON><PERSON>_<PERSON>", "Dagger_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/SecondWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "Dagger_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 999, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Hit/RevivedOnSecondWind"]}}, {"Line": "_______________________________匕首_防御_动作_______________________________"}, {"Id": "Cike_Defense", "Cmds": ["Aim"], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}, {"Tag": "Dagger_Defense", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Defense"]}}, {"说明": "防御成功", "Id": "Cike_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "<PERSON><PERSON>_<PERSON>", "Dagger_Defense", "Unarm_UseItem"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_Yin/Battle/G_Defense_Success"]}}, {"Line": "_______________________________匕首受击(特殊)动作________________________________"}, {"Id": "<PERSON><PERSON>_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Hit/Hurt_Frozen"]}}, {"Line": "_______________________________匕首特殊动作________________________________"}, {"Line": "_______________________________匕首基础(附加)动作________________________________"}, {"说明": "匕首弹刀动作", "Id": "<PERSON><PERSON>_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Dagger_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/Bounced"]}}, {"说明": "瞄准动作", "Id": "<PERSON><PERSON>_Aim", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_Aim", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>", "Dagger_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Action/Cike_Aim"]}}, {"Line": "_______________________________匕首战斗触发动作________________________________"}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "Cike_DashSpike_HitJump", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["Dagger_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_HitJump"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "Cike_DashSpike_HitJump2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["Dagger_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_HitJump2"]}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "Cike_DashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapDownSlash"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Cike_RiseSlash2_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_RiseSlash2"]}}, {"Line": "_______________________________法器反击_______________________________"}, {"说明": "法器反击成功了就自动变成这个了(仅用于justblock)", "Id": "FrozenParry_JustBlock", "Cmds": [], "Tags": [{"Tag": "FrozenParry_JustBlock", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_Yin/Action/MagicItem/MagicItem_Parry_CounterAtk"]}}], "RogueBattleActions": [{"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Action/PickUp"]}}, {"Line": "_______________________________匕首_普通攻击_动作_______________________________"}, {"Line": "_______________________________匕首_普攻_地面_______________________________"}, {"Id": "Cike_LAttack01", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "Dagger_LAttack1", "From": 0}, {"Tag": "Da<PERSON>_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_LAttack2", "<PERSON><PERSON>_LAttack2_T1"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["Dagger_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash0"]}, "InitAction": true}, {"Id": "Cike_LAttack02", "Cmds": [], "Tags": [{"Tag": "Dagger_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_LAttack3"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash1"]}, "InitAction": true}, {"Id": "Cike_LAttack03", "Cmds": [], "Tags": [{"Tag": "Dagger_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_LAttack1"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash2"]}, "InitAction": true}, {"说明": "匕首分支普通攻击1", "Id": "Cike_AttackB1", "Cmds": [], "Tags": [{"Tag": "Dagger_AttackB1", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_AttackB2"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_SlashB1"]}, "InitAction": true}, {"说明": "匕首分支普通攻击2", "Id": "Cike_AttackB2", "Cmds": [], "Tags": [{"Tag": "Dagger_AttackB2", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_AttackB3"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_SlashB2"]}, "InitAction": true}, {"说明": "匕首分支普通攻击3", "Id": "Cike_AttackB3", "Cmds": [], "Tags": [{"Tag": "Dagger_AttackB3", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_InitAttack"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_SlashB3"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击2", "Id": "Cike_LAttack02_T1", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_LAttack2_T1", "From": 0.0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_LAttack3_T1"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash1_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击3", "Id": "Cike_LAttack03_T1", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_LAttack3_T1", "From": 0.0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_LAttack4_T1"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash2_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击4", "Id": "Cike_LAttack04_T1", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_LAttack4_T1", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_InitAttack"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_Slash3_T1"]}, "InitAction": true}, {"Line": "_______________________________匕首_普攻_空中_______________________________"}, {"Id": "Cike_AirAttack1", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirAttack1", "From": 0}, {"Tag": "Da<PERSON>_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirAttack2"], "1": ["_"], "2": ["Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_Slash0"]}, "InitAction": true}, {"Id": "Cike_AirAttack2", "Cmds": [], "Tags": [{"Tag": "Dagger_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirAttack1"], "1": ["_"], "2": ["Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________匕首_技能A_地面Action2_______________________________"}, {"Id": "Cike_RiseDaggerSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirSkillAttack", "Unarm_UseItem", "Dagger_AirInitAttack"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_RiseSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Cike_RiseSpinSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_AirSkillAttack", "Unarm_UseItem", "Dagger_AirInitAttack"], "1": ["<PERSON><PERSON>_Dodge_Step", "Da<PERSON>_Air_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem", "Dagger_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_RiseSpinSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________匕首_技能A(下落)_空中Action2_______________________________"}, {"Id": "Cike_AirDaggerDownSpin", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DaggerDownSpin"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________匕首_技能B(突刺)_地面Action3_______________________________"}, {"说明": "向前冲刺", "Id": "Cike_DashDaggerSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem", "DashDaggerSlash2"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashDaggerSlash0"]}}, {"说明": "向前冲刺第二下", "Id": "Cike_DashDaggerSlash2", "Cmds": [], "Tags": [{"Tag": "DashDaggerSlash2", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 4, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashDaggerSlash1"]}}, {"说明": "丢出钩锁", "Id": "<PERSON><PERSON>_HookDash_Start", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_HookDash_Start"]}}, {"说明": "丢出钩锁-空中", "Id": "<PERSON><PERSON>_HookDashAir_Start", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["Da<PERSON>_Air_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_HookDash_Start"]}}, {"说明": "丢出钩锁-命中后飞出", "Id": "<PERSON><PERSON>_<PERSON>Dash_Mid", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["Da<PERSON>_Air_Dodge_Step"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_HookDash_Mid"]}}, {"说明": "丢出钩锁-命中后上踢", "Id": "<PERSON><PERSON>_HookDash_End", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Unarm_UseItem"], "1": ["Da<PERSON>_Air_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_HookDash_End"]}}, {"Line": "_______________________________匕首_技能B(突刺、后续技能)_空中Action3_______________________________"}, {"Id": "Cike_AirDashSpinSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step", "Da<PERSON>_Air_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSpinSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________匕首_技能C(位移、后续技能)_地面Action4_______________________________"}, {"Id": "Cike_DashThrust", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashThrust"]}, "Cost": {"MP": 0}}, {"Id": "Cike_DashSweapSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashSweapSlash"]}, "Cost": {"MP": 0}}, {"Id": "Cike_DashSweapSlash_RiseSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashSweapSlash_RiseSlash"]}, "Cost": {"MP": 0}}, {"Id": "Cike_DashSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/G_DashSweapSlash_HitJump"]}, "Cost": {"MP": 0}}, {"Id": "Cike_BackJumpSweapSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_BackJumpSweapSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON>_BackJumpSweapSlash_DownSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_BackJumpSweapSlash_DownSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON>_BackJumpSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_BackJumpSweapSlash_HitJump"]}, "Cost": {"MP": 0}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "Cike_DashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapDownSlash"]}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中弹跳", "Id": "Cike_DashSweap_HitJump2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["Dagger_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_HitJump2"]}}, {"Line": "_______________________________匕首_技能C(位移、后续技能)_空中Action4_______________________________"}, {"Id": "Cike_AirDashSweapSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Cike_AirDashSweapSlash_DownSlash", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapSlash_DownSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Cike_AirDashSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "Dagger_AirInitAttack", "From": 0}, {"Tag": "Dagger_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapSlash_HitJump"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "空中前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "Cike_AirDashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Dagger_AirInitAttack", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/A_DashSweapDownSlash"]}}, {"Line": "_______________________________匕首_动作连招式_轻重击_______________________________"}, {"说明": "匕首地面普攻1", "Id": "Cike_Attack_GN1", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN1", "From": 0}, {"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "Da<PERSON>_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN2", "Dagger_Attack_GH2"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["Dagger_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN1"]}, "InitAction": true}, {"说明": "匕首地面普攻2", "Id": "Cike_Attack_GN2", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN2", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN3", "Dagger_Attack_GH3"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN2"]}, "InitAction": true}, {"说明": "匕首地面普攻3", "Id": "Cike_Attack_GN3", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN3", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN4", "Dagger_Attack_GH4"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN3"]}, "InitAction": true}, {"说明": "匕首地面普攻4", "Id": "Cike_Attack_GN4", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN4", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN5", "Dagger_Attack_GH5"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN4"]}, "InitAction": true}, {"说明": "匕首地面普攻5", "Id": "Cike_Attack_GN5", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN5", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN5"]}, "InitAction": true}, {"说明": "匕首地面普攻6", "Id": "Cike_Attack_GN6", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN6", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN7", "Dagger_Attack_GH7"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN6"]}, "InitAction": true}, {"说明": "匕首地面普攻7", "Id": "Cike_Attack_GN7", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GN7", "From": 0.0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GN7"]}, "InitAction": true}, {"说明": "匕首地面重攻1", "Id": "Cike_Attack_GH1", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH1", "From": 0}, {"Tag": "Dagger_InitAttack", "From": 0}, {"Tag": "Da<PERSON>_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻2", "Id": "Cike_Attack_GH2", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH2", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻3", "Id": "Cike_Attack_GH3", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH3", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AN1", "Dagger_Attack_AH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["Dagger_AirSkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻4", "Id": "Cike_Attack_GH4", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH4", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH4"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻5", "Id": "Cike_Attack_GH5", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH5", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AN1", "Dagger_Attack_AH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["Dagger_AirSkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH5"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻6", "Id": "Cike_Attack_GH6", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH6", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AN1", "Dagger_Attack_AH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH6"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面重攻7", "Id": "Cike_Attack_GH7", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_GN1", "Dagger_Attack_GH1"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_GH7"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面空中普攻1", "Id": "Cike_Attack_AN1", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_AN1", "From": 0}, {"Tag": "Dagger_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AN2", "Dagger_Attack_AH1", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_AN1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面空中普攻2", "Id": "Cike_Attack_AN2", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_AN2", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AN3", "Dagger_Attack_AH1", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_AN2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面空中普攻3", "Id": "Cike_Attack_AN3", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_AN3", "From": 0}], "BeCancelledTags": {"0": ["Dagger_Attack_AH1", "Dagger_AirSkillAttack", "Da<PERSON>_Air_Dodge_Step"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ci<PERSON><PERSON>_<PERSON>/Battle/Attack_AN3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "匕首地面空中重攻1", "Id": "Cike_Attack_AH1", "Cmds": [], "Tags": [{"Tag": "Dagger_Attack_AH1", "From": 0}, {"Tag": "Dagger_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["Dagger_InitAttack", "Unarm_UseItem"], "1": ["<PERSON><PERSON>_Dodge_Step", "Dagger_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON>i<PERSON><PERSON>_<PERSON>/Battle/Attack_AH1"]}, "InitAction": true, "Cost": {"MP": 0}}]}], "Buff": [], "Aoe": []}