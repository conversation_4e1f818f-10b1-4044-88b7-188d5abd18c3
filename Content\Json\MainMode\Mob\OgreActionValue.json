{"MobActionValue": [{"范例": {"Id": "一个Id，AI这里用", "RestRate": ["每个等级FightingWill选择休息动作的概率"], "Actions": [{"Id": "这个Action的Id", "MinRange": ["<float>在每个FightingWill他可能使用的最小距离"], "MaxRange": ["<float>在每个FightingWill他可能使用的最大距离"], "Rate": ["<float>在每个FightingWill可能使用的权重"], "Front": "<float>敌人位于自己正面的权重倍率", "Back": "<float>敌人位于背后的权重倍率"}], "RestActions": [{"说明": "和Actions一样结构，但是这里是发呆系的动作", "Id": "这个Action的Id", "MinRange": ["<float>在每个FightingWill他可能使用的最小距离"], "MaxRange": ["<float>在每个FightingWill他可能使用的最大距离"], "Rate": ["<float>在每个FightingWill可能使用的权重"], "Front": "<float>敌人位于自己正面的权重倍率", "Back": "<float>敌人位于背后的权重倍率"}]}}, {"Id": "Ogre", "RestRate": [50, 35, 0], "Actions": [{"Id": "Roar", "MinRange": [0, 0, 0], "MaxRange": [999999, 999999, 999999], "Rate": [0, 0, 40], "Front": 1, "Back": 1}, {"Id": "Attack_L1", "MinRange": [500, 500, 500], "MaxRange": [999999, 999999, 999999], "Rate": [0, 35, 10], "Front": 1, "Back": 0.3}, {"Id": "Attack_L2", "MinRange": [500, 500, 500], "MaxRange": [999999, 999999, 999999], "Rate": [0, 25, 10], "Front": 1, "Back": 0}, {"Id": "Attack_L1_Down", "MinRange": [500, 500, 500], "MaxRange": [999999, 999999, 999999], "Rate": [15, 35, 10], "Front": 1, "Back": 0.3}, {"Id": "Attack_L2_Down", "MinRange": [500, 500, 500], "MaxRange": [999999, 999999, 999999], "Rate": [15, 25, 10], "Front": 1, "Back": 0}, {"Id": "Attack_M1", "MinRange": [300, 300, 300], "MaxRange": [1000, 1000, 1000], "Rate": [15, 20, 5], "Front": 1, "Back": 0.3}, {"Id": "Attack_S1", "MinRange": [0, 0, 0], "MaxRange": [300, 300, 300], "Rate": [20, 30, 15], "Front": 1, "Back": 2}, {"Id": "Attack_S2", "MinRange": [0, 0, 0], "MaxRange": [400, 400, 400], "Rate": [20, 30, 15], "Front": 1, "Back": 2}, {"Id": "Attack_S5", "MinRange": [0, 0, 0], "MaxRange": [700, 700, 700], "Rate": [15, 25, 15], "Front": 1, "Back": 2}, {"Id": "Attack_S6", "MinRange": [0, 0, 0], "MaxRange": [500, 500, 500], "Rate": [5, 25, 10], "Front": 1, "Back": 0.3}, {"Id": "Attack_S7", "MinRange": [100, 100, 100], "MaxRange": [800, 800, 800], "Rate": [15, 35, 15], "Front": 1.5, "Back": 0}], "RestActions": [{"Id": "Stare03", "MinRange": [0, 0, 0], "MaxRange": [999999, 999999, 999999], "Rate": [25, 15, 5], "Front": 1, "Back": 1}, {"Id": "Stare05", "MinRange": [0, 0, 0], "MaxRange": [999999, 999999, 999999], "Rate": [25, 15, 5], "Front": 1, "Back": 1}]}]}