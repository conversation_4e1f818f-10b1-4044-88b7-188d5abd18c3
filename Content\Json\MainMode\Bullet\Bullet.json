{"Bullet": [{"Id": "Test01", "Tag": ["Fire"], "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 5, "HitDelay": 0.1, "Life": 5, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit()"], "OnRemoved": []}, {"Id": "Claw", "Tag": ["Claw"], "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.TryCatchOnTarget(Asshole)"], "OnRemoved": []}, {"Id": "Test_Fireball", "Tag": ["<PERSON><PERSON>", "Magic"], "BpPath": "Core/Item/Bullet/FireballScroll_Bullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.FireBallHit(1000)"], "OnRemoved": ["BulletScript.FireBallRemoved()"]}, {"Id": "Bullet_SwordLight", "Tag": ["<PERSON><PERSON>", "Magic"], "BpPath": "Core/Item/Bullet/PlayerSkill/Bullet_SwordLight", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.FireBallHit(1000)"], "OnRemoved": ["BulletScript.FireBallRemoved()"]}, {"Id": "FallingPillar", "Tag": [], "BpPath": "Core/Item/Monster/Ogre/FallingPillar", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(PillarExplosion,0.4,true)", "OgreBulletScript.DestroyAllPillar()", "BulletScript.CreateVFXOnHit(Temp/Effect/ParagonZ/FX_Rampage/Particles/Abilities/Lunge/FX/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactGround_Cue)"], "OnRemoved": ["BulletScript.CreateAoE(PillarExplosion,0.4,true)", "BulletScript.CreateSFXOnRemoved(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactCharacter_Cue)"]}, {"Id": "OgrePillar", "Tag": [], "BpPath": "Core/Item/Bullet/Ogre_PillarBullet", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(PillarExplosion,0.2,true)", "BulletScript.CreateVFXOnHit(Temp/Effect/ParagonZ/FX_Rampage/Particles/Abilities/Lunge/FX/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactGround_Cue)", "OgreBulletScript.CreateExplodePillarOnHit()"], "OnRemoved": ["BulletScript.CreateAoE(PillarExplosion,0.2,true)", "BulletScript.CreateVFXOnRemoved(Temp/Effect/ParagonZ/FX_Rampage/Particles/Abilities/Lunge/FX/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnRemoved(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactCharacter_Cue)", "OgreBulletScript.CreateExplodePillarOnRemoved()"]}, {"描述": "玩家丢出来的奶酪", "Id": "ThrownCheese", "Tag": [], "BpPath": "Core/Item/Bullet/Cheese", "SightEffect": "", "CanHitFoe": false, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 1, "Type": "Bullet", "OnCreate": [], "OnHit": [], "OnRemoved": ["BulletScript.CreateAoE(<PERSON><PERSON>,60,true)"]}, {"描述": "冰恶魔丢出来的镰刀", "Id": "IceScythe", "Tag": [], "BpPath": "Core/Item/Monster/IceDevil/Bullet_IceScythe", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": ["IceDevilBulletScript.SetBulletOriginRotation(-2)"], "OnHit": ["IceDevilBulletScript.DealDamageOnHit()", "BulletScript.CreateVFXOnHit(Temp/Effect/ParagonZ/FX_Aurora/Particles/Abilities/Ultimate/FX/P_Aurora_Ultimate_Explode_Small)"], "OnRemoved": ["BulletScript.CreateVFXOnRemoved()", "IceDevilBulletScript.CreateIceScytheSceneItem(Game/Temp/Effect/ParagonZ/FX_Aurora/Particles/Abilities/Leap/FX/P_Aurora_Decoy_Explode_Fragments,Temp/Effect/ParagonZ/FX_Aurora/Particles/Abilities/Ultimate/FX/P_Aurora_Ultimate_Explode_Small)"]}, {"Id": "Arrow", "Tag": [], "BpPath": "Core/Item/Bullet/Arrow", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(50)"], "OnRemoved": []}, {"Id": "CannonBullet", "Tag": [], "BpPath": "Core/Item/Bullet/CannonBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}]}