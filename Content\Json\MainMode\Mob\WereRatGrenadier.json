{"Mob": [{"Id": "WereRatGrenadier", "Tag": ["WereRat"], "AI": ["StopAI", "WereRat_ClearRoaredBuff", "WereRat_Debut", "NormalPickUpCheese", "MoveToClosetCheese", "WereRatGrenadier_ThrowThreeBombs", "WereRatGrenadier_ThrowOneBomb", "WereRatGrenadier_ThrowAround", "WereRatGrenadier_DodgeBack", "WereRat_DodgeLeftOrRight", "WereRat_Roar", "WereRatGrenadier_Crawling", "WereRatGrenadier_Pace", "WereRatTurnToStimulate"], "BpPath": "Core/Characters/WereRatGrenadier/WereRat_Grenadier", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -30}], "OnBeKilled": ["MobBeKilled.RatManBeKilled()", "DesignerScript/TriggerScript_Country.CheckAllRatsDeathInMission04()"], "Flyable": false, "ExpGiven": 1, "LootPackageId": "WereRat", "MountsTypeRotate": false, "Name": "鼠人掷弹兵", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 1200, "SightHalfAngleDregee": 135, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 3, "Attack": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [{"Id": "WereRat_Grenadier_Head", "Rate": 1}, {"Id": "WereRat_Grenadier_Arm", "Rate": 1}, {"Id": "WereRat_Grenadier_Body", "Rate": 1}], "Part": [{"Meat": {"Physical": 0.8}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1.0}, "Part": "Tail", "Durability": [1], "CanBeDestroy": false, "Priority": 7, "StableMod": 1.0, "Type": "Meat"}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["Move", "NormalAction", "InitAttack", "SpecialHurt"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/WereRatCommando/Move"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Dead_Blow_Back"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up"]}}, {"说明": "被自己炸弹眩晕+着急跺脚(待Montage)", "Id": "HurtByAllyBomb", "Cmds": ["HurtByAllyBomb"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": [""]}, "InitAction": true}, {"说明": "登场", "Id": "Debut", "Cmds": ["Debut"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRat/Action/Debut"]}, "InitAction": true}, {"说明": "向前跑（短）", "Id": "Crawling_Short", "Cmds": ["Crawling_Short"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Short"]}, "InitAction": true}, {"说明": "向前跑（长）", "Id": "Crawling_Long", "Cmds": ["Crawling_Long"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Long"]}, "InitAction": true}, {"说明": "捡起矿石收起(待Monatge)", "Id": "CollectOre", "Cmds": ["CollectOre"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Mine_Short"]}, "InitAction": true}, {"说明": "捡起矿石丢到(待Monatge)", "Id": "DiscardOre", "Cmds": ["DiscardOre"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Mine_Long"]}, "InitAction": true}, {"说明": "扔一个炸弹", "Id": "ThrowOneBomb", "Cmds": ["ThrowOneBomb"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["SpecialHurt"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/WereRatGrenadier/ThrowAttack"]}, "InitAction": true}, {"说明": "扔三个炸弹", "Id": "ThrowThreeBombs", "Cmds": ["ThrowThreeBombs"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["SpecialHurt"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/WereRatGrenadier/TwiceThrowAttack"]}, "InitAction": true}, {"说明": "自爆(待Monatge)", "Id": "SelfExplosion", "Cmds": ["SelfExplosion"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["SpecialHurt"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/WereRatGrenadier/ExplodeSelfAttack"]}, "InitAction": true}, {"说明": "往四周扔东西", "Id": "ThrowAround", "Cmds": ["ThrowAround"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["SpecialHurt"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/WereRatGrenadier/CrazyThrowAttack"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Daze1", "Cmds": ["Daze1"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_1"]}, "InitAction": true}, {"说明": "发呆2", "Id": "Daze2", "Cmds": ["Daze2"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_2"]}, "InitAction": true}, {"说明": "发呆3", "Id": "Daze3", "Cmds": ["Daze3"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_3"]}, "InitAction": true}, {"说明": "转向刺激源动作", "Id": "TurnToStimulate", "Cmds": ["TurnToStimulate"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LookAround"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Roar"]}, "InitAction": true}, {"说明": "左踱步", "Id": "LeftPace", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LeftPace"]}, "InitAction": true}, {"说明": "右踱步", "Id": "RightPace", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/RightPace"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_Back"]}, "InitAction": true}, {"说明": "向左闪避", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_Left"]}, "InitAction": true}, {"说明": "向右闪避", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_Right"]}, "InitAction": true}, {"说明": "生气（待替换）", "Id": "WereRatAngry", "Cmds": ["WereRatAngry"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Angry"]}}, {"说明": "发呆：喘气", "Id": "WereRat_DeepBreathe", "Cmds": ["WereRat_DeepBreathe"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/DeepBreathe"]}}, {"说明": "发呆：害怕", "Id": "WereRat_Fear", "Cmds": ["WereRat_Fear"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Fear"]}}, {"说明": "发呆：累了", "Id": "WereRat_Tired", "Cmds": ["WereRat_Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Tired"]}}]}], "AOE": [{"Id": "WereRatGrenadier_Bomb_OnGround", "Tag": ["Explosion"], "TickTime": 0, "BpPath": "Core/Item/Monster/WereRat/GroundGrenade", "OnTick": [], "OnRemoved": ["AOEScript.CreateAoeOnRemoved(WereRatGrenadier_Bomb_Explosion,0.3)"], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "WereRatGrenadier_Bomb_Explosion", "Tag": ["Explosion"], "TickTime": 0, "BpPath": "Core/Item/Monster/WereRat/GrenadeExplosion", "OnCreate": [], "OnCharacterEnter": ["WereRat_AOEScript.DealDamageAndStunWereRat(20)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DestroySceneItem()"], "OnActorLeave": []}], "Bullet": [{"Id": "WereRatGrenadier_Throw_Bomb", "Tag": ["Fire"], "BpPath": "Core/Item/Monster/WereRat/ThrowingGrenade", "CanHitFoe": false, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.CreateAOEOnHit(WereRatGrenadier_Bomb_Explosion,0.3)"], "OnRemoved": ["BulletScript.CreateAoE(WereRatGrenadier_Bomb_Explosion,0.3)"]}], "Buff": [{"Id": "<PERSON>man", "Tag": ["TEST"], "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(BerserkEye,Temp/ArtRes/Environment/Scene/FantasyDungeon/FX/Fire1,Eye)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(BerserkEye)"]}, {"Id": "ThrowBombCD", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 2, "OnOccur": [], "OnRemoved": []}]}