{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个冰霜", "Id": "Anim_IceHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Ice,1,8,true,false)"]}, {"说明": "动画中引发冻结", "Id": "Anim_FrozenHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,Rogue_IceCastFrozen,1,5,true,false,)"]}, {"说明": "动画中引发无消耗冻结", "Id": "<PERSON><PERSON>_FrozenHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,Rogue_IceCastFrozenNoCost,1,0.01,true,false,)"]}, {"说明": "动画中概率引发冻结", "Id": "Anim_ChanceFrozenHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(0.5,Rogue_IceCastFrozen,1,5,true,false,)"]}, {"说明": "动画中引发冰霜旋涡", "Id": "Anim_JustDodgeIceSpiral", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_IceArea,2,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeIceSpiral,1,0,true)"]}, {"说明": "动画中挨打概率持续给冰霜", "Id": "Anim_<PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,<PERSON>_<PERSON>,1,5,true,false)"]}, {"说明": "动画中挨打引发特殊冰霜旋涡", "Id": "Anim_<PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnBeHurtSocket(Rogue_Aoe_Ilm8,4,root)", "BuffUtils.RemoveSelfBuffStackOnHit(An<PERSON>_<PERSON>,1,0,true)"]}, {"分割": "-------------------------------------------Ice-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个冰霜", "Id": "IceHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_IceHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_IceHit,1,0,true)"]}, {"说明": "造成伤害时引爆冰霜给对方一个冻结", "Id": "FrozenHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceFrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceFrozenHit,1,0,true)"]}, {"说明": "击中的第一个目标产生一次冰爆", "Id": "IceBurst", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["BuffUtils.CreateAoeOnHitPos(Rogue_IceBurst,1)", "BuffUtils.RemoveSelfBuffStackOnHit(IceBurst,1,0,true)"]}, {"说明": "下砸时候对击中的第一个目标产生一次冰爆", "Id": "Smash_IceBurst", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(IceBurst,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(IceBurst,1,0,true)"]}, {"说明": "下砸时对击中的目标产生一次冻结", "Id": "Smash_FrozenHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "位移技能时造成伤害则触发冰霜转换", "Id": "Dash_IceCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "蓄力时被打,反手给个冰霜", "Id": "Power_BeHurtCastIce", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(An<PERSON>_<PERSON>,1,0,true)", "BuffUtils.AddBuffObj(An<PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_<PERSON>,1,0,true)", "BuffUtils.RemoveSubBuffObj(Anim_<PERSON>ce<PERSON>,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次冻结", "Id": "Power_FrozenHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个冰霜领域的AOE持续一段时间", "Id": "IceDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Ilm8,3,<PERSON>)"]}, {"说明": "动画中完美闪避生成冰霜Aoe", "Id": "An<PERSON>_DogeCreateIceSpiral", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_IceArea,2,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeIceSpiral,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeIceSpiral,1,0,true)"]}, {"说明": "击飞技能时对击中的目标产生一次冻结", "Id": "Rise_FrozenHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "造成冰元素伤害的时候概率对冰元素的主动道具进行额外充能", "Id": "AddItemRecover_Ice", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Ice)"]}, {"说明": "每过TickTime秒发射一个冰匕首", "Id": "Launch_IceTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Ilm17,0,0.3,pelvis)"]}, {"说明": "受击时产生一个冰盾,CDxxx秒", "Id": "Hurt_CreateIceShield", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.CreateAoeOnBeHurtSocketByCoolDown(Rogue_Aoe_Ilm18,3,pelvis,5,true)"]}]}