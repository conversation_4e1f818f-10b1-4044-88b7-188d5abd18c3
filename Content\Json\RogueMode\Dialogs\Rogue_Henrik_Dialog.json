{"Dialogs": [{"说明": "<PERSON>_<PERSON>初次对话", "Id": "<PERSON>_<PERSON>_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_VeryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_VeryFirstDialog2"}], "Selections": [{"说明": "<PERSON>_<PERSON>故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueHenrikStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_Henrik初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialog7"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON>_<PERSON>_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(<PERSON>_VeryF<PERSON>t_Dialog,1)"]}, {"说明": "Rogue_Henrik通关后初次对话", "Id": "Rogue_Henrik_WeeklyRoundFirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_WeeklyRound1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_WeeklyRound2"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Henrik_WeeklyRound_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Henrik默认开始对话", "Id": "Rogue_<PERSON>_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstDialog3"}], "Selections": [{"说明": "<PERSON>_<PERSON>故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueHenrikStoryDialogPicker()", "Actions": []}, {"说明": "<PERSON>_<PERSON>解锁职业", "特别说明": "为了实现解锁职业跳出另一个UI，然后解锁失败返回对话，解锁成功自动进入下一段对话，这里的NextEventFunc先为空，成功的NextEventFunc在解锁成功的UI蓝图里手动调用", "Conditions": ["DialogCondition.CheckCareerLock(BladeDancer_Henrik)"], "EnableChecks": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionUnlockClass", "NextEventFunc": "", "Actions": ["DialogAction.ShowUnlockCareerUI(BladeDancer_Henrik,100)"], "StillInDialog": true}, {"说明": "<PERSON>_<PERSON>切换职业", "Conditions": ["DialogCondition.CheckCareerUnlock(BladeDancer_Henrik)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionChangeClass", "NextEventFunc": "DialogAction.DirectGoTo(ChangeClass)", "Actions": []}, {"说明": "<PERSON>_<PERSON>结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "DialogAction.DirectGoTo(EndDialog)", "Actions": []}]}, {"说明": "Rogue_Henrik默认的重复对话", "Id": "DefaultStoryDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik<PERSON>Dialog"}], "Selections": []}, {"说明": "Rogue_Henrik初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialogRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialogRepeat2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_StoryFirstDialogRepeat3"}], "Selections": []}, {"说明": "Rogue_Henrik 别的角色死亡后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_AfterDeathDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_AfterDeathDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_AfterDeathDialog3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON><PERSON>_StoryDialog,1)"]}, {"说明": "Rogue_Henrik 别的角色死亡后的重复对话", "Id": "AfterDeathDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_AfterDeathDialogRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_AfterDeathDialogRepeat2"}], "Selections": []}, {"说明": "Rogue_Henrik 见到兽人碎骨者后的故事对话", "Id": "ArriveLevel30Dialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_5"}, {"Type": "Speak", "Id": "Step5", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_6"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Soul,100)", "DialogAction.SetRogueSwitch(Henrik_ArriveLevel30_StoryDialog,1)"]}, {"说明": "Rogue_Henrik 见到兽人碎骨者后的重复对话", "Id": "ArriveLevel30DialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_6"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ArriveLevel30_4"}], "Selections": []}, {"说明": "Rogue_Henrik 通关后的重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_WeeklyRoundRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 拿到1个圣杯碎片的对话", "Id": "FirstGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFirstGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFirstGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel01_Dialog,1)"]}, {"说明": "Rogue_Henrik 拿到1个圣杯碎片的重复对话", "Id": "FirstGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFirstGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 得知“圣杯誓言石”后的对话", "Id": "GetGrailInfoDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainGrailOathStone1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainGrailOathStone2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainGrailOathStone3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GetGrailInfo_Dialog,1)"]}, {"说明": "Rogue_Henrik 得知“圣杯誓言石”后的重复对话", "Id": "GetGrailInfoDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainGrailOathStoneRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 拿到2个圣杯碎片的对话", "Id": "SecondGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainSecondGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel02_Dialog,1)"]}, {"说明": "Rogue_Henrik 拿到2个圣杯碎片的重复对话", "Id": "SecondGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 拿到3个圣杯碎片的对话", "Id": "ThirdGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainThirdGrail1"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel03_Dialog,1)"]}, {"说明": "Rogue_Henrik 拿到3个圣杯碎片的重复对话", "Id": "ThirdGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainThirdGrail1Repeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 拿到4个圣杯碎片的对话", "Id": "ForthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_O<PERSON>ainForthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainForthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainForthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel04_Dialog,1)"]}, {"说明": "Rogue_Henrik 拿到4个圣杯碎片的重复对话", "Id": "ForthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_O<PERSON>ainForthGrailRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_O<PERSON>ainForthGrailRepeat2"}], "Selections": []}, {"说明": "Rogue_Henrik 拿到5个圣杯碎片的对话", "Id": "FifthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFifthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFifthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFifthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel05_Dialog,1)"]}, {"说明": "Rogue_Henrik 拿到5个圣杯碎片的重复对话", "Id": "FifthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_ObtainFifthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 解除圣杯封印的对话", "Id": "UnlockGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_UnlockGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_UnlockGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_GrailLevel06_Dialog,1)"]}, {"说明": "Rogue_Henrik 解除圣杯封印的重复对话", "Id": "UnlockGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_UnlockGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_<PERSON> 击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>KillRealDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik<PERSON>KillRealDeathLord2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Henrik_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Henrik 击败真·真死骸骑士的重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_KillRealDeathLordRepeat1"}], "Selections": []}, {"说明": "Rogue_Henrik 送魂之残响", "Id": "GiveEcho", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho2"}, {"Type": "GiveCurrencyToNPC", "Id": "Step2", "NextId": "", "Params": ["Rogue_Soul", "5", "GiveEchoSuccess", "GiveEchoFailure"]}], "Selections": []}, {"说明": "Rogue_Henrik 送魂之残响成功", "Id": "GiveEchoSuccess", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho4"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho5"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho6"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho7"}, {"Type": "Speak", "Id": "Step4", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho8"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON>,1)"]}, {"说明": "Rogue_Henrik 送魂之残响失败", "Id": "GiveEchoFailure", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_FirstGiveEcho10"}], "Selections": []}, {"说明": "Rogue_Henrik解锁职业成功并自动切换职业", "Id": "UnlockClassSuccess", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_SecondGiveEcho1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_SecondGiveEcho2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_SecondGiveEcho3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_SecondGiveEcho4"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 3}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 1}], "EndClipScript": ["DialogAction.RogueChangePawn(BladeDancer_Henrik)"]}, {"说明": "<PERSON>_<PERSON>切换职业", "Id": "ChangeClass", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Henrik_SwitchCharacter1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 3}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 1}], "EndClipScript": ["DialogAction.RogueChangePawn(BladeDancer_Henrik)"]}, {"说明": "<PERSON>_<PERSON>结束对话", "Id": "EndDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>"}], "Selections": []}], "EndDialogScript": []}]}