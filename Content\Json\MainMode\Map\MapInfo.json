{"MapInfo": [{"Id": "TestBattle", "LevelPath": "/Game/Maps/WhiteBox/Map_Battle", "Points": [{"Id": "EscapePoint", "Offset": "X=100,Y=500,Z=100"}], "OnCreate": [{"Id": "TestCreateOgre", "Condition": ["MapScript.TestMapCondition()"], "Action": ["MapScript.TestMapOnCreate(10)"]}], "OnRemoved": []}, {"Id": "TestCountry", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Country", "Points": [{"Id": "PillarSpawnPoint01", "Offset": "X=-980,Y=-1420,Z=150"}, {"Id": "PillarSpawnPoint02", "Offset": "X=2020,Y=-360,Z=150"}, {"Id": "PillarSpawnPoint03", "Offset": "X=1360,Y=-2060,Z=150"}], "OnCreate": [], "OnRemoved": []}, {"Id": "<PERSON><PERSON><PERSON>", "LevelPath": "/Game/Maps/Debug/MobTest", "Points": [{"Id": "PillarSpawnPoint01", "Offset": "X=0,Y=-2333,Z=150"}, {"Id": "PillarSpawnPoint02", "Offset": "X=0,Y=2343,Z=150"}, {"Id": "PillarSpawnPoint03", "Offset": "X=-1551,Y=-1671,Z=150"}, {"Id": "PillarSpawnPoint04", "Offset": "X=-1682,Y=1590,Z=150"}, {"Id": "PillarSpawnPoint05", "Offset": "X=-2273,Y=0,Z=150"}, {"Id": "CenterPoint", "Offset": "X=0,Y=0,Z=200"}], "PathNodes": [{"Id": "Test01", "Nodes": ["Point0", "Point1", "Point2", "Point3"], "Loop": true}, {"Id": "Mine01", "Nodes": ["MinePoint0", "MinePoint1"], "Loop": false}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestRouge", "LevelPath": "/Game/Maps/WhiteBox/WhiteBox_Rouge", "Points": [], "PathNodes": [{"Id": "Door01", "Nodes": ["Door01_01", "Door01_02", "Door01_03", "Door01_04", "Door01_05", "Door01_06"], "Loop": false}, {"Id": "Door02", "Nodes": ["Door02_01", "Door02_02", "Door02_03", "Door02_04", "Door02_05", "Door01_04", "Door01_05", "Door01_06"], "Loop": false}, {"Id": "Door03", "Nodes": ["Door03_01", "Door03_02", "Door03_03", "Door03_04", "Door03_05", "Door03_06"], "Loop": false}, {"Id": "Door04", "Nodes": ["Door04_01", "Door04_02", "Door04_03", "Door04_04", "Door04_05", "Door03_04", "Door03_05", "Door03_06"], "Loop": false}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeon", "LevelPath": "/Game/Maps/MainMapWhiteBox/TestRandomDungeon", "Points": [], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonRoom01", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_01", "Points": [{"Id": "MobSpawnPoint01", "Offset": "X=1850,Y=240,Z=427"}, {"Id": "MobSpawnPoint02", "Offset": "X=-706,Y=-2653,Z=745"}, {"Id": "MobSpawnPoint03", "Offset": "X=-1464,Y=1624,Z=1158"}, {"Id": "PlayerSpawnPoint", "Offset": "X=390,Y=900,Z=530"}], "OnCreate": [{"Id": "SpawnBlockRock", "Condition": ["TriggerScript.CheckNoSwitchInRole(UnlockMineGoblin)"], "Action": ["MapScript.SpawnBlockGoblinRock(BlockGoblinDoor,BlockGoblinRock)"]}], "OnRemoved": []}, {"Id": "TestDungeonRoom02", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_02", "Points": [{"Id": "MobSpawnPoint01", "Offset": "X=3382,Y=883,Z=717"}, {"Id": "MobSpawnPoint02", "Offset": "X=2519,Y=-1802,Z=376"}, {"Id": "MobSpawnPoint03", "Offset": "X=-4836,Y=-4173,Z=-682"}, {"Id": "PlayerSpawnPoint", "Offset": "X=-680,Y=-90,Z=580"}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonRoom03", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_03", "Points": [{"Id": "MobSpawnPoint01", "Offset": "X=457,Y=2922,Z=186"}, {"Id": "MobSpawnPoint02", "Offset": "X=-2708,Y=750,Z=626"}, {"Id": "MobSpawnPoint03", "Offset": "X=-161,Y=-4301,Z=1189"}, {"Id": "PlayerSpawnPoint", "Offset": "X=1270,Y=-80,Z=250"}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonRoom04", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_04", "Points": [{"Id": "MobSpawnPoint01", "Offset": "X=-1795,Y=-1284,Z=250"}, {"Id": "MobSpawnPoint02", "Offset": "X=3004,Y=-1301,Z=417"}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonRoom05", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_05", "Points": [{"Id": "PillarSpawnPoint01", "Offset": "X=-980,Y=-1420,Z=570"}, {"Id": "PillarSpawnPoint02", "Offset": "X=2620,Y=-360,Z=570"}, {"Id": "PillarSpawnPoint03", "Offset": "X=1360,Y=-2060,Z=570"}, {"Id": "PillarSpawnPoint04", "Offset": "X=140,Y=-560,Z=570"}, {"Id": "PillarSpawnPoint05", "Offset": "X=-1220,Y=-480,Z=570"}, {"Id": "PillarSpawnPoint06", "Offset": "X=490,Y=200,Z=570"}], "OnCreate": [{"Id": "SpawnBlockOgreRock", "Condition": ["TriggerScript.CheckNoSwitchInRole(MineStoryStep)"], "Action": ["MapScript.SpawnBlockOgreRock(BlockOgreRock)"]}, {"Id": "SpawnBlockIceDevilRock", "Condition": ["TriggerScript.CheckSwitchInRoleLess(MineStoryStep,4)"], "Action": ["MapScript.SpawnBlockIceDevilRock(BlockIceDevilRock)"]}], "OnRemoved": []}, {"Id": "TestDungeonRoom06", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_06", "Points": [{"Id": "CenterPoint", "Offset": "X=920,Y=35,Z=700"}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonRoom07", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_07", "Points": [{"Id": "BlockRocklSpawnPoint01", "Offset": "X=1850,Y=240,Z=427"}, {"Id": "BlockRocklSpawnPoint02", "Offset": "X=-706,Y=-2653,Z=745"}, {"Id": "BlockRocklSpawnPoint03", "Offset": "X=-1464,Y=1624,Z=1158"}, {"Id": "BlockRocklSpawnPoint04", "Offset": "X=390,Y=900,Z=530"}], "OnCreate": [{"Id": "SpawnBlockRock", "Condition": ["TriggerScript.CheckNoSwitchInRole(UnlockMineGoblin)"], "Action": ["MapScript.SpawnBlockGoblinRock(BlockGoblinDoor,BlockGoblinRock)"]}], "OnRemoved": []}, {"Id": "TestDungeonRoom09", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_09", "Points": [], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonEntrance01", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_Entrance_01", "Points": [{"Id": "PlayerSpawnPoint", "Offset": "X=0,Y=-410.0,Z=300"}], "OnCreate": [], "OnRemoved": []}, {"Id": "TestDungeonEntrance02", "LevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_Entrance_02", "Points": [], "OnCreate": [], "OnRemoved": []}, {"Id": "Village_Main", "LevelPath": "/Game/Maps/Village/Village_Main"}, {"Id": "GameTitle", "LevelPath": "/Game/Maps/Main/GameTitle"}, {"Id": "RodianTown_01", "LevelPath": "/Game/Maps/RodianTown/RodianTown_01", "Points": [], "PathNodes": [{"Id": "Adjutant_0", "Nodes": ["Adjutant0_0", "Adjutant0_1", "Adjutant0_2", "Adjutant0_3", "Adjutant0_4", "Adjutant0_5", "Adjutant0_6", "Adjutant0_7", "Adjutant0_8", "Adjutant0_9"], "Loop": false}, {"Id": "Captain_0", "Nodes": ["Captain0_0", "Captain0_1"], "Loop": false}, {"Id": "Captain_1", "Nodes": ["Captain1_0", "Captain1_1", "Captain1_2", "Captain1_3", "Captain1_4", "Captain1_5"], "Loop": false}, {"Id": "Guard_0", "Nodes": ["Guard0_0", "Guard0_1", "Guard0_2"], "Loop": false}, {"Id": "Guard_1", "Nodes": ["Guard1_0", "Guard1_1", "Guard1_2"], "Loop": false}, {"Id": "Guard_2", "Nodes": ["Guard2_0", "Guard2_1", "Guard2_2"], "Loop": false}, {"Id": "Guard_3", "Nodes": ["Guard3_0", "Guard3_1", "Guard3_2", "Guard3_3", "Guard3_4"], "Loop": false}], "OnCreate": [], "OnRemoved": []}, {"Id": "Mission3", "LevelPath": "/Game/Maps/Mission3/LA_Mission3", "Points": [], "PathNodes": [], "OnCreate": [], "OnRemoved": []}, {"Id": "Mission4", "LevelPath": "/Game/Maps/Mission4/LA_Mission4", "Points": [], "PathNodes": [{"Id": "Guard_0", "Nodes": ["Guard0_0", "Guard0_1", "Guard0_2", "Guard0_3", "Guard0_4", "Guard0_5", "Guard0_6", "Guard0_7", "Guard0_8", "Guard0_9", "Guard0_10", "Guard0_11", "Guard0_12", "Guard0_13"], "Loop": true}, {"Id": "GuardCombat1-0", "Nodes": ["GuardCombat1_0"], "Loop": false}, {"Id": "GuardCombat1-1", "Nodes": ["GuardCombat1_1"], "Loop": false}, {"Id": "GuardCombat1-2", "Nodes": ["GuardCombat1_2"], "Loop": false}, {"Id": "GuardCombat1-3", "Nodes": ["GuardCombat1_3"], "Loop": false}, {"Id": "GuardCombat1-4", "Nodes": ["GuardCombat1_4"], "Loop": false}, {"Id": "GuardCombat1-5", "Nodes": ["GuardCombat1_5"], "Loop": false}, {"Id": "GuardCombat1-6", "Nodes": ["GuardCombat1_6"], "Loop": false}, {"Id": "GuardCombat1-7", "Nodes": ["GuardCombat1_7"], "Loop": false}, {"Id": "GuardCombat1-8", "Nodes": ["GuardCombat1_8"], "Loop": false}, {"Id": "GuardCombat1-9", "Nodes": ["GuardCombat1_9"], "Loop": false}, {"Id": "GuardCombat2-0", "Nodes": ["GuardCombat2_0"], "Loop": false}, {"Id": "GuardCombat2-1", "Nodes": ["GuardCombat2_1"], "Loop": false}, {"Id": "GuardCombat2-2", "Nodes": ["GuardCombat2_2"], "Loop": false}, {"Id": "GuardCombat2-3", "Nodes": ["GuardCombat2_3"], "Loop": false}, {"Id": "GuardCombat2-4", "Nodes": ["GuardCombat2_4"], "Loop": false}, {"Id": "GuardCombat2-5", "Nodes": ["GuardCombat2_5"], "Loop": false}, {"Id": "GuardCombat2-6", "Nodes": ["GuardCombat2_6"], "Loop": false}, {"Id": "GuardCombat2-7", "Nodes": ["GuardCombat2_7"], "Loop": false}, {"Id": "GuardCombat2-8", "Nodes": ["GuardCombat2_8"], "Loop": false}, {"Id": "GuardCombat2-9", "Nodes": ["GuardCombat2_9"], "Loop": false}, {"Id": "GuardCombat3-0", "Nodes": ["GuardCombat3_0_0", "GuardCombat3_0_1", "GuardCombat3_0_2", "GuardCombat3_0_3", "GuardCombat3_0_4", "GuardCombat3_0_5"], "Loop": false}, {"Id": "GuardCombat3-1", "Nodes": ["GuardCombat3_1_0", "GuardCombat3_1_1", "GuardCombat3_1_2", "GuardCombat3_1_3", "GuardCombat3_1_4", "GuardCombat3_1_5"], "Loop": false}, {"Id": "GuardCombat3-2", "Nodes": ["GuardCombat3_2_0", "GuardCombat3_2_1", "GuardCombat3_0_2", "GuardCombat3_1_3", "GuardCombat3_0_4", "GuardCombat3_2_5"], "Loop": false}, {"Id": "GuardCombat3-3", "Nodes": ["GuardCombat3_3_0", "GuardCombat3_0_1", "GuardCombat3_1_2", "GuardCombat3_1_3", "GuardCombat3_1_4", "GuardCombat3_3_5"], "Loop": false}, {"Id": "GuardCombat3-4", "Nodes": ["GuardCombat3_0_0", "GuardCombat3_0_1", "GuardCombat3_0_2", "GuardCombat3_0_3", "GuardCombat3_0_4", "GuardCombat3_4_5"], "Loop": false}, {"Id": "GuardCombat3-5", "Nodes": ["GuardCombat3_1_0", "GuardCombat3_2_1", "GuardCombat3_0_2", "GuardCombat3_0_3", "GuardCombat3_1_4", "GuardCombat3_5_5"], "Loop": false}, {"Id": "GuardCombat3-6", "Nodes": ["GuardCombat3_2_0", "GuardCombat3_1_1", "GuardCombat3_1_2", "GuardCombat3_1_3", "GuardCombat3_0_4", "GuardCombat3_6_5"], "Loop": false}, {"Id": "GuardCombat3-7", "Nodes": ["GuardCombat3_3_0", "GuardCombat3_0_1", "GuardCombat3_0_2", "GuardCombat3_0_3", "GuardCombat3_1_4", "GuardCombat3_7_5"], "Loop": false}, {"Id": "GuardCombat3-8", "Nodes": ["GuardCombat3_2_0", "GuardCombat3_2_1", "GuardCombat3_1_2", "GuardCombat3_1_3", "GuardCombat3_0_4", "GuardCombat3_8_5"], "Loop": false}, {"Id": "GuardCombat3-9", "Nodes": ["GuardCombat3_1_0", "GuardCombat3_1_1", "GuardCombat3_0_2", "GuardCombat3_0_3", "GuardCombat3_1_4", "GuardCombat3_9_5"], "Loop": false}, {"Id": "GuardCombat4-0", "Nodes": ["GuardCombat4_0"], "Loop": false}, {"Id": "GuardCombat4-1", "Nodes": ["GuardCombat4_1"], "Loop": false}, {"Id": "GuardCombat4-2", "Nodes": ["GuardCombat4_2"], "Loop": false}, {"Id": "GuardCombat4-3", "Nodes": ["GuardCombat4_3"], "Loop": false}, {"Id": "GuardCombat4-4", "Nodes": ["GuardCombat4_4"], "Loop": false}, {"Id": "GuardCombat4-5", "Nodes": ["GuardCombat4_5"], "Loop": false}, {"Id": "GuardCombat4-6", "Nodes": ["GuardCombat4_6"], "Loop": false}, {"Id": "GuardCombat4-7", "Nodes": ["GuardCombat4_7"], "Loop": false}, {"Id": "GuardCombat4-8", "Nodes": ["GuardCombat4_8"], "Loop": false}, {"Id": "GuardCombat4-9", "Nodes": ["GuardCombat4_9"], "Loop": false}, {"Id": "Captain0", "Nodes": ["Captain0_0", "Captain0_1", "Captain0_2"], "Loop": false}, {"Id": "OgreFinishGuard0", "Nodes": ["OgreFinishGuard0_0", "OgreFinishGuard0_1", "OgreFinishGuard0_2"], "Loop": false}, {"Id": "OgreFinishGuard1", "Nodes": ["OgreFinishGuard1_0", "OgreFinishGuard1_1"], "Loop": false}], "OnCreate": [], "OnRemoved": []}, {"Id": "Mission5", "LevelPath": "/Game/Maps/Mission5/LA_Mission5", "Points": [], "PathNodes": [], "OnCreate": [], "OnRemoved": []}]}