{"说明": "3级动作相关的buff", "Buff": [{"说明": "击飞技能施放时在面前产生一个Aoe", "Id": "Rise_FireAoe", "Tag": ["ActionLv3", "FXRelic_Below_Fire", "FXRelic_NAtk_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni13,1,Root)"]}, {"说明": "位移终点生成一个火焰Aoe", "Id": "Dash_FireAoe", "Tag": ["ActionLv3", "FXRelic_Dash_Fire", "FXRelic_NAtk_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni6,1,Weapon_rSocket)"]}, {"说明": "下砸落地的时候产生一个Aoe", "Id": "Smash_GroundFireAoe", "Tag": ["ActionLv3", "FXRelic_Smash_Fire", "FXRelic_NAtk_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni4,3,root)"]}, {"说明": "击飞技能施放时在面前产生一个冰Aoe", "Id": "Rise_IceAoe", "Tag": ["ActionLv3", "FXRelic_Below_Ice", "FXRelic_NAtk_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Ilm13,4,Root)"]}, {"说明": "位移终点生成一个冰霜Aoe", "Id": "Dash_IceAoe", "Tag": ["ActionLv3", "FXRelic_Dash_Ice", "FXRelic_NAtk_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Ilm6,0.5,Weapon_rSocket)"]}, {"说明": "下砸时候产生一个即将坠落的冰晶", "Id": "Smash_IceAoe", "Tag": ["ActionLv3", "FXRelic_Smash_Ice", "FXRelic_NAtk_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_Bullet_Ilm4,20,NONE)"]}, {"说明": "击飞技能时，在周围卷起一阵旋风，掀飞被击中的敌人。", "Id": "Rise_WindAoe", "Tag": ["ActionLv3", "FXRelic_Below_Wind", "FXRelic_NAtk_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia13,0.5,Root)"]}, {"说明": "位移技能时，在位移的起点，产生一个气流弹，掀飞被击中的敌人。", "Id": "Dash_WindBullet", "Tag": ["ActionLv3", "FXRelic_Dash_Wind", "FXRelic_NAtk_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_Bullet_Zantia6,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"]}, {"说明": "下砸时候,周围卷起一阵旋风Aoe", "Id": "Smash_WindAoe1", "Tag": ["ActionLv3", "FXRelic_Smash_Wind", "FXRelic_NAtk_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia3,1,Root)"]}, {"说明": "击飞技能施放时在面前产生一个闪电鞭", "Id": "Rise_ThunderWhips", "Tag": ["ActionLv3", "FXRelic_Below_Thunder", "FXRelic_NAtk_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick13,0.5,Root)"]}, {"说明": "位移起点生成一个闪电残影", "Id": "Dash_ThunderStanding", "Tag": ["ActionLv3", "FXRelic_Dash_Thunder", "FXRelic_NAtk_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick6,1,Root)"]}, {"说明": "下砸时对击中的目标产生一次电涌", "Id": "Smash_ThunderHit", "Tag": ["ActionLv3", "FXRelic_Smash_Thunder", "FXRelic_NAtk_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick13,0.5,Root)"]}, {"说明": "击飞技能时，产生一个光柱。", "Id": "Rise_LightAoe", "Tag": ["ActionLv3", "FXRelic_Below_Light", "FXRelic_NAtk_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem13,1,Root)"]}, {"说明": "位移技能时，在位移的终点，产生一圈光弹。", "Id": "Dash_LightBullet", "Tag": ["ActionLv3", "FXRelic_Dash_Light", "FXRelic_NAtk_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem6,1,Root_Bullet)"]}, {"说明": "下砸技能时，产生一圈光弹，每颗产生一次耀光", "Id": "Smash_LightAoe2", "Tag": ["ActionLv3", "FXRelic_Smash_Light", "FXRelic_NAtk_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem4,1,Root)"]}, {"说明": "击飞技能时，对击中的一个敌人产生一个黑暗立场。", "Id": "Rise_DarkAoe", "Tag": ["ActionLv3", "FXRelic_Below_Dark", "FXRelic_NAtk_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit2,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit2,1,0,true)"]}, {"说明": "下砸技能时，将落点附近的敌人拉扯到落点", "Id": "Dash_DarkAoe2", "Tag": ["ActionLv3", "FXRelic_Dash_Dark", "FXRelic_NAtk_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis4,0.7,root)"]}, {"说明": "下砸技能时，产生一道黑暗AOE，每秒向周遭的敌人进行一次攻击并吸引敌人", "Id": "Smash_DarkAoe1", "Tag": ["ActionLv3", "FXRelic_Smash_Dark", "FXRelic_NAtk_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis3,1,Root)"]}]}