{"AIScript": [{"说明": "转阶段", "Id": "<PERSON><PERSON>_Golem_ChangeStage", "Condition": ["MobAIScript.CheckRogueMobHpLessThanBuffStack(Rogue_Boss_CheckSecondStage)", "MobAIScript.CheckHasBuff(Rogue_Boss_FirstStage)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageState)"]}, {"说明": "特殊动作", "Id": "Lava_Golem_MoveToSetPoint", "Condition": ["MobAIScript.CheckHasBuff(Rogue_Lava_MoveToSetPoint)"], "OnReady": [], "Action": ["MobAIScript.AITurnToTargetPointThenDoAction(CenterPoint,Action_MoveToSetPoint,2000,2000,100)"]}, {"说明": "特殊动作", "Id": "Lava_Golem_NormalAttack_S5", "Condition": ["MobAIScript.CheckHasBuff(Rogue_Boss_FirstStage)", "MobAIScript.CheckHasBuff(Rogue_Lava_Beam)"], "OnReady": [], "Action": ["MobAIScript.CheckCenterPointThenDoAction(CenterPoint,NormalAttack_S5)"]}, {"说明": "特殊动作", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_RageAttack_S5", "Condition": ["MobAIScript.CheckHasBuff(Rogue_Boss_SecondStage)", "MobAIScript.CheckHasBuff(Rogue_Lava_Beam)"], "OnReady": [], "Action": ["MobAIScript.CheckCenterPointThenDoAction(CenterPoint,RageAttack_S5)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "Lava_Golem_MoveToPlayer", "Condition": ["MobAIScript.CheckPlayerInRange(200,99999)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}]}