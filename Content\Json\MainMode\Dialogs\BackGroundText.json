{"Dialogs": [{"说明": "背景字幕主线任务1a", "Id": "BackGroundText_MainQuest1a", "FirstClip": "Dialog1a", "NpcId": [], "Clips": [{"Id": "Dialog1a", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1a1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1a2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1a3"}], "Selections": [{}]}]}, {"说明": "背景字幕主线任务1b", "Id": "BackGroundText_MainQuest1b", "FirstClip": "Dialog1b", "NpcId": [], "Clips": [{"Id": "Dialog1b", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1b2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1b3"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1b4"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest1b5"}], "Selections": [{}]}]}, {"说明": "背景字幕主线任务2e", "Id": "BackGroundText_MainQuest2e", "FirstClip": "Dialog1a", "NpcId": [], "Clips": [{"Id": "Dialog2e", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest2e1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "BackGroundText_MainQuest2e2"}], "Selections": [{}]}]}, {"说明": "背景字幕主线任务4c", "Id": "BackGroundText_MainQuest4c", "FirstClip": "Dialog4c", "NpcId": [], "Clips": [{"Id": "Dialog4c", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c3"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c4"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c5"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "GuideGuard_Name", "Text": "GuideGuard_Speech4c6"}], "Selections": [{}]}]}]}