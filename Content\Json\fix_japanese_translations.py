#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正日文翻译，将中文翻译改为正确的日文
"""
import json
import re

def is_chinese_text(text):
    """检查文本是否包含中文字符"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))

def translate_to_japanese(chinese_text, english_text=""):
    """将中文翻译为日文"""
    
    # 技能名称翻译对照表
    skill_translations = {
        # 剑盾技能
        "鹰喙连击": "鷹嘴連撃",
        "蛇尾扫击": "蛇尾掃撃", 
        "环空击": "環空撃",
        "枪尾刺": "槍尾刺",
        "岚刺": "嵐刺",
        "闪空斩": "閃空斬",
        "辉风击": "輝風撃",
        "烈鹰斩": "烈鷹斬",
        "枪连击": "槍連撃",
        "枪乱舞": "槍乱舞",
        "突刺旋风": "突刺旋風",
        "突袭升空击": "突襲昇空撃",
        "飞龙爪": "飛龍爪",
        "降临之枪": "降臨の槍",
        "飞燕升空击": "飛燕昇空撃",
        
        # 双刀技能
        "蝎尾连击": "蠍尾連撃",
        "恐鸦连击": "恐鴉連撃",
        "双月斩": "双月斬",
        "蜂刺": "蜂刺",
        "断空斩": "断空斬",
        "迅刃斩": "迅刃斬",
        "迅月斩": "迅月斬",
        "圆舞斩(待删)": "円舞斬（削除予定）",
        "锋刃剑舞": "鋒刃剣舞",
        "升空斩": "昇空斬",
        "猎空斩": "猟空斬",
        "片刃双击": "片刃双撃",
        "飞燕回旋斩": "飛燕回旋斬",
        "风车乱舞": "風車乱舞",
        "风车斩": "風車斬",
        
        # 大剑技能
        "狮鹫连击": "獅子鷲連撃",
        "龙爪斩击": "龍爪斬撃",
        "雄鹿连击": "雄鹿連撃",
        "蓄力斩": "蓄力斬",
        "旋风蓄力斩": "旋風蓄力斬",
        "崩裂蓄力斩": "崩裂蓄力斬",
        "巨刃破空斩": "巨刃破空斬",
        "突袭迅斩": "突襲迅斬",
        "剑意反击": "剣意反撃",
        "空蓄斩": "空蓄斬",
        "冲地斩": "衝地斬",
        "猎魔突袭斩": "猟魔突襲斬",
        "空中突袭斩": "空中突襲斬",
        "狼跃斩": "狼躍斬",
        "落鹰击": "落鷹撃",
        "破魔律法": "破魔律法",
        
        # 特殊技能
        "火焰的祝福": "炎の祝福",
        
        # 技能描述中的常用词汇
        "连续三次向前猛刺敌人": "前方の敵に三連続で強力な突きを放つ",
        "快速戳击后连续三次横扫敌人": "素早い突きの後、三連続で敵を横薙ぎする",
        "向上挑起前的敌人": "前方の敵を上方に打ち上げる",
        "连续突刺敌人，连打可延长攻击時間": "敵を連続突刺、連打で攻撃時間延長",
        "使用长枪横扫周囲敌人，可连续攻击": "長槍で周囲の敵を横薙ぎ、連続攻撃可能",
        "用长枪的尾部向前快速刺击敌人": "長槍の尾部で前方の敵を素早く突く",
        "向前突刺,命中敌人时自动派生挑空连击": "前方突刺、敵命中時に自動で打ち上げ連撃派生",
        "向前跃击,命中敌人时向后跳，并挑空敌人": "前方跳撃、敵命中時に後方跳躍し敵を打ち上げ",
        "后跳同时挥舞长枪,命中敌人后自动派生挑空连击": "後方跳躍と同時に長槍振り、敵命中後自動で打ち上げ連撃派生",
        "在空中垂直向下突刺，使用时高度越高，造成ダメージ越大": "空中で垂直下方突刺、使用時の高度が高いほどダメージ増加",
        "在空中向斜下俯冲突刺,命中敌人时派生跳跃连击": "空中で斜め下急降下突刺、敵命中時に跳躍連撃派生",
        "在空中向前突进横扫,命中敌人后派生跳跃下劈": "空中で前方突進横薙ぎ、敵命中後に跳躍下劈派生",
        
        # 双刀技能描述
        "双剑快速打出三段斩击": "双剣で素早く三段斬撃を放つ",
        "双剑快速打出四段斩击": "双剣で素早く四段斬撃を放つ",
        "二连斩击并向后跳跃": "二連斬撃で後方跳躍",
        "上斩的同时快速跃起，并挑空敌人": "上斬と同時に素早く跳躍し、敵を打ち上げる",
        "向周囲打出乱舞斩击": "周囲に乱舞斬撃を放つ",
        "迅速的突刺前敌人": "前方の敵を素早く突刺",
        "向前冲刺并突进斩击": "前方ダッシュで突進斬撃",
        "向前冲刺，可连续攻击": "前方ダッシュ、連続攻撃可能",
        "在空中如风车旋转般向下连续斬击": "空中で風車回転のように下方連続斬撃",
        "在空中打出强有力的二连击": "空中で強力な二連撃を放つ",
        "在空中向前快速旋转斬击": "空中で前方高速回転斬撃",
        "在空中向斜下突进斬击": "空中で斜め下突進斬撃",
        
        # 大剑技能描述
        "大剑快速打出三段斬击": "大剣で素早く三段斬撃を放つ",
        "大剑快速打出四段斬击": "大剣で素早く四段斬撃を放つ",
        "大剑快速打出五段斬击，最后一击是强力一击": "大剣で素早く五段斬撃、最後の一撃は強力な一撃",
        "蓄力之后用力劈下，可蓄力三段": "チャージ後に力強く劈き下ろし、三段チャージ可能",
        "蓄力之后挥舞大剑转圈，可蓄力三段": "チャージ後に大剣を振り回し、三段チャージ可能",
        "蓄力之后向前跳劈，可蓄三段": "チャージ後に前方跳び劈き、三段チャージ可能",
        "大剑向前突刺，命中敌人之后跃向空中斬击，并把敌人击飞到空中": "大剣で前方突刺、敵命中後空中跳躍斬撃で敵を空中に吹き飛ばす",
        "挥舞着大剑向前冲锋": "大剣を振りながら前方突撃",
        "举剑招架敌人，完美招架可触发强力反击": "剣で敵をパリィ、完璧なパリィで強力な反撃発動",
        "在空中蓄力之后用力劈下，可蓄力三段。": "空中でチャージ後に力強く劈き下ろし、三段チャージ可能",
        "从空中快速向下重劈": "空中から素早く下方重劈",
        "在空中向前冲锋并挥舞大剑，有两下连段": "空中で前方突撃し大剣振り、二段連撃",
        "向上跃起后，挥舞大剑向前冲锋，直到落地后砍出强烈的一击": "上方跳躍後、大剣振りながら前方突撃、着地後に強烈な一撃",
        "在空中斬击敌人，命中后向上一小段距离": "空中で敵を斬撃、命中後上方に少し移動",
        "向周囲释放振动波攻攻击人，随后冲刺地面造成范围ダメージ": "周囲に振動波を放ち敵を攻撃、その後地面ダッシュで範囲ダメージ",
        
        # 特殊描述
        "ノーマル攻击附加火属性附魔": "通常攻撃に火属性エンチャント付与",
        
        # 通用词汇
        "造成": "",
        "法器充能": "法器チャージ",
        "戻る": "後退斬",
        "終了": "槍尾刺",
    }
    
    # 应用翻译
    result = chinese_text
    for cn, jp in skill_translations.items():
        result = result.replace(cn, jp)
    
    return result

def fix_japanese_translations(file_path):
    """修正文件中的日文翻译"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        game_texts = data.get('GameTexts', [])
        modified_count = 0
        
        for entry in game_texts:
            # 跳过注释行
            if any(key.startswith('_') for key in entry.keys()):
                continue
                
            # 检查日文翻译
            if 'Japanese' in entry:
                japanese_text = entry['Japanese']
                chinese_text = entry.get('Chinese', '')
                english_text = entry.get('English', '')
                
                # 如果日文翻译包含中文字符，需要修正
                if is_chinese_text(japanese_text):
                    new_japanese = translate_to_japanese(chinese_text, english_text)
                    entry['Japanese'] = new_japanese
                    modified_count += 1
                    print(f"修正: {japanese_text} -> {new_japanese}")
        
        # 保存文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        
        print(f"完成！共修正 {modified_count} 个翻译")
        return modified_count
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return 0

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        fix_japanese_translations(file_path)
    else:
        print("请提供文件路径作为参数")
