{"RogueMobActionValue": [{"Id": "Rogue_Death_Reaper", "Actions": [{"Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": -99}, {"MinRange": 200, "MaxRange": 700, "Weight": 10}, {"MinRange": 700, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 4, "MaxActionCD": 7}, {"Id": "NormalAttack_M1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 600, "Weight": 5}, {"MinRange": 600, "MaxRange": 1500, "Weight": 5}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "MinActionCD": 4, "MaxActionCD": 7}, {"Id": "NormalAttack_M2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": 5}, {"MinRange": 200, "MaxRange": 500, "Weight": 5}, {"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "MinActionCD": 4, "MaxActionCD": 7}, {"Id": "NormalAttack_L1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 600, "Weight": 4}, {"MinRange": 600, "MaxRange": 1500, "Weight": 4}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 4, "MaxActionCD": 7}, {"Id": "Dodge_DashStep_Front", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 600, "Weight": -99}, {"MinRange": 600, "MaxRange": 1500, "Weight": 9}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "MinActionCD": 2, "MaxActionCD": 3.5}, {"Id": "<PERSON>_Taunt_Back", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": 9}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Death_Reaper_ForceTaunt", "BuffStack": 6, "Weight": 9999}], "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Action_Stare01", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": 1}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 9999}], "MinActionCD": 2.8, "MaxActionCD": 3.5}]}], "AIScript": [{"说明": "Death_Reaper基础移动", "Id": "Death_Reaper_MoveToPlayer", "Condition": ["MobAIScript.CheckPlayerInRange(500,99999)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}]}