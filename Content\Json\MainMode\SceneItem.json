{"SceneItem": [{"Id": "Test01", "Tag": ["Wall"], "BpPath": "Core/Item/TestSceneItem", "LifeSpan": 30, "Part": [{"Meat": {"Slash": 0.01, "Blunt": 0.01, "Bullet": 0.01, "Fire": 0, "Ice": 0, "Water": 0, "Wind": 0, "Earth": 0, "Holy": 0, "Dark": 0}, "Part": "Other", "SaveThrow": {"Stun": 10.0}, "Breakable": {"Blunt": 1.0}, "Durability": [20], "CanBeDestroy": false, "StableMod": 100.0, "Type": "Metal"}], "Scale": "X=2,Y=2,Z=2", "Tween": ""}, {"Id": "BlockGoblinDoor", "Tag": [], "BpPath": "Core/Item/Mine/BlockGoblinDoor", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "BlockGoblinRock", "Tag": [], "BpPath": "Core/Item/Mine/BlockGoblinRock", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "BlockOgreRock", "Tag": [], "BpPath": "Core/Item/Mine/BlockOgreRock", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "BlockIceDevilRock", "Tag": [], "BpPath": "Core/Item/Mine/BlockIceDevilRock", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "OgrePillar", "Tag": ["<PERSON><PERSON>"], "BpPath": "Core/Item/Monster/Ogre/OgrePillar", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "OgreExplodePillar", "Tag": [], "BpPath": "Core/Item/Monster/Ogre/ExplodePillar", "LifeSpan": 0, "Part": [], "Scale": "X=1,Y=1,Z=1", "Tween": ""}, {"Id": "IceScythe", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/IceDevil/SceneItem_IceScythe", "LifeSpan": 60, "Part": [], "Tween": ""}, {"Id": "I<PERSON><PERSON>", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/IceDevil/Icicle", "LifeSpan": 60, "Part": [], "Tween": ""}, {"Id": "IceSpike", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/IceDevil/IceSpike", "LifeSpan": 60, "Part": [], "Tween": ""}]}