{"Buff": [{"分割": "-------------------------------------------伤害攻击-----------------------------------------"}, {"说明": "角色造成伤害提高 1%", "Id": "Rogue_DamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["BuffUtils.DamageTimesUp(0.01)"]}, {"说明": "角色造成伤害降低 1%", "Id": "Rogue_DamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["BuffUtils.DamageTimesUp(-0.01)"]}, {"说明": "角色攻击力提高 0.01%", "Id": "Rogue_AttackUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{"PAtk": 1}, {}]}, {"说明": "角色攻击力百分比提高 0.01%", "Id": "Rogue_AttackPercentUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"PAtk": 1}]}, {"说明": "角色造成的元素伤害提高 1%", "Id": "Rogue_ElementalDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.ElementalDamageUp(0.01)"]}, {"说明": "角色造成的元素伤害降低 1%", "Id": "Rogue_ElementalDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.ElementalDamageUp(-0.01)"]}, {"说明": "角色造成的一级元素伤害提高 1%", "Id": "Rogue_BasicElementalDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.BasicElementalDamageUp(0.01)"]}, {"说明": "角色造成的一级元素伤害降低 1%", "Id": "Rogue_BasicElementalDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.BasicElementalDamageUp(-0.01)"]}, {"说明": "角色造成的二级元素伤害提高 1%", "Id": "Rogue_AdvancedElementalDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.AdvancedElementalDamageUp(0.01)"]}, {"说明": "角色造成的二级元素伤害降低 1%", "Id": "Rogue_AdvancedElementalDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.AdvancedElementalDamageUp(-0.01)"]}, {"说明": "角色造成的物理伤害提高 1%", "Id": "Rogue_PhysicalDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.PhysicalDamageUp(0.01)"]}, {"说明": "角色造成的物理伤害降低 1%", "Id": "Rogue_PhysicalDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.PhysicalDamageUp(-0.01)"]}, {"说明": "角色造成的直接伤害提高 1%", "Id": "Rogue_DirectDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "角色造成的直接伤害降低 1%", "Id": "Rogue_DirectDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.DirectDamageUp(-0.01)"]}, {"说明": "角色造成的反击伤害提高 1%", "Id": "Rogue_CounterDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.CounterDamageUp(0.01)"]}, {"说明": "角色造成的反击伤害降低 1%", "Id": "Rogue_CounterDamageDown", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["RogueBuff.CounterDamageUp(-0.01)"]}, {"说明": "限制内角色造成的物理伤害提高 1%", "Id": "Rogue_PhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 100000, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "限制内角色造成的物理伤害降低 1%", "Id": "Rogue_PhysicalDamageDownLimited", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 100000, "OnHit": ["RogueBuff.DirectDamageUp(-0.01)"]}, {"说明": "角色非地面造成的直接伤害提高，每层1%", "Id": "Rogue_SkyDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["BuffUtils.PhysicalDamageTimesUpInSky(0.01)"]}, {"说明": "角色地面造成的直接伤害提高，每层1%", "Id": "Rogue_GroundDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["BuffUtils.PhysicalDamageTimesUpOnGround(0.01)"]}, {"说明": "生命值高于80%,伤害加成1%", "Id": "Rogue_NoInjury", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DamageTimesUpInNoInjury(0.8,0.01)"]}, {"说明": "生命值低于50% 每1%伤害加成0.1%", "Id": "Rogue_BackWater", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DamageTimesUpInBackwater(0.5,0.01)"]}, {"说明": "生命值低于50% 每1%伤害加成0.1%", "Id": "Rogue_BackWater", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DamageTimesUpInBackwater(0.5,0.01)"]}, {"说明": "生命值低于50% 每1%伤害加成0.1%", "Id": "Rogue_BackWaterPower", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DamageTimesUpInBackwaterPower(0.3,0.001)"]}, {"说明": "精英以及Boss伤害加成 1%", "Id": "Rogue_EliteDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.EliteDamageUp(0.01)"]}, {"说明": "普通敌人伤害加成 1%", "Id": "Rogue_NormalDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.NormalDamageUp(0.01)"]}, {"说明": "对被控制敌人伤害提升 1%", "Id": "Rogue_ControlledDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.ControlledDamageUp(0.01)"]}, {"说明": "对带有debuff的敌人伤害提升 1%", "Id": "Rogue_DebuffCarrierDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DebuffCarrierDamageUp(0.01)"]}, {"说明": "根据敌人身上的debuff数量提高伤害倍率 1%", "Id": "Rogue_DamageUpByTargetDebuffNum", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000, "OnHit": ["RogueBuff.DamageUpByTargetDebuffNum(0.01)"]}, {"说明": "法器伤害增加1%", "Id": "Rogue_ItemDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000}, {"说明": "法器伤害减少1%", "Id": "Rogue_ItemDamageDown", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000}, {"分割": "-----------------------------------------暴击暴伤--------------------------------------"}, {"说明": "暴击率增加0.01%", "Id": "Rogue_CriticalChanceUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{"CriticalChance": 0.0001}, {}]}, {"说明": "暴击率减少0.01%", "Id": "Rogue_CriticalChanceDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{"CriticalChance": -0.0001}, {}]}, {"说明": "暴击伤害增加0.01%", "Id": "Rogue_CriticalRateUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{"CriticalRate": 0.0001}, {}]}, {"说明": "暴击伤害减少0.01%", "Id": "Rogue_CriticalRateDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{"CriticalRate": -0.0001}, {}]}, {"分割": "-----------------------------------------生存--------------------------------------"}, {"说明": "最大生命值增加 1点", "Id": "Rogue_MaxHealthUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{"HP": 1}, {}]}, {"说明": "最大生命值百分比增加 0.01%", "Id": "Rogue_MaxHealthPercentUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"HP": 1}]}, {"说明": "最大生命值减少 1点", "Id": "Rogue_MaxHealthDown", "Tag": ["Help", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{"HP": -1}, {}]}, {"说明": "最大生命值百分比减少 0.01%", "Id": "Rogue_MaxHealthPercentDown", "Tag": ["Help", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"HP": -1}]}, {"说明": "角色受到的伤害提高，每层1%", "Id": "Rogue_HurtUp", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnBeHurt": ["BuffUtils.HurtDamageUp(0.01)"]}, {"说明": "角色受到的伤害减少，每层1%", "Id": "Rogue_HurtDown", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnBeHurt": ["BuffUtils.HurtDamageUp(-0.01)"]}, {"说明": "角色受到的Break伤害提高，每层1%", "Id": "Rogue_HurtBreakUp", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnBeHurt": ["BuffUtils.BreakDamageUp(0.01)"]}, {"说明": "角色造成的Break伤害提高，每层1%", "Id": "Rogue_HitBreakUp", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["BuffUtils.BreakDamageUp(0.01)"]}, {"说明": "角色空中时受到暴击概率提升0.01%", "Id": "Rogue_Wind_HurtCriticalChanceUp", "Tag": ["<PERSON>rm", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.HurtCriticalChanceUpInSkyOrWind(0.0001)"]}, {"分割": "-----------------------------------------敏捷--------------------------------------"}, {"说明": "角色行动速度减少，每层0.01%", "Id": "Rogue_ActionSpeedDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 10000, "Property": [{}, {"ActionSpeed": -1}], "OnOccur": ["RogueBuff.BuffPropCaluTargetResistance(SpeedDownResistance)"]}, {"说明": "角色移动速度降低，每层0.01%", "Id": "Rogue_MoveSpeedDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 100000, "Property": [{}, {"MoveSpeed": [-1, -1, -1]}], "OnOccur": ["RogueBuff.BuffPropCaluTargetResistance(SpeedDownResistance)"]}, {"说明": "角色移动速度、行动速度增加，每层0.01%", "Id": "Rogue_SpeedUp", "Tag": ["<PERSON>rm", "PropertyUp"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "角色行动速度增加，每层0.01%", "Id": "Rogue_ActionSpeedUp", "Tag": ["<PERSON>rm", "PropertyUp"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1}]}, {"分割": "-----------------------------------------资源--------------------------------------"}, {"说明": "获得的钥匙数量+1(标记用，在生成钥匙奖励时检测该BUFF层数)", "Id": "Rogue_GetKeyUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "获得的魂晶数量+1%(标记用，在生成魂晶奖励时检测该BUFF层数)", "Id": "Rogue_GetSoulUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "获得的金币数量+1%(标记用，在生成金币奖励时检测该BUFF层数)", "Id": "Rogue_GetCoinUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"分割": "-----------------------------------------法器Cd--------------------------------------"}, {"说明": "主动道具充能加快", "Id": "RogueItemRecover", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "OnOccur": ["RogueBuff.RogueItemRecoverUp_OnOccur(0.01)"], "OnRemoved": ["RogueBuff.RogueItemRecoverDown_OnRemove(0.01)"]}, {"说明": "主动道具充能减慢", "Id": "RogueItemExtend", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "OnOccur": ["RogueBuff.RogueItemRecoverDown_OnOccur(0.01)"], "OnRemoved": ["RogueBuff.RogueItemRecoverUp_OnRemove(0.01)"]}, {"说明": "主动道具充能固定数值加快", "Id": "RogueItemValueRecover", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "OnOccur": ["RogueBuff.RogueItemRecoverValueUp_OnOccur(1)"], "OnRemoved": ["RogueBuff.RogueItemRecoverValueDown_OnRemove(1)"]}, {"说明": "主动道具充能减慢", "Id": "RogueItemValueExtend", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "OnOccur": ["RogueBuff.RogueItemRecoverValueDown_OnOccur(1)"], "OnRemoved": ["RogueBuff.RogueItemRecoverValueUp_OnRemove(1)"]}, {"分割": "-----------------------------------------Buff时间--------------------------------------"}, {"说明": "一级元素Buff的持续时间增加", "Id": "Rogue_BasicElementalDurationUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000}, {"分割": "-----------------------------------------道具类--------------------------------------"}, {"说明": "血瓶增强(天赋用)", "Id": "Rogue_PotionHealUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "OnOccur": ["RogueBuff.PotionHealUp(1)"], "OnRemoved": ["RogueBuff.PotionHealDown_Remove(1)"]}, {"说明": "血瓶遗物增强", "Id": "Rogue_RelicPotionHealUp", "Tag": ["Help", "Relic"], "Priority": 0, "MaxStack": 100000, "OnOccur": ["RogueBuff.PotionHealUp(1)"], "OnRemoved": ["RogueBuff.PotionHealDown_Remove(1)"]}, {"说明": "血瓶增强 百分比等级提升 1级 = 1%最大生命回复", "Id": "Rogue_RelicPotionHealLevelUp", "Tag": ["Help", "Relic"], "Priority": 0, "MaxStack": 100, "OnOccur": ["RogueBuff.PotionHealLevelUp(1)"], "OnRemoved": ["RogueBuff.PotionHealLevelDown_Remove(1)"]}, {"说明": "血瓶增强 百分比等级提升 1级 = 1%最大生命回复 天赋用", "Id": "Rogue_TalentPotionHealLevelUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100, "OnOccur": ["RogueBuff.PotionHealLevelUp(1)"], "OnRemoved": ["RogueBuff.PotionHealLevelDown_Remove(1)"]}, {"说明": "回血道具减弱 百分比等级提升 1级 = -1%总生命回复 诅咒用", "Id": "Rogue_CursePotionHealPercentDown", "Tag": ["Curse"], "Priority": 0, "MaxStack": 100, "OnBeHurt": ["RogueBuff.TakeItemHealDown(0.01)"]}, {"说明": "血瓶数量增多", "Id": "Rogue_PotionNumUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000, "OnOccur": ["RogueBuff.PotionNumUp(1)"], "OnRemoved": ["RogueBuff.PotionNumDown_Remove(1)"]}, {"说明": "血瓶数量回满", "Id": "Rogue_PotionNumRecover", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000, "OnOccur": ["RogueBuff.PotionNumRecover()"]}, {"分割": "-----------------------------------------天赋--------------------------------------"}, {"说明": "休息处升级血瓶时恢复生命值(标记用，在升级血瓶时检测该BUFF，回复该BUFF层数的百分比生命值)", "Id": "Rogue_HealOnUpgrade", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000}, {"说明": "房间结束时百分比恢复生命值", "Id": "Rogue_HealOnRoomEnd", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000, "OnRogueRoomEnd": ["RogueBuff.HealOnRoomEnd(1)"]}, {"说明": "房间结束时百分比恢复AP", "Id": "Rogue_RecoverApOnRoomEnd", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000, "OnRogueRoomEnd": ["RogueBuff.RecoverApPercentOnRoomEnd(1)"]}, {"说明": "捡起魂晶和钥匙时恢复生命值(标记用，在获得钥匙或魂晶奖励时检测该BUFF，回复该BUFF层数的生命值)", "Id": "Rogue_HealOnGetKeyAndSoul", "Tag": ["Help"], "Priority": 0, "MaxStack": 1000}, {"说明": "初始+1金币", "Id": "Rogue_GiveMoneyOnGameStart", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnRogueGameStart": ["RogueBuff.GiveRogueCurrencyOnOccur(Rogue_Coin,1)"]}, {"说明": "获得的钥匙数量+1(标记用，在生成钥匙奖励时检测该BUFF层数)", "Id": "Rogue_GetKeyUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "获得的魂晶数量+1%(标记用，在生成魂晶奖励时检测该BUFF层数)", "Id": "Rogue_GetSoulUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "获得的金币数量+1%(标记用，在生成金币奖励时检测该BUFF层数)", "Id": "Rogue_GetCoinUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "房间结束时金币+1", "Id": "Rogue_GetCoinOnRoomEnd", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnRogueRoomEnd": ["RogueBuff.GetCoinOnRoomEnd(1)"]}, {"说明": "在随机房间难度时，精英房间的权重+1(标记用，在随机房间难度时时检测该BUFF层数)", "Id": "Rogue_AddEliteRoom", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "在Boss房价结算时,奖励加1", "Id": "Rogue_AddBossReward", "Tag": ["Help"], "Priority": 0, "MaxStack": 1, "TickTime": 0}, {"说明": "在商店时,物品的价格减少n%", "Id": "Rogue_ShopDiscount", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "在商店时,物品的价格增加n%", "Id": "Rogue_ShopMoreExpensive", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "在商店时,重置物品的价格永远固定为50", "Id": "Rogue_ShopRefreshFixedPrices", "Tag": ["Help"], "Priority": 0, "MaxStack": 1, "TickTime": 0}, {"说明": "在商店时,商店刷新打折1%", "Id": "Rogue_ShopRefreshDiscount", "Tag": ["Help"], "Priority": 0, "MaxStack": 100000, "TickTime": 0}, {"说明": "战斗中的额外生命次数", "Id": "Rogue_OtherLife", "Tag": ["Help", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnOccur": ["RogueBuff.SetRevivedLifeChanceInData()"], "OnTick": ["RogueBuff.RevivedCarrierOnBeKilled()"], "OnBeKilled": ["RogueBuff.StartRevivedCarrierOnBeKilled(0.1)"]}, {"说明": "Rogue怪物的强制发呆标记BUFF", "Id": "Rogue_MobForceStare", "Tag": ["<PERSON><PERSON>", "Stare"], "Priority": 0, "MaxStack": 99, "TickTime": 0}, {"说明": "Rogue怪物检测玩家远离1500距离的BUFF（每秒给怪物自己添加一层该BUFF，靠近时层数设为1）", "Id": "Rogue_CheckPlayerFarAway", "Tag": ["<PERSON><PERSON>", "Stare"], "Priority": 0, "MaxStack": 999, "TickTime": 1, "OnTick": ["BuffUtils.AddStackByPlayerDistance(1500)"]}, {"分割": "----------------------------------------新手教程--------------------------------------"}, {"说明": "Rogue新手教程用，如果用剑盾的普攻命中敌人则加一层BUFF", "Id": "Rogue_NewbieCheckNormalAttack", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 6, "TickTime": 0, "OnHit": ["RogueBuff.CheckNewbieActionHit(Swordsman_LAttack01,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_LAttack02,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_LAttack03,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirAttack1,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirAttack2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN1,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN3,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN4,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN5,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN1,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN3,2.0)"]}, {"说明": "Rogue新手教程用，如果用剑盾的地面技能命中敌人则加一层BUFF", "Id": "Rogue_NewbieCheckGSkill", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["RogueBuff.CheckNewbieActionHit(Swordsman_UpSlash,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_DashShield,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_RiseSlash,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_DashSlash,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH1,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH3,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH4,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH5,2.0)"]}, {"说明": "Rogue新手教程用，如果用剑盾的空中技能命中敌人则加一层BUFF", "Id": "Rogue_NewbieCheckASkill", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["RogueBuff.CheckNewbieActionHit(Swordsman_AirSlashAttack,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirShieldSmash,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirDownSlashAttack1,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirDownShieldSmash,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirDashSting,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AH1,2.0)"]}, {"说明": "Rogue新手教程用，如果用喝血药则加一层BUFF", "Id": "Rogue_NewbieCheckPotion", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 2, "TickTime": 0.1, "OnTick": ["RogueBuff.CheckNewbieCurActionId(DrinkPotion_Rogue,2.0)"]}, {"说明": "Rogue新手教程用，如果用剑盾防御成功则加一层BUFF", "Id": "Rogue_NewbieCheckDefense", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 3, "TickTime": 0.1, "OnTick": ["RogueBuff.CheckNewbieCurActionId(Swordsman_Defense_Success,2.0)", "RogueBuff.CheckNewbieCurActionId(Swordsman_Defense_JustBlock,3.0)", "RogueBuff.CheckNewbieCurActionId(WuJiang_Defense_Success,3.0)"]}, {"说明": "Rogue新手教程用，如果使用法器则加一层BUFF", "Id": "Rogue_NewbieCheckMagicItem", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 2, "TickTime": 0.1, "OnTick": ["RogueBuff.CheckNewbieCurActionId(CannonSummon_Rogue,3.0)", "RogueBuff.CheckNewbieCurActionId(RangeThunder_Rogue,3.0)"]}, {"说明": "Rogue新手教程用，如果使用觉醒技能则加一层BUFF", "Id": "Rogue_NewbieCheckAwakeSkill", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 2, "TickTime": 0.1, "OnTick": ["RogueBuff.CheckNewbieCurActionId(AwakeSkill_Earthquake,3.0)"]}, {"说明": "Rogue新手教程用，如果使用赠送的技能则加一层BUFF", "Id": "Rogue_NewbieCheckTargetSkill", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["RogueBuff.CheckNewbieActionHit(Swordsman_LAttack01,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_LAttack02,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_LAttack03,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirAttack1,2.0)", "RogueBuff.CheckNewbieActionHit(Swordsman_AirAttack2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN1,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN3,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN4,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GN5,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN1,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN2,2.0)", "RogueBuff.CheckNewbieActionHit(WuJiang_Attack_AN3,2.0)"]}, {"说明": "幸存者新手教程用，使用C技能则加一层BUFF", "Id": "Rogue_NewbieCheckChargeSkill", "Tag": ["<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["RogueBuff.CheckNewbieActionHit(WuJiang_Attack_GH5,2.0)"]}, {"分割": "---------------------------------------- 武器特殊buff --------------------------------------"}, {"说明": "盾反伤害提高 1%", "Id": "Rogue_DamageUp_DefenceCounter", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["WeaponBuff.DamageUp_CounterAction()"]}, {"说明": "JustAttack伤害提高 1%", "Id": "Rogue_DamageUp_JustAttack", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["WeaponBuff.DamageUp_JustAttackAction()"]}, {"说明": "每次武器命中 有几层回几点血", "Id": "Rogue_RestoreHpOnHit", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 10000, "OnHit": ["WeaponBuff.AddHpOnHit()"]}, {"说明": "在空中每停留1秒，攻击力增加2%，最多10%", "Id": "Rogue_AttackUpOnAir_1_2_10", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(1,2,10)"]}, {"说明": "在空中每停留1秒，攻击力增加3%，最多15%", "Id": "Rogue_AttackUpOnAir_1_3_15", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(1,3,15)"]}, {"说明": "在空中每停留0.8秒，攻击力增加3%，最多15%", "Id": "Rogue_AttackUpOnAir_0.8_3_15", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(0.8,3,15)"]}, {"说明": "在空中每停留0.8秒，攻击力增加4%，最多20%", "Id": "Rogue_AttackUpOnAir_0.8_4_20", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(0.8,4,20)"]}, {"说明": "在空中每停留0.6秒，攻击力增加4%，最多20%", "Id": "Rogue_AttackUpOnAir_0.6_4_20", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(0.6,4,25)"]}, {"说明": "在空中每停留0.5秒，攻击力增加5%，最多25%", "Id": "Rogue_AttackUpOnAir_0.5_5_25", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(0.5,5,25)"]}, {"说明": "在空中每停留1秒，攻击力增加4%，最多20%", "Id": "Rogue_AttackUpOnAir_1_4_20", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnTick": ["WeaponBuff.AttackUpOnAir(1,4,20)"]}, {"说明": "蓄力时间缩短1%", "Id": "Rogue_PowerSpeedUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "TickTime": 0.1}, {"说明": "蓄力伤害 增加 1%", "Id": "Rogue_PowerDamageUp", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "TickTime": 0.1, "OnHit": ["WeaponBuff.DamageUp_PowerAction()"]}, {"说明": "蓄力伤害 减少 1%", "Id": "Rogue_PowerDamageDown", "Tag": ["Help"], "Priority": 0, "MaxStack": 10000, "TickTime": 0.1, "OnHit": ["WeaponBuff.DamageDown_PowerAction()"]}]}