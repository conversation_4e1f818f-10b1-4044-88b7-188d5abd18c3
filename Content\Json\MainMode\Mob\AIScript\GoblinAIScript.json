{"AIScript": [{"说明": "向巡逻点走", "Id": "Goblin_MoveToNextPathNode", "Condition": ["MobAIScript.CheckMoveToNextPathNode()"], "OnReady": [], "Action": ["MobAIScript.MoveToNextPathNode()"], "Tag": ""}, {"说明": "脱战时清除已吼叫BUFF", "Id": "Goblin_ClearRoaredBuff", "Condition": ["GoblinAIScript.CheckJustLeaveBattle()"], "OnReady": [], "Action": ["GoblinAIScript.ClearGoblinRoaredBuff()"]}, {"说明": "进入战斗的吼叫", "Id": "Go<PERSON>_Roar", "Condition": ["MobAIScript.CheckNotHasBuff(Goblin_HasRoared)", "MobAIScript.CheckStimulateByView()"], "OnReady": [], "Action": ["WereRatAIScript.WereRatRoarWhenStartBattle(300,<PERSON>_<PERSON>,Roar)"]}, {"说明": "庆祝胜利", "Id": "Go<PERSON>_CelebrateVictory", "Condition": ["MobAIScript.CheckNotHasBuff(Goblin_Celebrate)", "GoblinAIScript.CheckViewedDeathEnemy()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(CelebrateVictory)"]}, {"说明": "向左跳", "Id": "Goblin_LeftJump", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,-90,-60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,400,-90,-60,LeftJump)"]}, {"说明": "向右跳", "Id": "Goblin_RightJump", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,60,90)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,400,60,90,RightJump)"]}, {"说明": "后跳", "Id": "Goblin_DodgeBack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,150,-30,30)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Dodge_Back)"]}, {"说明": "侧滚躲避（左或者右）", "Id": "Goblin_DodgeLeftOrRight", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,150,-90,90)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(Dodge_Left,Dodge_Right)"]}, {"说明": "向右/左踱步", "Id": "Goblin_<PERSON>", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(300,800)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveAroundViewedClosetEnemy(300,800,RightPace,LeftPace)"]}, {"说明": "180度左右转身动作", "Id": "Goblin_Turn", "Condition": ["GoblinAIScript.CheckCanTurn(0,3000,-60,60)"], "OnReady": [], "Action": ["GoblinAIScript.AttackClosetNoViewedEnemy(0,3000,TurnRight_180,TurnLeft_180)"]}]}