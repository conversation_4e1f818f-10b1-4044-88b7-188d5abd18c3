{"AIScript": [{"Notes": "在矿点附近，有采矿buff，采矿buff的层数>=15, 做采矿动作和休息动作", "Id": "WereRat_DoActionOnMinePoint", "Condition": ["WereRatCommandoAIScript.CheckIsNearMinePoint(70)", "MobAIScript.CheckHasBuff(DoMine)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.DoActionOnMinePoint()"], "Tag": ""}, {"说明": "如果有采矿的buff，移动到矿点", "Id": "WereRat_MoveToNearestMinePoint", "Condition": ["MobAIScript.CheckHasBuff(DoMine,0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveToNearestMinePoint()"], "Tag": ""}, {"说明": "检查是否前往下一个点巡逻，每到一个巡逻点，发一次呆", "Id": "WereRat_MoveToNextPathNode", "Condition": ["MobAIScript.CheckNotHasBuff(DoMine)", "MobAIScript.CheckMoveToNextPathNode()"], "OnReady": [], "Action": ["MobAIScript.MoveToNextPathNode(Daze1,<PERSON>ze2,<PERSON>ze3)"], "Tag": ""}, {"说明": "高兴捡起奶酪的动作", "Id": "HappyPickUp<PERSON>heese", "Condition": ["WereRatAIScript.CheckCanGetCheese()", "MobAIScript.CheckFightingWillLevelEquals(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(HappyPickUpCheese)"]}, {"说明": "吼叫后进入亢奋状态", "Id": "WereRat_RoarBeforeRage", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(1)", "MobAIScript.CheckFightingWillValueLess(2001)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageRoar)"]}, {"说明": "三连抓", "Id": "WereRat_RageScratch", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,500,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.WereRatRageScratch(0,500,-60,60,Scratch01)"]}, {"说明": "二连抓", "Id": "WereRat_Scratch", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,300,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.WereRatScratch(0,300,-60,60,Scratch01)"]}, {"说明": "向前突击，用力把武器砸下去，人倒地，然后起身拔武器", "Id": "WereRat_ForwardStrike", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(500,1000)", "MobAIScript.CheckFightingWillLevelEquals(1)", "MobAIScript.CheckNotHasBuff(WereRat_ForwardStrike_CD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(500,1000,ForwardStrike)"]}, {"说明": "向前突击，用力把武器砸下去，兴奋状态下不倒地，直接拔武器", "Id": "WereRat_RageForwardStrike", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(500,1000)", "MobAIScript.CheckFightingWillLevelEquals(2)", "MobAIScript.CheckNotHasBuff(WereRat_ForwardStrike_CD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(500,1000,RageForwardStrike)"]}, {"说明": "趴下四肢着地向敌人爬过来", "Id": "WereRatCommando_Crawling", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1300,3000)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatAIScript.RunToViewedClosetEnemy(1300,3000,2000,Crawling_Short,Crawling_<PERSON>)"]}, {"说明": "在视野范围内看到敌人，选取战斗连招", "Id": "WereRatCommando_SelectCombo", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1500)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.RandomWereRatCommandoCombo(0,450,300,900,800,1500)"]}, {"说明": "在视野范围内看到敌人，选取战斗连招(狂暴)", "Id": "WereRatCommando_SelectCrazyCombo", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1500)", "MobAIScript.CheckHasBuff(WereRatCrazy)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.RandomWereRatCommandoCrazyCombo(0,450,300,900,800,1500)"]}]}