{"AIScript": [{"说明": "近距离攻击", "Id": "Skeleton_NearAttack_S1", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,350,-15,15)", "MobAIScript.CheckNotHasBuff(MobAttackCD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,350,NormalAttack_S1)"]}, {"说明": "中距离攻击", "Id": "Skeleton_MiddleAttack_M1", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,800,-15,15)", "MobAIScript.CheckNotHasBuff(MobAttackCD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,800,NormalAttack_M1)"]}, {"说明": "远距离攻击", "Id": "Skeleton_FarAttack_L1", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,1200,-10,10)", "MobAIScript.CheckNotHasBuff(MobAttackCD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,1200,-10,10,NormalAttack_L1)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "SkeletonTurnToStimulate", "Condition": ["OrcAIScript.CheckHasStimulate()", "MobAIScript.CheckHasEnemyInRange(500,5000)"], "OnReady": [], "Action": ["MobAIScript.AITurnToStimulateDoAction()"]}, {"说明": "发呆", "Id": "Skeleton_Stare01", "Condition": ["MobAIScript.Always()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Stare01)"]}, {"说明": "发呆", "Id": "Skeleton_RotateNearStare01", "Condition": ["MobAIScript.Always()"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,400,Stare01)"]}, {"说明": "发呆", "Id": "Skeleton_RotateFarStare01", "Condition": ["MobAIScript.Always()"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,1200,Stare01)"]}]}