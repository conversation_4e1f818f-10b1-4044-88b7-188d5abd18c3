{"Class": [{"说明": "幸运E，当然也可能用的是Fuck zerg stick", "Id": "<PERSON><PERSON><PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "BladeDancer", "Swordsman"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "Spear_Move", "Unarmed": "Spear_Unarmed_Move"}, "Flying": {"Armed": "Spear_Move", "Unarmed": "Spear_Unarmed_Move"}, "Falling": {"Armed": "Spear_Fall", "Unarmed": "Spear_Unarmed_Fall"}, "Attached": {"Armed": "Spear_Ride", "Unarmed": "Spear_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "S<PERSON>_<PERSON>", "UnArmed": "S<PERSON>_<PERSON>"}, "Blow": {"Armed": "<PERSON><PERSON>_<PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON>"}, "Frozen": {"Armed": "S<PERSON>_Frozen", "UnArmed": "S<PERSON>_Frozen"}, "Bounced": {"Armed": "<PERSON><PERSON>_<PERSON>ced", "UnArmed": "<PERSON><PERSON>_<PERSON>ced"}, "Dead": {"Armed": "Spear_Dead", "UnArmed": "Spear_Dead"}, "Landing": {"Armed": "S<PERSON>_JustFall", "UnArmed": "S<PERSON>_Unarmed_JustFall"}, "SecondWind": {"Armed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, "GetUp": {"Armed": "Spear_RevivedOnSecWind", "UnArmed": "Spear_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "PoleArm", "DefaultWeapons": ["Iron_Spear"], "ActionOnChangeTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________长枪徒手基础动作________________________________"}, {"说明": "长枪徒手走路站立", "Id": "Spear_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge", "<PERSON><PERSON><PERSON>_Aim", "S<PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive", "Spear_DrawAttack"], "1": ["S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge", "<PERSON><PERSON><PERSON>_Aim", "S<PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Spearman_Male/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "长枪徒手起跳", "Id": "S<PERSON>_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "S<PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["Spear_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "长枪徒手翻滚", "Id": "<PERSON><PERSON>_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["Spear_Unarmed_Move", "S<PERSON>_Unarmed_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_Male/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "长枪徒手下落", "Id": "Spear_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Spearman_Male/UnarmedFall"]}, "Priority": 1}, {"说明": "长枪徒手下落着地", "Id": "S<PERSON>_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "S<PERSON>_Unarmed_Jump", "Unarmed_<PERSON><PERSON>_Dodge", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "长枪收刀", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Player/Fighter/Spearman_Male/Sheath<PERSON><PERSON><PERSON>n"]}}, {"说明": "长枪拔刀", "Id": "DrawSpear", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "S<PERSON>_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Fighter/Spearman_Male/DrawWeapon"]}}, {"Line": "_______________________________长枪(LevelSquencer)动作________________________________"}, {"说明": "长枪_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________长枪(持武器)基础动作________________________________"}, {"Id": "Spear_Move", "Cmds": ["Spear_Move"], "Tags": [{"Tag": "Spear_Move", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Spearman_Male/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Spearman_Male/Move_AimState"]}}, {"Id": "S<PERSON>_<PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["<PERSON><PERSON>_<PERSON>"], "2": ["Spear_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>_<PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_QS_B"], "1": ["Spear_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Blow_Front"]}}, {"Id": "Spear_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "Spear_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "Spear_Jump", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}, {"Tag": "S<PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "Spear_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack"], "1": ["Spear_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Spearman_Male/Fall"]}, "Priority": 1}, {"Id": "S<PERSON>_JustFall", "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump", "<PERSON><PERSON>_<PERSON>"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>_<PERSON>", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>_<PERSON>", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "S<PERSON>_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "S<PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Dodge/Step_F"]}, "Cost": {"SP": 0}}, {"说明": "受身动作后翻", "Id": "Spear_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Spear_QS_B", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "<PERSON><PERSON>_<PERSON>", "Spear_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身动作前翻", "Id": "Spear_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Spear_QS_F", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "<PERSON><PERSON>_<PERSON>", "Spear_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "Spear_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________长枪受击(特殊)动作________________________________"}, {"Id": "S<PERSON>_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________长枪特殊动作________________________________"}, {"Id": "S<PERSON>_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Jump/AttachOnTarget"]}}, {"Id": "Spear_Ride", "Cmds": [], "Tags": [{"Tag": "Spear_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["Spear_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________长枪基础(附加)动作________________________________"}, {"说明": "_", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/ChangeToSpearman1"]}}, {"说明": "长枪弹刀动作", "Id": "<PERSON><PERSON>_<PERSON>ced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Fighter/Spearman_Male/Attack/Spear/Bounced"]}}, {"说明": "瞄准动作", "Id": "<PERSON><PERSON>_Aim", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_Aim", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>", "S<PERSON>_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Player/Fighter/Spearman_Male/Spearman_Aim"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_Male/PickUp"]}}, {"Line": "_______________________________长枪命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "Spear_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "Spear_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/OrderBuddy/OrderBuddyMoveToTarget"]}}], "RogueBattleActions": [{"Line": "_______________________________长枪_普攻_地面Action1_______________________________"}, {"Id": "Spearman_LAttack01", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_LAttack1", "From": 0}, {"Tag": "Spear_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_LAttack2"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"], "3": ["Spear_BranchAttack2"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_Slash0"]}, "InitAction": true}, {"Id": "Spearman_LAttack02", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_Slash1"]}, "InitAction": true}, {"Id": "Spearman_LAttack03", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack1"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_Slash2"]}, "InitAction": true}, {"说明": "长枪分支普通攻击2", "Id": "Spearman_BranchAttack2", "Cmds": [], "Tags": [{"Tag": "Spear_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_BranchAttack3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SlashB1"]}, "InitAction": true}, {"说明": "长枪分支普通攻击3", "Id": "Spearman_BranchAttack3", "Cmds": [], "Tags": [{"Tag": "Spear_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_BranchAttack4"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SlashB2"]}, "InitAction": true}, {"说明": "长枪分支普通攻击4", "Id": "Spearman_BranchAttack4", "Cmds": [], "Tags": [{"Tag": "Spear_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SlashB3"]}, "InitAction": true}, {"Line": "_______________________________长枪_普攻_空中Action1_______________________________"}, {"Id": "Spearman_AirSweapAttack1", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSweapAttack1", "From": 0}, {"Tag": "Spear_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirSweapAttack2"], "1": ["_"], "2": ["Spear_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_SweapSlash0"]}, "InitAction": true}, {"Id": "Spearman_AirSweapAttack2", "Cmds": [], "Tags": [{"Tag": "Spear_AirSweapAttack2", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirSweapAttack1"], "1": ["_"], "2": ["Spear_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_SweapSlash1"]}, "InitAction": true}, {"Line": "_______________________________长枪_技能A(横扫4连)_地面Action2_______________________________"}, {"Id": "Spearman_SweapAttack1", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_SpAttack1", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["Spear_SpAttack2"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_InitAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SweapSlash0"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack2", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_SpAttack3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_InitAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SweapSlash1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack3", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_SpAttack4"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_InitAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SweapSlash2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack4", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["_"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_InitAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_SweapSlash3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_ConsecutiveSpike", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_SpAttack1", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_ConsecutiveSpike"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________长枪_技能A(下落)_空中Action2_______________________________"}, {"Id": "Spearman_AirDownSpikeAttack", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_DashSpike_FallDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________长枪_技能B(突刺、后续技能)_地面Action3_______________________________"}, {"Id": "<PERSON><PERSON><PERSON>_DashSpike_Add_Rise", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_DashSpike"]}}, {"Id": "<PERSON><PERSON><PERSON>_DashSpike_Add_Spike", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_DashSpike_2"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_DashSpike_Combo"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_RiseSlash"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_RiseSlash2_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_RiseSlash2"]}}, {"Line": "_______________________________长枪_技能B(突刺、后续技能)_空中Action3_______________________________"}, {"Id": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_DashSpike_ForwardDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_HitJump"]}}, {"Line": "_______________________________长枪_技能C(位移、后续技能)_地面Action4_______________________________"}, {"Id": "S<PERSON><PERSON>_DashSweapSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/G_DashSweapSlash"]}, "Cost": {"MP": 0}}, {"Id": "S<PERSON><PERSON>_BackJumpSweapSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_BackJumpSweapSlash"]}, "Cost": {"MP": 0}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_DashSweapDownSlash"]}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中弹跳", "Id": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_HitJump2"]}}, {"Line": "_______________________________长枪_技能C(位移、后续技能)_空中Action4_______________________________"}, {"Id": "Spearman_AirDashSweapSlash", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Dodge_Step"], "2": ["Spear_AirInitAttack", "Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_DashSweapSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "空中前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "<PERSON><PERSON><PERSON>_AirDashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Spearman_Male/Attack/Spear/A_DashSweapDownSlash"]}}]}], "Buff": [], "Aoe": []}