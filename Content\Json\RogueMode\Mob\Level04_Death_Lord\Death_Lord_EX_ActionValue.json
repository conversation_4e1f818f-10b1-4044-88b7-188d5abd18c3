{"RogueMobActionValue": [{"Id": "Rogue_<PERSON>_Lord_EX", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 200, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_ThirdStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "RageAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 200, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 4, "MaxActionCD": 6}, {"Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 1500, "Weight": 19}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_ThirdStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 8, "MaxActionCD": 12}, {"Id": "RageAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 1500, "Weight": 19}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 12, "MaxActionCD": 16}, {"Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": 9}, {"MinRange": 200, "MaxRange": 1000, "Weight": 9}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "3"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_ThirdStage", "BuffStack": 1, "Weight": -9999}], "InAir": 1, "MinActionCD": 6, "MaxActionCD": 9}, {"Id": "RageAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-180", "MaxRange": "-135", "Weight": "3"}, {"MinRange": "135", "MaxRange": "180", "Weight": "3"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 15, "MaxActionCD": 20}, {"Id": "NormalAttack_M1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": -99}, {"MinRange": 200, "MaxRange": 1000, "Weight": 9}, {"MinRange": 1000, "MaxRange": 99999, "Weight": 9}], "BuffWeight": [{"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}, {"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_ThirdStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "InDodge": 3, "MinActionCD": 5, "MaxActionCD": 9}, {"Id": "RageAttack_M1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": -99}, {"MinRange": 200, "MaxRange": 1000, "Weight": 9}, {"MinRange": 1000, "MaxRange": 99999, "Weight": 9}], "BuffWeight": [{"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 6, "MaxActionCD": 10}, {"Id": "RageAttack_M2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 700, "Weight": -9999}, {"MinRange": 700, "MaxRange": 1100, "Weight": -9999}, {"MinRange": 1100, "MaxRange": 99999, "Weight": 19}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "InDodge": 3, "MinActionCD": 8, "MaxActionCD": 13}, {"说明": "死灵召唤-level0-骷髅兵", "Id": "RageAttack_M3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level1-弓箭手", "Id": "RageAttack_M4", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level2-铁锤哥", "Id": "RageAttack_M5", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level3-弩哥", "Id": "RageAttack_M6", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level4-盖伦哥", "Id": "RageAttack_M7", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level5-死神镰刀", "Id": "RageAttack_M8", "BaseWeight": -9999, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"说明": "死灵召唤-level6-冰魔俑", "Id": "RageAttack_M9", "BaseWeight": -9999, "DistanceWeight": [{"MinRange": 0, "MaxRange": 100, "Weight": -9999}, {"MinRange": 100, "MaxRange": 4000, "Weight": 10}], "BuffWeight": [{"BuffId": "SummonNum", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": 0}], "MinActionCD": 30, "MaxActionCD": 30}, {"Id": "NormalAttack_L1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 9}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}, {"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_Boss_ThirdStage", "BuffStack": 1, "Weight": -9999}], "InAir": 2, "InDodge": 3, "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "RageAttack_L1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 9}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}, {"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "InAir": 2, "InDodge": 3, "MinActionCD": 8, "MaxActionCD": 13}, {"Id": "RageAttack_X1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 10, "MaxActionCD": 20}, {"Id": "Dodge_DashStep_Back", "BaseWeight": -999, "DegreeWeight": [{"MinRange": "135", "MaxRange": "180", "Weight": "9"}, {"MinRange": "-180", "MaxRange": "-135", "Weight": "9"}, {"MinRange": "-45", "MaxRange": "45", "Weight": "3"}], "ClearedGame": 9, "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Dodge_DashStep_Left", "BaseWeight": -99, "DegreeWeight": [{"MinRange": "135", "MaxRange": "180", "Weight": "9"}, {"MinRange": "-180", "MaxRange": "-135", "Weight": "9"}, {"MinRange": "-45", "MaxRange": "45", "Weight": "3"}], "ClearedGame": 109, "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Dodge_DashStep_Right", "BaseWeight": -99, "DegreeWeight": [{"MinRange": "135", "MaxRange": "180", "Weight": "9"}, {"MinRange": "-180", "MaxRange": "-135", "Weight": "9"}, {"MinRange": "-45", "MaxRange": "45", "Weight": "3"}], "ClearedGame": 109, "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Stare01", "BaseWeight": 10, "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 9999}], "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -2}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 0}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "MinActionCD": 15, "MaxActionCD": 25}, {"Id": "Walk_Front", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": 10}], "BuffWeight": [{"BuffId": "Rogue_Death_Lord_Walk_Front", "BuffStack": 3, "Weight": 9999}], "InHurt": 3, "MinActionCD": 0, "MaxActionCD": 5}, {"Id": "Dodge_DashStep_Forward", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": 10}], "BuffWeight": [{"BuffId": "Rogue_Death_Lord_Walk_Front", "BuffStack": 3, "Weight": 9999}], "InHurt": 3, "MinActionCD": 0, "MaxActionCD": 5}, {"Id": "TurnRight_90", "BaseWeight": 5, "DegreeWeight": [{"MinRange": "-180", "MaxRange": "45", "Weight": "-99"}, {"MinRange": "45", "MaxRange": "135", "Weight": "5"}], "MinActionCD": 1, "MaxActionCD": 7}, {"Id": "TurnRight_180", "BaseWeight": 5, "DegreeWeight": [{"MinRange": "-180", "MaxRange": "90", "Weight": "-99"}, {"MinRange": "135", "MaxRange": "180", "Weight": "5"}], "MinActionCD": 1, "MaxActionCD": 7}, {"Id": "TurnLeft_90", "BaseWeight": 5, "DegreeWeight": [{"MinRange": "-45", "MaxRange": "180", "Weight": "-99"}, {"MinRange": "-135", "MaxRange": "-45", "Weight": "5"}], "MinActionCD": 1, "MaxActionCD": 7}, {"Id": "TurnLeft_180", "BaseWeight": 5, "DegreeWeight": [{"MinRange": "-90", "MaxRange": "180", "Weight": "-99"}, {"MinRange": "-180", "MaxRange": "-135", "Weight": "5"}], "MinActionCD": 1, "MaxActionCD": 7}]}]}