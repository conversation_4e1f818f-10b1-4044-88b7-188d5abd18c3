{"RoleCreation": [{"Type说明": [{"Id": "id", "Sex": "男的还是女的", "BpPath": "蓝图路径", "FolderName": "文件夹名称，和 ClassId 一起拼成一个文件夹路径 <Class.json.Id>_<Male/Female>", "OptionalClass": ["可选职业"]}], "Types": [{"Id": "TypeA", "Sex": "Female", "BpPath": "Core/Characters/StandardHuman/Human_Player", "FolderName": "Female", "OptionalClass": ["<PERSON><PERSON><PERSON>", "Swordsman", "BladeDancer", "Warrior"]}, {"Id": "TypeB", "Sex": "Male", "BpPath": "Core/Characters/StandardHuman/Player_Human_Man", "FolderName": "Male", "OptionalClass": ["<PERSON><PERSON><PERSON>", "Swordsman", "BladeDancer", "Warrior"]}]}]}