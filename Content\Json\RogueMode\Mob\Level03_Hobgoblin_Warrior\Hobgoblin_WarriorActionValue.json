{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "<PERSON>_<PERSON><PERSON><PERSON>blin_Warrior", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 5}, {"MinRange": 300, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "MinWaitCD": 0.1, "MaxWaitCD": 0.5, "MinActionCD": 4, "MaxActionCD": 9}, {"Id": "Action_Stare01", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 9}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Walk_Left", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 2}, {"MinRange": 400, "MaxRange": 1000, "Weight": 0}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "Front": 1, "Back": 1, "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 14}, {"Id": "Walk_Right", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 2}, {"MinRange": 400, "MaxRange": 1000, "Weight": 0}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "Front": 1, "Back": 1, "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 14}]}]}