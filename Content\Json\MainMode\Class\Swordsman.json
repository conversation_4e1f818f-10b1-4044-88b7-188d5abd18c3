{"Class": [{"说明": "剑盾战士", "Id": "Swordsman", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "BladeDancer", "<PERSON><PERSON><PERSON>"], "Buffs": ["Warrior_Passive", "Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "SwordShield_Move", "UnArmed": "SwordShield_Unarmed_Move"}, "Flying": {"Armed": "SwordShield_Move", "UnArmed": "SwordShield_Unarmed_Move"}, "Falling": {"Armed": "SwordShield_Fall", "UnArmed": "SwordShield_Unarmed_Fall"}, "Attached": {"Armed": "SwordShield_Ride", "UnArmed": "SwordShield_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "SwordShield_Hurt", "UnArmed": "SwordShield_Hurt"}, "Blow": {"Armed": "SwordShield_Blow", "UnArmed": "SwordShield_Blow"}, "Frozen": {"Armed": "SwordShield_Frozen", "UnArmed": "SwordShield_Frozen"}, "Bounced": {"Armed": "SwordShield_Bounced", "UnArmed": "SwordShield_Bounced"}, "Dead": {"Armed": "SwordShield_Dead", "UnArmed": "SwordShield_Dead"}, "Landing": {"Armed": "SwordShield_JustFall", "UnArmed": "SwordShield_Unarmed_JustFall"}, "SecondWind": {"Armed": "SwordShield_SecWind", "UnArmed": "SwordShield_SecWind"}, "GetUp": {"Armed": "SwordShield_RevivedOnSecWind", "UnArmed": "SwordShield_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "SwordShield", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "ChangeToSwordsman", "ClassBuff": [], "Actions": [{"Line": "_______________________________剑盾徒手基础动作________________________________"}, {"说明": "剑盾徒手走路站立", "Id": "SwordShield_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge", "SwordShield_Aim", "SwordShield_DrawWeapon", "Unarm_UseItem", "SwordShield_DrawAttack", "Interactive"], "1": ["SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge", "SwordShield_Aim", "SwordShield_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "剑盾徒手起跳", "Id": "SwordShield_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "SwordShield_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "剑盾徒手翻滚", "Id": "SwordShield_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"说明": "剑盾徒手下落", "Id": "SwordShield_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "SwordShield_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/UnarmedFall"]}, "Priority": 1}, {"说明": "剑盾徒手下落着地", "Id": "SwordShield_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "SwordShield_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "剑盾收刀", "Id": "SheathSwordShield", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "SwordShield_SheathWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/SheathWeapon"]}}, {"说明": "剑盾拔刀", "Id": "DrawSwordShield", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "SwordShield_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/DrawWeapon"]}}, {"Line": "_______________________________剑盾(LevelSquencer)动作________________________________"}, {"说明": "剑盾趴地上_LevelSquencer用", "Id": "FallDown_Loop", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_SwordShield", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Swordsman_FallDown_Loop"]}}, {"说明": "剑盾趴地上起来_LevelSquencer用", "Id": "FallDown_End", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_SwordShield", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Swordsman_FallDown_End"]}}, {"说明": "剑盾_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________剑盾(持武器)基础动作________________________________"}, {"Id": "SwordShield_Move", "Cmds": ["SwordShield_Move"], "Tags": [{"Tag": "SwordShield_Move", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"], "1": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/Defense", "ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/Defense"]}}, {"Id": "SwordShield_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_HurtCounter"], "1": ["SwordShield_Dodge"], "2": ["SwordShield_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Hurt_Air"]}}, {"Id": "SwordShield_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_QS_B"], "1": ["SwordShield_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/Blow_Front"]}}, {"Id": "SwordShield_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "SwordShield_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "SwordShield_Jump", "From": 0}, {"Tag": "SwordShield_Dodge", "From": 0}, {"Tag": "SwordShield_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "SwordShield_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"], "1": ["SwordShield_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Swordsman_<Type>/Fall"]}, "Priority": 1}, {"Id": "SwordShield_JustFall", "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump", "SwordShield_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "SwordShield_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"Id": "SwordShield_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 800}, "InitAction": true}, {"Id": "SwordShield_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 1200}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "SwordShield_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "SwordShield_QS_B", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Dodge", "SwordShield_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 1600}}, {"说明": "受身动作前翻", "Id": "SwordShield_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "SwordShield_QS_F", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Dodge", "SwordShield_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 1600}}, {"说明": "倒地动作", "Id": "SwordShield_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "SwordShield_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________剑盾特殊动作________________________________"}, {"Id": "SwordShield_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Jump/AttachOnTarget"]}}, {"Id": "SwordShield_Ride", "Cmds": [], "Tags": [{"Tag": "SwordShield_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["SwordShield_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________剑盾受击(特殊)动作________________________________"}, {"Id": "SwordShield_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________剑盾基础(附加)动作________________________________"}, {"说明": "剑与盾盾", "Id": "ChangeToSwordsman", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/ChangeToSwordsman1"]}}, {"说明": "剑盾弹刀动作", "Id": "SwordShield_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/Bounced"]}}, {"说明": "瞄准动作", "Id": "SwordShield_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "SwordShield_Defense", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge"], "2": ["SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/PickUp"]}}, {"Line": "_______________________________剑盾命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "SwordShield_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "SwordShield_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/OrderBuddy/OrderBuddyMoveToTarget"]}}, {"Line": "_______________________________剑盾战斗动作_______________________________"}, {"Line": "_______________________________剑盾_普攻_地面Action1_______________________________"}, {"Id": "Swordsman_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_LAttack1", "From": 0}, {"Tag": "SwordShield_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_LAttack2"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"], "3": ["SwordShield_BranchAttack2"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Slash0"]}, "InitAction": true}, {"Id": "Swordsman_LAttack02", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack3"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Slash1"]}, "InitAction": true}, {"Id": "Swordsman_LAttack03", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack1"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Slash2"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击2", "Id": "Swordsman_BranchAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_BranchAttack3"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_SlashB1"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击3", "Id": "Swordsman_BranchAttack3", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_BranchAttack4"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_SlashB2"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击4", "Id": "Swordsman_BranchAttack4", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_BranchAttack5"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_SlashB3"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击5", "Id": "Swordsman_BranchAttack5", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_BranchAttack5", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_SlashB4"]}, "InitAction": true}, {"Line": "_______________________________剑盾_普攻_空中Action1_______________________________"}, {"Id": "Swordsman_AirAttack1", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirAttack1", "From": 0}, {"Tag": "SwordShield_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirAttack2", "SwordShield_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/A_Slash0"]}, "InitAction": true}, {"Id": "Swordsman_AirAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirAttack1"], "1": ["_"], "2": ["SwordShield_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________剑盾_技能A_地面Action2_______________________________"}, {"Id": "Swordsman_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_RiseSlash"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Id": "Swordsman_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "SwordShield_HurtCounter", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_RiseSlash_Counter"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"Id": "Swordsman_RiseComboSlash", "Cmds": ["_"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Rise2Slash"]}, "InitAction": true, "Cost": {"MP": 1200}}, {"Line": "_______________________________剑盾_技能A_空中Action2_______________________________"}, {"Id": "Swordsman_AirDownSlashAttack1", "Cmds": ["Action2"], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/A_DashSlash_FallDown"]}, "InitAction": true, "Cost": {"MP": 300}}, {"Line": "_______________________________剑盾_技能B_地面Action3_______________________________"}, {"Id": "Swordsman_DashSlash", "Cmds": ["Action3"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_DashAttack2", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_DashSlash"]}, "Cost": {"MP": 1200}}, {"Id": "Swordsman_DashSlash2", "Cmds": ["Action3"], "Tags": [{"Tag": "SwordShield_DashAttack2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_DashSlash1"]}, "Cost": {"MP": 1200}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Swordsman_DashSpike_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_DashSpike_Combo"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Swordsman_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_RiseSlash"]}}, {"Line": "_______________________________剑盾_技能B_空中Action3_______________________________"}, {"Id": "Swordsman_AirDashSting", "Cmds": ["Action3"], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/A_DashSting_Forward"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Line": "_______________________________剑盾_技能C(防御)_地面Action4_______________________________"}, {"Id": "Swordsman_Defense", "Cmds": ["Action4"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "SwordShield_Defense", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense"]}}, {"Line": "_______________________________剑盾_防御_动作_______________________________"}, {"说明": "防御成功", "Id": "Swordsman_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "MontageAnimPickFunc.GetActionByPriorityDistance(4)", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_Success_Big", "ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_Success_Small"]}, "Cost": {"SP": 1800, "ReplaceAction": "Swordsman_Defense_Success_Broken"}}, {"说明": "防御成功_但体力不足_破防", "Id": "Swordsman_Defense_Success_Broken", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_Success_Broken"]}}, {"说明": "完美防御_成功(justblock)", "Id": "Swordsman_Defense_JustBlock", "Cmds": [], "Tags": ["SwordShield_JustBlock"], "BeCancelledTags": {"0": ["SwordShield_JustBlock_Success_Attack"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_JustBlock_Success"]}}, {"说明": "*完美防御_成功的后续动作1", "Id": "Swordsman_Defense_JustBlock_Success_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_JustBlock_ShieldStrike"]}}, {"说明": "*完美防御_成功的后续动作2", "Id": "Swordsman_Defense_JustBlock_Success_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "SwordShield_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_JustBlock_ShieldUpperStrike"]}}, {"说明": "*完美防御_成功的后续动作3", "Id": "Swordsman_Defense_JustBlock_Success_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "SwordShield_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_JustBlock_DashSlash"]}}, {"说明": "*完美防御_成功的后续动作4", "Id": "Swordsman_Defense_JustBlock_Success_Attack4", "Cmds": ["Action4"], "Tags": [{"Tag": "SwordShield_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_JustBlock_GroundSmash"]}}, {"说明": "防御时Action1的动作", "Id": "Swordsman_Defense_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_Defense_Attack1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense_Attack1_2", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_ShieldSweapSmash0"]}, "InitAction": true}, {"说明": "防御时Action1的连招动作", "Id": "Swordsman_Defense_Attack1_2", "Cmds": ["Action1"], "Tags": [{"Tag": "SwordShield_Defense_Attack1_2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "Swordsman_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_ShieldSweapSmash1"]}, "InitAction": true}, {"说明": "防御时Action2的动作", "Id": "Swordsman_Defense_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "SwordShield_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "SwordShield_Defense_Attack1", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_ShieldUpperSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action3的动作", "Id": "Swordsman_Defense_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "SwordShield_Defense_Attack3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_ShieldChargeSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action2的动作", "Id": "Swordsman_Defense_Attack2B", "Cmds": [], "Tags": [{"Tag": "SwordShield_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_CounterSlash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时，Acion2的完美防御成功了就自动变成这个了(仅用于justblock)", "Id": "Swordsman_Defense_CounterSlash_JustBlock", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/G_Defense_CounterSlash_JustBlock3"]}}, {"Line": "_______________________________剑盾_技能C_空中Action4_______________________________"}, {"Id": "Swordsman_AirDownShieldSmash", "Cmds": ["Action4"], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Attack/SwordShield/A_ShieldSmash_FallDown"]}, "InitAction": true, "Cost": {"MP": 1500}}]}], "Buff": [], "Aoe": []}