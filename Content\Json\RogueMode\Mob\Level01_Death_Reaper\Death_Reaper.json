{"Mob": [{"Id": "Rogue_Death_Reaper", "MobRank": "Elite", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "Death_Reaper_MoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Death_Reaper/Death_Reaper", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Death_Reaper", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 5000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 6000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 16, "PAtk": 11, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "Death_Reaper_ForceTaunt", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 3000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 10000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Standard_Move", "UnArmed": "Standard_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Death_Reaper/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Hit/Break_Down"]}, "InitAction": true}, {"说明": "近距离三连击", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "黑暗冲击", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/NormalAttack_M1"]}, "InitAction": true}, {"说明": "死亡之握", "Id": "NormalAttack_M2", "Cmds": ["NormalAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/NormalAttack_M2"]}, "InitAction": true}, {"说明": "暗月收割", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/NormalAttack_L1"]}, "InitAction": true}, {"说明": "前瞬步", "Id": "Dodge_DashStep_Front", "Cmds": ["Dodge_DashStep_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "挑衅动作", "Id": "<PERSON>_Taunt_Back", "Cmds": ["<PERSON>_Taunt_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/Dodge_Taunt_Back"]}, "InitAction": true}, {"说明": "发呆01", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Reaper/Battle/Action_Stare01"]}, "InitAction": true}]}], "Buff": [{"Id": "Death_Reaper_GhostHand_SecondBlow", "Tag": ["Death_Reaper"], "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(<PERSON>_Reaper_DelayAOE,ArtResource/ProjectRogue/VFX/ParagonZ/P_GhostHand_Hold_Loop_Purple,Mesh,false,1)"], "OnRemoved": ["BuffUtils.CreateAOEOnRemove(Death_Reaper_GhostHand_SecondAOE,0.2)", "BuffUtils.StopVFXOnCha_Remove(Death_Reaper_DelayAOE)"]}, {"Id": "Death_Reaper_ForceTaunt", "Tag": ["Death_Reaper"], "Priority": 0, "MaxStack": 99, "OnBeHurt": ["BuffUtils.AddStackOnBeHurt()"]}], "Bullet": [{"Id": "Death_Reaper_GhostHand", "Tag": [], "BpPath": "Core/Item/Monster/Death_Reaper/Death_Reaper_GhostHand", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 20, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}], "AOE": [{"Id": "Death_Reaper_GhostHand_UpAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Reaper/Death_Reaper_GhostHand_UpAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.8,0)", "AOEScript.DealMoveVelocityToAoeCasterOnTouch(-500,0,150,ToMontageState,KnockOut,8,0.7)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Reaper_GhostHand_SecondAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Reaper/Death_Reaper_GhostHand_SecondAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.8,0)", "AOEScript.DealMoveVelocityToAoeCasterOnTouch(-500,0,300,ToMontageState,KnockOut,8,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Reaper_DarkImpact_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Reaper/Death_Reaper_DarkImpact_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2,0)", "AOEScript.DealMoveVelocityToAoeCasterOnTouch(-500,0,200,ToMontageState,KnockOut,8,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}]}