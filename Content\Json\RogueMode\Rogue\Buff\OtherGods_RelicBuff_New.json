{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中概率引发额外突刺", "Id": "Anim_Pierce<PERSON>it", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.DoExtraOffense(0.2,3,<PERSON><PERSON><PERSON><PERSON><PERSON>,Physical)"]}, {"说明": "动画中造成的break上升", "Id": "Anim_BreakUp", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "OnHit": ["BuffUtils.BreakDamageUp(0.05)"]}, {"分割": "-------------------------------------------Other-----------------------------------------"}, {"说明": "攻击时产生一个剑气", "Id": "AttackCreateBulletOnSocket", "Tag": ["CreateBullet", "Rogue_Slash", "NotSave", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Bullet_SwordLight,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"]}, {"说明": "突刺攻击时刻产生额外距离Aoe", "Id": "Rogue_Pierce1_Effect", "Tag": ["Hit", "NotSave", "<PERSON><PERSON><PERSON>", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(<PERSON>_<PERSON><PERSON>_<PERSON>,1,<PERSON>_<PERSON>et)"]}, {"说明": "突刺攻击命中时额外造成伤害", "Id": "Rogue_Pierce2_Effect", "Tag": ["NotSave", "Rogue_Pierce_State", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_PierceHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_PierceHit,1,0,true)"]}, {"说明": "造成伤害时x%概率给对方一个会受到额外伤的DeBuff", "Id": "BloodlyHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,<PERSON>_<PERSON>ly,1,8,true,false)"]}, {"说明": "造成击杀时回复1点生命", "Id": "HealHpOnKill", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 20, "TickTime": 0, "OnKill": ["RogueBuff.HealHpOnKill(1)"]}, {"说明": "造成反击命中时回复1点生命", "Id": "HealHpOnCounter", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 20, "TickTime": 0, "OnHit": ["RogueBuff.HealHpOnCounter(1)"]}, {"说明": "造成暴击时回复1点生命", "Id": "HealHpOnCrit", "Tag": ["Relic", "<PERSON>rit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnCrit": ["RogueBuff.HealHpOnCrit(1)"]}, {"说明": "造成暴击时回复充能", "Id": "RecoverItemOnCrit", "Tag": ["Relic", "<PERSON>rit", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0, "OnCrit": ["RogueBuff.RecoverItemOnCrit(0.01)"]}, {"说明": "受到伤害时候+1金币", "Id": "<PERSON><PERSON><PERSON>", "Tag": ["Relic", "Hurted", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.GiveRogueCurrencyOnHurt(Rogue_Coin,1)"]}, {"说明": "击杀怪物时增加主动道具充能", "Id": "AddItemRecoverOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnKill": ["RogueBuff.AddItemRecoverOnKill(0.01)"]}, {"说明": "击杀精英怪或Boss时永久加生命上限", "Id": "LifeOfKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["RogueBuff.AddMaxLifByKillOnOccur(5,999999)"], "OnKill": ["RogueBuff.AddMaxLifeOnKill(Elite,5,999999)"]}, {"说明": "击杀敌人时候获得3s的加速25%的Buff", "Id": "Rogue_Add30SpeedOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.AddBuffOnKill(Rogue_SpeedUpOnKill_01,3000,3,false,true)"]}, {"说明": "击杀敌人时候获得3s的加速15%的Buff", "Id": "Rogue_Add20SpeedOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.AddBuffOnKill(Rogue_SpeedUpOnKill_01,2000,3,false,true)"]}, {"说明": "肉鸽击杀后速度提高1（最高50%）", "Id": "Rogue_SpeedUpOnKill_01", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽击杀后10%概率生成范围治疗", "Id": "Rogue_CreateHealAoeOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.ChanceCreateAoeOnKill(0.1,<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5,root)"]}, {"说明": "身上每有一个圣遗物，全攻击力增加0.01%，上限300%约等于没有上限", "Id": "Rogue_RelicCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToAttackPercent(1,30000)"]}, {"说明": "身上每有一种神的圣遗物，全攻击力增加0.01%", "Id": "Rogue_RelicGodCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToAttackPercent(1)"]}, {"说明": "身上每有一块钱，全攻击力增加0.01%", "Id": "Rogue_MoneyCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastCoinNumToAttackPercent(1)"]}, {"说明": "身上每有一个圣遗物，暴击率增加0.01%，上限100%（约等于没有上限", "Id": "Rogue_RelicCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalChance(0.0001,1)"]}, {"说明": "身上每有一种神的圣遗物，暴击率增加0.01%", "Id": "Rogue_RelicGodCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalChance(0.0001)"]}, {"说明": "身上每有一个圣遗物，暴击倍率增加0.01%，上限200%", "Id": "Rogue_RelicCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalRate(0.0001,2)"]}, {"说明": "身上每有一种神的圣遗物，暴击率增加0.01%", "Id": "Rogue_RelicGodCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalRate(0.0001)"]}, {"说明": "身上每有一个圣遗物，最大生命增加1，上限100（约等于没有上限", "Id": "Rogue_CastRelicNumToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicNumToMaxHp(1,100)"], "OnTick": ["RogueBuff.CastRelicNumToMaxHp(1,100)"]}, {"说明": "身上每有一种神的圣遗物，最大生命增加1", "Id": "Rogue_CastRelicGodTagToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicGodTagToMaxHp(1)"], "OnTick": ["RogueBuff.CastRelicGodTagToMaxHp(1)"]}, {"说明": "肉鸽主动道具充能时,提升暴击率", "Id": "Rogue_CriticalChanceUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalChanceUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "肉鸽主动道具充能时,提升暴击倍率", "Id": "Rogue_CriticalRateUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalRateUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "敌人没有或break值低于50%的时候暴击率提升1%", "Id": "敌人没有或break值低于0%（breakdown时间延长故条件收紧）的时候暴击率提升1%", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.LowBreakEnemyCriticalChanceUp(0.5,0.0001)"]}, {"说明": "对生命值低于30%的敌人造成的伤害提升1%", "Id": "Rogue_DamageUpToLowHPEnemy", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.DamageUpToLowHpEnemy(0.3,0.01)"]}, {"说明": "每过n秒没有直接攻击,则下次攻击造成的伤害+1%", "Id": "Rogue_TimeIntervalDamageUp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.TimeIntervalDamageUp(3,0.01)"]}, {"描述": "-------------------------------------XXX后提升------------------------------------------------"}, {"说明": "肉鸽动作后速度提高", "Id": "Rogue_SpeedUpOnAction", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽动作后攻击力提高", "Id": "Rogue_AttackUpOnAction", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "动作后,角色造成的直接动作伤害提高,限制层数", "Id": "Rogue_AfterDirectDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 100, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "肉鸽使用法器后速度提高", "Id": "Rogue_SpeedUpOnUseItem", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 10000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "使用法器后，添加攻击速度提升30%的Buff", "Id": "UseItem_SpeedUp01", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem,3000,3,false,true)"]}, {"说明": "使用法器后，添加攻击速度提升20%的Buff", "Id": "UseItem_SpeedUp02", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem,2000,3,false,true)"]}, {"说明": "使用法器后，添加攻击速度提升10%的Buff", "Id": "UseItem_SpeedUp03", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem,1000,3,false,true)"]}, {"说明": "技能槽1后,角色造成的直接动作伤害提高,限制层数", "Id": "Rogue_Skill_1_DirectDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 50, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "技能槽2后,角色造成的直接动作伤害提高,限制层数", "Id": "Rogue_Skill_2_DirectDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 50, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "技能槽1后,角色动作速度提高", "Id": "Rogue_Skill_1_SpeedUpLimited", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 4000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "技能槽2后,角色动作速度提高", "Id": "Rogue_Skill_2_SpeedUpLimited", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 4000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "技能1后，添加直接伤害增强20%的Buff,同种至多叠加50%", "Id": "Skill_1_AddDirectionDamage01", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,20,15,false,true)"]}, {"说明": "技能1后，添加直接伤害增强30%的Buff,同种至多叠加50%", "Id": "Skill_1_AddDirectionDamage02", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,30,15,false,true)"]}, {"说明": "技能1后，添加直接伤害增强50%的Buff,同种至多叠加50%", "Id": "Skill_1_AddDirectionDamage03", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,50,15,false,true)"]}, {"说明": "技能2后，添加直接伤害增强20%的Buff,同种至多叠加50%", "Id": "Skill_2_AddDirectionDamage01", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,20,15,false,true)"]}, {"说明": "技能2后，添加直接伤害增强30%的Buff,同种至多叠加50%", "Id": "Skill_2_AddDirectionDamage02", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,30,15,false,true)"]}, {"说明": "技能2后，添加直接伤害增强50%的Buff,同种至多叠加50%", "Id": "Skill_2_AddDirectionDamage03", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_DirectDamageUpLimited,50,15,false,true)"]}, {"说明": "技能1后，添加速度增强10%的Buff,同种至多叠加50%", "Id": "Skill_1_AddSpeed01", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_SpeedUpLimited,1000,5,false,true)"]}, {"说明": "技能1后，添加速度增强20%的Buff,同种至多叠加50%", "Id": "Skill_1_AddAddSpeed02", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_SpeedUpLimited,2000,5,false,true)"]}, {"说明": "技能1后，添加速度增强40%的Buff,同种至多叠加40%", "Id": "Skill_1_AddAddSpeed03", "Tag": ["Relic", "NotSave", "Skill01_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_1_SpeedUpLimited,4000,5,false,true)"]}, {"说明": "技能2后，添加速度增强40%的Buff,同种至多叠加40%", "Id": "Skill_2_AddAddSpeed01", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_2_SpeedUpLimited,1000,5,false,true)"]}, {"说明": "技能2后，添加速度增强40%的Buff,同种至多叠加40%", "Id": "Skill_2_AddAddSpeed02", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_2_SpeedUpLimited,2000,5,false,true)"]}, {"说明": "技能2后，添加速度增强40%的Buff,同种至多叠加40%", "Id": "Skill_2_AddAddSpeed03", "Tag": ["Relic", "NotSave", "Skill02_End"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_Skill_2_SpeedUpLimited,4000,5,false,true)"]}]}