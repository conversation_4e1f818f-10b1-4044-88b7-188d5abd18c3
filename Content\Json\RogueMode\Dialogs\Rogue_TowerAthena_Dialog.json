{"Dialogs": [{"说明": "<PERSON>_<PERSON>初次对话", "Id": "Rogue_<PERSON><PERSON><PERSON><PERSON>_Start_Dialog", "FirstClip": "FirstDialog", "Conditions": ["DialogCondition.CheckSurvivorSwitchOff(TowerAthena_Start_Dialog)"], "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_Veryfirst_StartDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_Veryfirst_StartDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_Veryfirst_StartDialog3"}], "Selections": [{"说明": "开始幸存者模式", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Survivor_Start_SelectionDialog", "NextEventFunc": "", "Actions": ["DialogAction.SurvivorBattleStart()"]}, {"说明": "其他说明", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "", "Actions": []}]}], "EndDialogScript": ["DialogAction.SetSurvivorSwitchOn(TowerAthena_Start_Dialog)"]}, {"说明": "到达场地中心时对话", "Id": "Rogue_TowerAthena_DialogAtCrosswise", "FirstClip": "FirstDialog", "Conditions": ["DialogCondition.CheckSurvivorSwitchOff(TowerAthena_DialogAtCrosswise)"], "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_<PERSON><PERSON><PERSON>_CrossWiseDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_<PERSON><PERSON><PERSON>_CrossWiseDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "TowerAthena", "Text": "Survivor_<PERSON><PERSON><PERSON>_CrossWiseDialog3"}], "Selections": [{"说明": "开始幸存者模式", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Survivor_Selection_GoUpstairs", "NextEventFunc": "", "Actions": []}]}], "EndDialogScript": ["DialogAction.SetSurvivorSwitchOn(TowerAthena_DialogAtCrosswise)"]}]}