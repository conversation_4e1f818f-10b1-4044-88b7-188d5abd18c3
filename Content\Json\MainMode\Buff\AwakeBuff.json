{"说明": "觉醒技能相关的Buff", "Buff": [{"说明": "造成伤害产生吸血", "Id": "Awake_BloodlyThirsty", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnOccur": ["AwakeBuff.ActiveAwakeSkill(BloodlyThirsty)"], "OnHit": ["AwakeBuff.BloodlyThirsty(0.5)"], "OnTick": ["AwakeBuff.CheckAwakeBuffCost()"], "OnRemoved": ["AwakeBuff.CloseAwakeSkill(BloodlyThirsty)"]}, {"说明": "周期性产生地震波", "Id": "Awake_Earthquake", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnOccur": ["AwakeBuff.ActiveAwakeSkill(Earthquake)"], "OnTick": ["AwakeBuff.CheckAwakeBuffCost()", "BuffUtils.CreateAoeOnCarrier(Aoe_Awake_EarthQuakeWave,0.35,0.35,Root)"], "OnRemoved": ["AwakeBuff.CloseAwakeSkill(Earthquake)"]}]}