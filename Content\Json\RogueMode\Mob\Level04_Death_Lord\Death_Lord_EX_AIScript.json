{"AIScript": [{"说明": "转阶段1", "Id": "Death_Lord_EX_ChangeStage", "Condition": ["MobAIScript.CheckRogueMobHpLessThanBuffStack(Rogue_Boss_CheckSecondStage)", "MobAIScript.CheckHasBuff(Rogue_Boss_FirstStage)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageState)"]}, {"说明": "转阶段2", "Id": "Death_Lord_EX_ChangeStage2", "Condition": ["MobAIScript.CheckRogueMobHpLessThanBuffStack(Rogue_Boss_CheckThirdStage)", "MobAIScript.CheckHasBuff(Rogue_Boss_SecondStage)"], "OnReady": [], "Action": ["MobAIScript.CheckCenterPointThenDoAction(CenterPoint,RageAttack_X2_PreCast)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "Death_Lord_EX_TurnToStimulate", "Condition": ["MobAIScript.CheckPlayerInRange(200,99999)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}]}