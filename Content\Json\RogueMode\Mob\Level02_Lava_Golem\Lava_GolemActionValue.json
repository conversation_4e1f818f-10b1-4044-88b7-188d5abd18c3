{"RogueMobActionValue": [{"Id": "Rogue_Lava_Golem", "Actions": [{"说明": "发呆", "Id": "Action_Stare01", "BaseWeight": 0, "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 9999}], "MinActionCD": 3, "MaxActionCD": 5}, {"说明": "近战攻击", "Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 4, "MaxActionCD": 6}, {"说明": "燃烧践踏", "Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"说明": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "3"}, {"说明": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "3"}, {"说明": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "3"}, {"说明": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "3"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 6, "MaxActionCD": 8}, {"说明": "地震熔岩", "Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 12, "MaxActionCD": 16}, {"说明": "烈焰扫射", "Id": "NormalAttack_S5", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": -99}, {"MinRange": 500, "MaxRange": 1000, "Weight": 9}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "3"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "InAir": 1, "MinActionCD": 30, "MaxActionCD": 40}, {"说明": "爆射炎弹", "Id": "NormalAttack_S4", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 9}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}], "InAir": 2, "InDodge": 3, "MinActionCD": 8, "MaxActionCD": 12}, {"说明": "岩浆跃进", "Id": "NormalAttack_S8", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 1500, "Weight": 9}, {"MinRange": 1500, "MaxRange": 3000, "Weight": 18}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 8, "MaxActionCD": 14}, {"说明": "向前走路", "Id": "Walk_Front", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 9}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "InHurt": 3, "MinActionCD": 6, "MaxActionCD": 8}, {"说明": "左转90", "Id": "TurnLeft_90", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "5"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "左转180", "Id": "TurnLeft_180", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "9"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "右转90", "Id": "TurnRight_90", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "5"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "右转180", "Id": "TurnRight_180", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "9"}], "BuffWeight": [{"BuffId": "Rogue_Boss_SecondStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "近战攻击EX", "Id": "RageAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "-45", "MaxRange": "45", "Weight": "4"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 4, "MaxActionCD": 6}, {"说明": "燃烧践踏EX", "Id": "RageAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"说明": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "3"}, {"说明": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "3"}, {"说明": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "3"}, {"说明": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "3"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 22, "MaxActionCD": 25}, {"说明": "地震熔岩", "Id": "RageAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 25, "MaxActionCD": 30}, {"说明": "地震熔岩", "Id": "RageAttack_S3_2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 9}, {"MinRange": 400, "MaxRange": 800, "Weight": 5}, {"MinRange": 800, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "OnGround": 3, "MinActionCD": 35, "MaxActionCD": 50}, {"说明": "爆射炎弹EX", "Id": "RageAttack_S4", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 3000, "Weight": 9}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}, {"BuffId": "Rogue_CheckPlayerFarAway", "BuffStack": 15, "Weight": 3}], "InAir": 2, "InDodge": 3, "MinActionCD": 10, "MaxActionCD": 14}, {"说明": "熔岩咆哮", "Id": "RageAttack_S7", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "InAir": 3, "MinActionCD": 30, "MaxActionCD": 40}, {"说明": "火山陨落", "Id": "RageAttack_S6", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": -9999}, {"MinRange": 200, "MaxRange": 1000, "Weight": 0.1}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -9999}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 30, "MaxActionCD": 40}, {"说明": "岩浆跃进（EX）", "Id": "RageAttack_S8", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 1500, "Weight": 9}, {"MinRange": 1500, "MaxRange": 3000, "Weight": 18}, {"MinRange": 3000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 18, "MaxActionCD": 24}, {"说明": "左转90", "Id": "Turn_Fast_Left_90", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "5"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "左转180", "Id": "Turn_Fast_Left_180", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "9"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "右转90", "Id": "Turn_Fast_Right_90", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "5"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}, {"说明": "右转180", "Id": "Turn_Fast_Right_180", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 99999, "Weight": 0.1}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "-99"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "9"}], "BuffWeight": [{"BuffId": "Rogue_Boss_FirstStage", "BuffStack": 1, "Weight": -9999}], "MinActionCD": 2, "MaxActionCD": 3}]}]}