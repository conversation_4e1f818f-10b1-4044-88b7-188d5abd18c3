{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个风切", "Id": "Anim_WindOfZantia_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Wind,1,10,true,false)"]}, {"说明": "动画中引发风蚀", "Id": "Anim_WindHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.25,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON><PERSON><PERSON>,<PERSON>_WindErosion,1,1,1,true,false,true,<PERSON>_Aoe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0,0.25,<PERSON>_<PERSON><PERSON>_Zantia2,1)"]}, {"说明": "动画中无消耗引发风蚀", "Id": "Anim_WindHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.25,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1,false)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON>_<PERSON>,<PERSON>_WindErosion,1,1,1,true,false,true,false,<PERSON>_<PERSON>oe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0,0.25,<PERSON>_<PERSON><PERSON>_Zantia2,1,false)"]}, {"说明": "动画中概率引发风蚀", "Id": "Anim_ChanceWindHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.25,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON><PERSON><PERSON>,<PERSON>_WindErosion,1,3,1,true,false,true,<PERSON>_<PERSON>oe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0,0.25,<PERSON>_<PERSON><PERSON>_Zantia2,1)"]}, {"说明": "动画中引发大旋风", "Id": "Anim_JustDodgeWind", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Zantia11,0.2,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeWind,0.7,0,true)"]}, {"分割": "-------------------------------------------Wind-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个风切", "Id": "WindOfZantia_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindOfZantia_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindOfZantia_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率引爆风切产生一次风蚀", "Id": "WindOfZantia_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceWindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceWindHit,1,0,true)"]}, {"说明": "下砸技能时，产生一道龙卷风，每秒向周遭的敌人进行一次攻击", "Id": "Smash_WindAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia4,2,<PERSON>)"]}, {"说明": "下砸技能时，被击中的目标引发“风蚀”效果。", "Id": "Smash_WindHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“风蚀”效果。", "Id": "Dash_WindCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "玩家蓄力时，在自身周围产生风盾，反伤，给敌人一层风切。", "Id": "Power_WindShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>Shield,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON>im_WindShield,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“风蚀”效果。", "Id": "Power_WindHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个小风环刃。", "Id": "WindDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Zantia10,1,<PERSON>)"]}, {"说明": "玩家完美闪避时，在敌人位置产生数道次元斩，并且给敌人一层“风切”。", "Id": "Anim_DogeCreateWindAoe", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Zantia11,0.55,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeWind,0.7,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeWind,0.7,0,true)"]}, {"说明": "击飞技能时，击中有”风切“层数的单位时，引发“风蚀”效果。", "Id": "Rise_WindCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "对空中敌人造成的伤害提升30%,并且带有风切的敌人视为空中敌人", "Id": "Wind_Effect_14", "Tag": ["Relic", "NotSave", "WindCastSky"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["RogueBuff.DamageTimesUpToSkyOrWind(0.3)"]}, {"说明": "造成风元素伤害的时候概率对风元素的主动道具进行额外充能", "Id": "AddItemRecover_Wind", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Wind)"]}, {"说明": "每过TickTime在目标生成一个Aoe", "Id": "Launch_WindTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Zantia17,0,0.3,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON><PERSON>_<PERSON>", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Zantia18,0.5,Root)"]}]}