{"ActionCmd": [{"------": "-------------------- Rogue Game --------------------"}, {"Action": "NormalAttack", "ActionKey": ["RogueKey_NormalAttack"], "State": ["Game"]}, {"Action": "Ability1", "ActionKey": ["RogueKey_Ability1"], "State": ["Game"]}, {"Action": "Ability2", "ActionKey": ["RogueKey_Ability2"], "State": ["Game"]}, {"Action": "AwakeSkill", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["Game"]}, {"Action": "AwakeSkill_BloodlyThirsty", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["Game"]}, {"Action": "AwakeSkill_Earthquake", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["Game"]}, {"Action": "AwakeSkill_<PERSON><PERSON>rker", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["Game"]}, {"Action": "AwakeSkill_DragonSummon", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["Game"]}, {"Action": "Sprint", "ActionKey": ["RogueKey_Sprint"], "State": ["Game"]}, {"Action": "Jump", "ActionKey": ["Rogue<PERSON>ey_Jump"], "State": ["Game"]}, {"Action": "Dodge", "ActionKey": ["Rogue<PERSON>ey_Dodge"], "State": ["Game"]}, {"Action": "PauseMenu", "ActionKey": ["RogueKey_PauseMenu", "RogueKey_Share"], "State": ["Game"]}, {"Action": "ManualMenu", "ActionKey": ["RogueKey_ManualMenu"], "State": ["Game"]}, {"Action": "ResetCamera", "ActionKey": ["RogueKey_ResetCamera"], "State": ["Game"]}, {"Action": "Aim", "ActionKey": ["RogueKey_Aim"], "State": ["Game"]}, {"Action": "ToggleWeapon", "ActionKey": ["RogueKey_ToggleWeapon"], "State": ["Game"]}, {"Action": "Interactive", "ActionKey": ["RogueKey_Interactive"], "State": ["Game"]}, {"Action": "DrinkPotion", "ActionKey": ["RogueKey_DrinkPotion"], "State": ["Game"]}, {"Action": "UseItem", "ActionKey": ["RogueKey_UseItem"], "State": ["Game"]}, {"Action": "UseItem2", "ActionKey": ["RogueKey_UseItem2"], "State": ["Game"]}, {"------": "-------------------- Rogue Game 显示Icon用，不要做为实际 Action 用--------------------"}, {"Action": "Move", "ActionKey": ["Key_UI_LeftStick"], "State": ["Game"]}, {"Action": "MoveCamera", "ActionKey": ["Key_UI_Rightstick"], "State": ["Game"]}, {"------": "-------------------- PauseMenu --------------------"}, {"Action": "PauseResume", "ActionKey": ["Key_Start"], "State": ["PauseMenu"]}, {"Action": "Menu_Up", "ActionKey": ["Key_Up"], "State": ["PauseMenu"]}, {"Action": "Menu_Down", "ActionKey": ["Key_Down"], "State": ["PauseMenu"]}, {"Action": "Menu_Left", "ActionKey": ["Key_Left"], "State": ["PauseMenu"]}, {"Action": "Menu_Right", "ActionKey": ["Key_Right"], "State": ["PauseMenu"]}, {"Action": "Menu_Confirm", "ActionKey": ["Key_Cross"], "State": ["PauseMenu"]}, {"Action": "<PERSON>u_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["PauseMenu"]}, {"------": "-------------------- ChangeSkill<PERSON>rClass --------------------"}, {"Action": "ChangeSkillOrClass_Up", "ActionKey": ["Key_Up"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Down", "ActionKey": ["Key_Down"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Left", "ActionKey": ["Key_Left"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Right", "ActionKey": ["Key_Right"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Confirm", "ActionKey": ["Key_Cross"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["ChangeSkillOrClass"]}, {"------": "-------------------- Dialog --------------------"}, {"Action": "StopDialog", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["Dialog"]}, {"Action": "DialogConfirm", "ActionKey": ["Key_Cross"], "State": ["Dialog"]}, {"Action": "DialogUp", "ActionKey": ["Key_Up"], "State": ["Dialog"]}, {"Action": "DialogDown", "ActionKey": ["Key_Down"], "State": ["Dialog"]}, {"------": "-------------------- MessageDialog --------------------"}, {"Action": "MessageDialog_Yes", "ActionKey": ["Key_Cross"], "State": ["MessageDialog"]}, {"Action": "MessageDialog_Cancel", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["MessageDialog"]}, {"------": "-------------------- NewbieHint --------------------"}, {"Action": "<PERSON><PERSON>_Confirm", "ActionKey": ["Key_Cross"], "State": ["NewbieHint"]}, {"Action": "<PERSON><PERSON>_Cancel", "ActionKey": ["Key_Eclipse"], "State": ["NewbieHint"]}, {"Action": "<PERSON><PERSON>_Left", "ActionKey": ["Key_Left"], "State": ["NewbieHint"]}, {"Action": "<PERSON>bie_Right", "ActionKey": ["Key_Right"], "State": ["NewbieHint"]}, {"------": "-------------------- Shopping --------------------"}, {"Action": "Shop_Up", "ActionKey": ["Key_Up"], "State": ["Shopping"]}, {"Action": "Shop_Down", "ActionKey": ["Key_Down"], "State": ["Shopping"]}, {"Action": "Shop_Left", "ActionKey": ["Key_Left"], "State": ["Shopping"]}, {"Action": "Shop_Right", "ActionKey": ["Key_Right"], "State": ["Shopping"]}, {"Action": "Shop_Confirm", "ActionKey": ["Key_Cross"], "State": ["Shopping"]}, {"Action": "Shop_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["Shopping"]}, {"------": "-------------------- Title --------------------"}, {"Action": "Title_Up", "ActionKey": ["Key_Up"], "State": ["Title"]}, {"Action": "Title_Down", "ActionKey": ["Key_Down"], "State": ["Title"]}, {"Action": "Title_Left", "ActionKey": ["Key_Left"], "State": ["Title"]}, {"Action": "Title_Right", "ActionKey": ["Key_Right"], "State": ["Title"]}, {"Action": "Title_Eclipse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["Title"]}, {"Action": "Title_Cross", "ActionKey": ["Key_Cross"], "State": ["Title"]}, {"------": "-------------------- Death --------------------"}, {"Action": "Death_Confirm", "ActionKey": ["Key_Cross"], "State": ["Death"]}, {"------": "-------------------- Sequence --------------------"}, {"Action": "<PERSON><PERSON>", "ActionKey": ["Key_UI_Action1"], "State": ["Sequence"]}, {"------": "-------------------- NewbieLevel --------------------"}, {"Action": "Newbie_NormalAttack", "ActionKey": ["RogueKey_NormalAttack"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_Ability1", "ActionKey": ["RogueKey_Ability1"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_Ability2", "ActionKey": ["RogueKey_Ability2"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_AwakeSkill", "ActionKey": ["RogueKey_AwakeSkill"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_Sprint", "ActionKey": ["RogueKey_Sprint"], "State": ["RogueNewbieLevel"]}, {"Action": "<PERSON><PERSON>_Jump", "ActionKey": ["Rogue<PERSON>ey_Jump"], "State": ["RogueNewbieLevel"]}, {"Action": "<PERSON><PERSON>_<PERSON>", "ActionKey": ["Rogue<PERSON>ey_Dodge"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_PauseMenu", "ActionKey": ["RogueKey_PauseMenu"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_ResetCamera", "ActionKey": ["RogueKey_ResetCamera"], "State": ["RogueNewbieLevel"]}, {"Action": "<PERSON><PERSON>_Aim", "ActionKey": ["RogueKey_Aim"], "State": ["RogueNewbieLevel"]}, {"Action": "<PERSON><PERSON>_ToggleWeapon", "ActionKey": ["RogueKey_ToggleWeapon"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_Interactive", "ActionKey": ["RogueKey_Interactive"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_DrinkPotion", "ActionKey": ["RogueKey_DrinkPotion"], "State": ["RogueNewbieLevel"]}, {"Action": "Newbie_UseItem", "ActionKey": ["RogueKey_UseItem"], "State": ["RogueNewbieLevel"]}, {"===============": "============================= Rougelike ============================================="}, {"------": "-------------------- SettingMain UI --------------------"}, {"Action": "MainRogueSetting_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueSettingMain"]}, {"Action": "MainRogueSetting_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueSettingMain"]}, {"Action": "MainRogueSetting_Left", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueSettingMain"]}, {"Action": "MainRogueSetting_Right", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueSettingMain"]}, {"Action": "MainRogueSetting_Back", "ActionKey": ["Key_UI_Close"], "State": ["RogueSettingMain"]}, {"Action": "MainRogueSetting_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueSettingMain"]}, {"------": "-------------------- Setting UI --------------------"}, {"Action": "RogueSetting_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Left", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Right", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Back", "ActionKey": ["Key_UI_Close"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Reset", "ActionKey": ["Key_UI_Action1"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_SwitchLeft", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_SwitchRight", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Apply", "ActionKey": ["Key_UI_Action2"], "State": ["RogueSetting"]}, {"Action": "RogueSetting_Cancel", "ActionKey": ["Key_UI_Close"], "State": ["RogueSetting"]}, {"------": "-------------------- SelectionRoom --------------------"}, {"Action": "SelectionRoom_Left", "ActionKey": ["Key_Left"], "State": ["SelectionRoom"]}, {"Action": "SelectionRoom_Right", "ActionKey": ["Key_Right"], "State": ["SelectionRoom"]}, {"Action": "SelectionRoom_Confirm", "ActionKey": ["Key_Cross"], "State": ["SelectionRoom"]}, {"------": "-------------------- AwakeSkill --------------------"}, {"Action": "AwakeSkill_Left", "ActionKey": ["Key_Left"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Right", "ActionKey": ["Key_Right"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Confirm", "ActionKey": ["Key_Cross"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["AwakeSkill"]}, {"------": "-------------------- RogueRewards --------------------"}, {"Action": "RogueRewards_Left", "ActionKey": ["Key_Left"], "State": ["RogueRewards"]}, {"Action": "RogueRewards_Right", "ActionKey": ["Key_Right"], "State": ["RogueRewards"]}, {"Action": "RogueRewards_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueRewards"]}, {"Action": "RogueRewards_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["RogueRewards"]}, {"------": "-------------------- RogueTalent --------------------"}, {"Action": "Talent_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueTalent"]}, {"Action": "Talent_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueTalent"]}, {"Action": "Talent_Left", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RogueTalent"]}, {"Action": "Talent_Right", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RogueTalent"]}, {"Action": "Talent_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueTalent"]}, {"Action": "Talent_Reset", "ActionKey": ["Key_UI_Action2"], "State": ["RogueTalent"]}, {"Action": "Talent_Close", "ActionKey": ["Key_UI_Close"], "State": ["RogueTalent"]}, {"------": "-------------------- RogueShop --------------------"}, {"Action": "RogueShop_Left", "ActionKey": ["Key_Left"], "State": ["RogueShop"]}, {"Action": "RogueShop_Right", "ActionKey": ["Key_Right"], "State": ["RogueShop"]}, {"Action": "RogueShop_Up", "ActionKey": ["Key_Up"], "State": ["RogueShop"]}, {"Action": "RogueShop_Down", "ActionKey": ["Key_Down"], "State": ["RogueShop"]}, {"Action": "RogueShop_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueShop"]}, {"Action": "RogueShop_Refresh", "ActionKey": ["Key_L1"], "State": ["RogueShop"]}, {"Action": "RogueShop_Refuse", "ActionKey": ["Key_UI_Close"], "State": ["RogueShop"]}, {"------": "-------------------- Rogue<PERSON>areer --------------------"}, {"Action": "RogueCareer_Left", "ActionKey": ["Key_Left"], "State": ["RogueCareer"]}, {"Action": "RogueCareer_Right", "ActionKey": ["Key_Right"], "State": ["RogueCareer"]}, {"Action": "RogueCareer_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueCareer"]}, {"Action": "RogueCareer_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["RogueCareer"]}, {"------": "-------------------- UpgradeHealingPotion --------------------"}, {"Action": "UpgradeHealingPotion_Left", "ActionKey": [""], "State": ["UpgradeHealingPotion"]}, {"Action": "UpgradeHealingPotion_Right", "ActionKey": [""], "State": ["UpgradeHealingPotion"]}, {"Action": "UpgradeHealingPotion_Confirm", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["UpgradeHealingPotion"]}, {"Action": "UpgradeHealingPotion_Confirm_2", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["UpgradeHealingPotion"]}, {"Action": "UpgradeHealingPotion_Refuse", "ActionKey": ["Key_UI_Close"], "State": ["UpgradeHealingPotion"]}, {"------": "-------------------- UpgradeActiveItem--------------------"}, {"Action": "UpgradeActiveItem_Confirm", "ActionKey": ["Key_L1"], "State": ["UpgradeActiveItem"]}, {"Action": "UpgradeActiveItem_Refuse", "ActionKey": ["Key_UI_Close"], "State": ["UpgradeActiveItem"]}, {"------": "-------------------- RogueSkillSelection --------------------"}, {"Action": "RogueSkillSelection_Left", "ActionKey": ["Key_Left"], "State": ["RogueSkillSelection"]}, {"Action": "RogueSkillSelection_Right", "ActionKey": ["Key_Right"], "State": ["RogueSkillSelection"]}, {"Action": "RogueSkillSelection_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueSkillSelection"]}, {"Action": "RogueSkillSelection_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["RogueSkillSelection"]}, {"------": "-------------------- Rogue<PERSON>rayerTargetSelection --------------------"}, {"Action": "RoguePrayerTargetSelection_Left", "ActionKey": ["Key_Left"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_Right", "ActionKey": ["Key_Right"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_SwitchLeft", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_SwitchRight", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_Confirm", "ActionKey": ["Key_Cross"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_Refuse", "ActionKey": ["Key_UI_Close", "Key_UI_Close2"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_GiveUp", "ActionKey": ["Key_UI_Action1"], "State": ["RoguePrayerTargetSelection"]}, {"Action": "RoguePrayerTargetSelection_ManualMenu", "ActionKey": ["Key_UI_ManualMenu"], "State": ["RoguePrayerTargetSelection"]}, {"------": "-------------------- RogueGamePaused --------------------"}, {"Action": "RoguePauseResume", "ActionKey": ["Key_Start"], "State": ["RogueGamePaused"]}, {"Action": "RogueGamePaused_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["RogueGamePaused"]}, {"------": "-------------------- RogueRewardsAndRoom --------------------"}, {"Action": "RogueRewardsAndRoom_Left", "ActionKey": ["Key_Left"], "State": ["RogueRewardsAndRoom"]}, {"Action": "RogueRewardsAndRoom_Right", "ActionKey": ["Key_Right"], "State": ["RogueRewardsAndRoom"]}, {"Action": "RogueRewardsAndRoom_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueRewardsAndRoom"]}, {"Action": "RogueRewardsAndRoom_Refuse", "ActionKey": ["Key_Eclipse", "Key_Refuse"], "State": ["RogueRewardsAndRoom"]}, {"------": "-------------------- Rogue Battle Upgrade Select Slot --------------------"}, {"Action": "SelectSlot_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueRewardsAndRoom"]}, {"Action": "SelectSlot_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueRewardsAndRoom"]}, {"Action": "SelectSlot_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueRewardsAndRoom"]}, {"Action": "SelectSlot_Cancel", "ActionKey": ["Key_UI_Close"], "State": ["RogueRewardsAndRoom"]}, {"------": "-------------------- RogueSetStakePawn --------------------"}, {"Action": "RogueSetStakePawn_SwitchLeft", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueSetStakePawn"]}, {"Action": "RogueSetStakePawn_SwitchRight", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueSetStakePawn"]}, {"Action": "RogueSetStakePawn_Reset", "ActionKey": ["Key_UI_Action1"], "State": ["RogueSetStakePawn"]}, {"Action": "RogueSetStakePawn_Close", "ActionKey": ["Key_UI_Close"], "State": ["RogueSetStakePawn"]}, {"------": "-------------------- 通用 确认或取消时的情况用 Reminder --------------------"}, {"Action": "Reminder_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["Reminder"]}, {"Action": "Reminder_Refuse", "ActionKey": ["Key_UI_Close"], "State": ["Reminder"]}, {"Action": "Reminder_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["Reminder"]}, {"Action": "Reminder_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["Reminder"]}, {"------": "-------------------- Rogue UI --------------------"}, {"------": "--- ↑ - W"}, {"Action": "RogueOnUI_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- ↓ - S"}, {"Action": "RogueOnUI_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- ← - A"}, {"Action": "RogueOnUI_Left", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- → - D"}, {"Action": "RogueOnUI_Right", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- L1 - Q"}, {"Action": "RogueOnUI_SwitchLeft", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RogueOnUI"]}, {"------": "--- R1 - E"}, {"Action": "RogueOnUI_SwitchRight", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RogueOnUI"]}, {"------": "--- <PERSON> - <PERSON><PERSON><PERSON>"}, {"Action": "RogueOnUI_Close", "ActionKey": ["Key_UI_Close", "Key_UI_Close2"], "State": ["RogueOnUI"]}, {"------": "--- X - F"}, {"Action": "RogueOnUI_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueOnUI"]}, {"------": "--- Triangle - R"}, {"Action": "RogueOnUI_Action1", "ActionKey": ["Key_UI_Action1"], "State": ["RogueOnUI"]}, {"------": "--- Rect - T"}, {"Action": "RogueOnUI_Action2", "ActionKey": ["Key_UI_Action2"], "State": ["RogueOnUI"]}, {"Action": "RogueOnUI_Up_GamepadLeftStick", "ActionKey": ["Key_UI_Up_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- ↓ - S"}, {"Action": "RogueOnUI_Down_GamepadLeftStick", "ActionKey": ["Key_UI_Down_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- ← - A"}, {"Action": "RogueOnUI_Left_GamepadLeftStick", "ActionKey": ["Key_UI_Left_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"------": "--- → - D"}, {"Action": "RogueOnUI_Right_GamepadLeftStick", "ActionKey": ["Key_UI_Right_GamepadLeftStick"], "State": ["RogueOnUI"]}, {"Action": "RogueOnUI_LeftThumbStick", "ActionKey": ["Key_UI_LeftThumbStick"], "State": ["RogueOnUI"]}, {"Action": "RogueOnUI_ManualMenu", "ActionKey": ["Key_UI_ManualMenu"], "State": ["RogueOnUI"]}, {"------": "--- 下面是哪个仅仅用于UI图片显示，无法进行操作判断"}, {"------": "--- ↑↓ - WS"}, {"Action": "RogueOnUI_UpDown", "ActionKey": ["Key_UI_UpAndDown"], "State": ["RogueOnUI"]}, {"------": "--- ←→ - AD"}, {"Action": "RogueOnUI_LeftRight", "ActionKey": ["Key_UI_LeftAndRight"], "State": ["RogueOnUI"]}, {"------": "--- ↑↓←→ - WSAD"}, {"Action": "RogueOnUI_UpDownLeftRight", "ActionKey": ["Key_UI_UpDownLeftRight"], "State": ["RogueOnUI"]}, {"------": "-------------------- Rogue Seconed UI --------------------"}, {"------": "--- ↑ - W"}, {"Action": "RogueOnSecondaryUI_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- ↓ - S"}, {"Action": "RogueOnSecondaryUI_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- ← - A"}, {"Action": "RogueOnSecondaryUI_Left", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- → - D"}, {"Action": "RogueOnSecondaryUI_Right", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- L1 - Q"}, {"Action": "RogueOnSecondaryUI_SwitchLeft", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- R1 - E"}, {"Action": "RogueOnSecondaryUI_SwitchRight", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- <PERSON> - <PERSON><PERSON><PERSON>"}, {"Action": "RogueOnSecondaryUI_Close", "ActionKey": ["Key_UI_Close", "Key_UI_Close2"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- X - F"}, {"Action": "RogueOnSecondaryUI_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- Triangle - R"}, {"Action": "RogueOnSecondaryUI_Action1", "ActionKey": ["Key_UI_Action1"], "State": ["RogueOnSecondaryUI"]}, {"------": "--- Rect - T"}, {"Action": "RogueOnSecondaryUI_Action2", "ActionKey": ["Key_UI_Action2"], "State": ["RogueOnSecondaryUI"]}, {"------": "-------------------- Rogue Third UI --------------------"}, {"------": "--- ↑ - W"}, {"Action": "RogueOnTertiaryUI_Up", "ActionKey": ["Key_UI_Up", "Key_UI_Up_Key", "Key_UI_Up_GamepadLeftStick"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- ↓ - S"}, {"Action": "RogueOnTertiaryUI_Down", "ActionKey": ["Key_UI_Down", "Key_UI_Down_Key", "Key_UI_Down_GamepadLeftStick"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- ← - A"}, {"Action": "RogueOnTertiaryUI_Left", "ActionKey": ["Key_UI_Left", "Key_UI_Left_Key", "Key_UI_Left_GamepadLeftStick"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- → - D"}, {"Action": "RogueOnTertiaryUI_Right", "ActionKey": ["Key_UI_Right", "Key_UI_Right_Key", "Key_UI_Right_GamepadLeftStick"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- L1 - Q"}, {"Action": "RogueOnTertiaryUI_SwitchLeft", "ActionKey": ["Key_UI_SwitchLeft"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- R1 - E"}, {"Action": "RogueOnTertiaryUI_SwitchRight", "ActionKey": ["Key_UI_SwitchRight"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- <PERSON> - <PERSON><PERSON><PERSON>"}, {"Action": "RogueOnTertiaryUI_Close", "ActionKey": ["Key_UI_Close", "Key_UI_Close2"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- X - F"}, {"Action": "RogueOnTertiaryUI_Confirm", "ActionKey": ["Key_UI_Confirm"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- Triangle - R"}, {"Action": "RogueOnTertiaryUI_Action1", "ActionKey": ["Key_UI_Action1"], "State": ["RogueOnTertiaryUI"]}, {"------": "--- Rect - T"}, {"Action": "RogueOnTertiaryUI_Action2", "ActionKey": ["Key_UI_Action2"], "State": ["RogueOnTertiaryUI"]}], "DefaultKeyMapping": [{"ActionKey": "Key_Start", "Gamepad": ["Gamepad_Special_Right"], "Keyboard": ["Escape"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Options_Lined_128x128", "KeyboardIcon": "P"}, {"ActionKey": "Key_L2", "Gamepad": ["Gamepad_LeftTrigger"], "Keyboard": ["RightMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_L2_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_Q"}, {"ActionKey": "Key_R1", "Gamepad": ["Gamepad_RightShoulder"], "Keyboard": ["Two"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_R1_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_2"}, {"ActionKey": "Key_R2", "Gamepad": ["Gamepad_RightTrigger"], "Keyboard": ["Q"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_R2_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_3"}, {"ActionKey": "Key_L1", "Gamepad": ["Gamepad_LeftShoulder"], "Keyboard": ["F"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_L1_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_F"}, {"ActionKey": "Key_L3", "Gamepad": ["Gamepad_LeftThumbstick"], "Keyboard": ["LeftShift"], "Key_LeftGamepKey_UpadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_LS_L3_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_Shift"}, {"ActionKey": "Key_R3", "Gamepad": ["Gamepad_RightThumbstick"], "Keyboard": ["MiddleMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_RS_R3_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_Mouse_Middle"}, {"ActionKey": "Key_Rect", "Gamepad": ["Gamepad_FaceButton_Left"], "Keyboard": ["LeftMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Sq_Lined_128x128", "KeyboardIcon": "MouseL"}, {"ActionKey": "Key_Triangle", "Gamepad": ["Gamepad_FaceButton_Top"], "Keyboard": ["One"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Tri_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_1"}, {"ActionKey": "Key_Cross", "Gamepad": ["Gamepad_FaceButton_Bottom"], "Keyboard": ["SpaceBar"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_X_Lined_128x128", "KeyboardIcon": "SpaceBar"}, {"ActionKey": "Key_Eclipse", "Gamepad": ["Gamepad_FaceButton_Right"], "Keyboard": ["C"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_El_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_C"}, {"ActionKey": "Key_Eclipse2", "Gamepad": [], "Keyboard": ["LeftAlt"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_El_Lined_128x128", "KeyboardIcon": "Z"}, {"ActionKey": "Key_ComboRectCross", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Base_Sq_Lined_128x128", "KeyboardIcon": "E"}, {"ActionKey": "Key_ComboTriangleEclipse", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Base_El_Lined_128x128", "KeyboardIcon": "Q"}, {"ActionKey": "Key_Select", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Share_Lined_128x128", "KeyboardIcon": "TAB"}, {"ActionKey": "Key_Up", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["W"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "Key_Left", "Gamepad": ["Gamepad_DPad_Left"], "Keyboard": ["A"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UD_Lined_128x128", "KeyboardIcon": "Left"}, {"ActionKey": "Key_Right", "Gamepad": ["Gamepad_DPad_Right"], "Keyboard": ["D"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Right"}, {"ActionKey": "Key_Down", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["S"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"ActionKey": "Key_SelectX", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Tpad_Lined_128x128", "KeyboardIcon": "M"}, {"ActionKey": "Key_Confirm", "Gamepad": [], "Keyboard": ["SpaceBar"]}, {"ActionKey": "Key_Refuse", "Gamepad": [], "Keyboard": ["LeftControl"]}, {"ActionKey": "Key_AwakeSkill", "Gamepad": ["Gamepad_LeftThumbstick", "Gamepad_RightThumbstick"], "Keyboard": ["R"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_R2_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_R"}, {"ActionKey": "Key_UseItem", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["E"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "ArtResource/UI/Icon/Controller/Keyboard/Textures/Keyboard_Black_E"}, {"ActionKey": "Key_ItemLeft", "Gamepad": ["Gamepad_DPad_Left"], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UD_Lined_128x128", "KeyboardIcon": "Left"}, {"ActionKey": "Key_ItemRight", "Gamepad": ["Gamepad_DPad_Right"], "Keyboard": ["Tab"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Right"}, {"ActionKey": "Key_ToggleWeapon", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["X"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"ActionKey": "Key_BigMap", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["M"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"v": "Action的LeftStick和RightStick的Up Left Right Down会有特别处理，因为本来输入方式就不太一样"}, {"ActionKey": "LeftStick_Up", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Down", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Left", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Right", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Up", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Down", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Left", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Right", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "Key_UI_Up", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["W"]}, {"ActionKey": "Key_UI_Down", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["S"]}, {"ActionKey": "Key_UI_Left", "Gamepad": ["Gamepad_DPad_Left"], "Keyboard": ["A"]}, {"ActionKey": "Key_UI_Right", "Gamepad": ["Gamepad_DPad_Right"], "Keyboard": ["D"]}, {"ActionKey": "Key_UI_Up_Key", "Gamepad": [""], "Keyboard": ["Up"]}, {"ActionKey": "Key_UI_Down_Key", "Gamepad": [""], "Keyboard": ["Down"]}, {"ActionKey": "Key_UI_Left_Key", "Gamepad": [""], "Keyboard": ["Left"]}, {"ActionKey": "Key_UI_Right_Key", "Gamepad": [""], "Keyboard": ["Right"]}, {"ActionKey": "Key_UI_Up_GamepadLeftStick", "Gamepad": ["Gamepad_LeftStick_Up"], "Keyboard": []}, {"ActionKey": "Key_UI_Down_GamepadLeftStick", "Gamepad": ["Gamepad_LeftStick_Down"], "Keyboard": []}, {"ActionKey": "Key_UI_Left_GamepadLeftStick", "Gamepad": ["Gamepad_LeftStick_Left"], "Keyboard": []}, {"ActionKey": "Key_UI_Right_GamepadLeftStick", "Gamepad": ["Gamepad_LeftStick_Right"], "Keyboard": []}, {"ActionKey": "Key_UI_SwitchLeft", "Gamepad": ["Gamepad_LeftShoulder"], "Keyboard": ["Q"]}, {"ActionKey": "Key_UI_SwitchRight", "Gamepad": ["Gamepad_RightShoulder"], "Keyboard": ["E"]}, {"ActionKey": "Key_UI_Close", "Gamepad": ["Gamepad_FaceButton_Right"], "Keyboard": ["Escape"]}, {"ActionKey": "Key_UI_Close2", "Gamepad": ["Gamepad_FaceButton_Right"], "Keyboard": ["RightMouseButton"]}, {"ActionKey": "Key_UI_Confirm", "Gamepad": ["Gamepad_FaceButton_Bottom"], "Keyboard": ["SpaceBar"]}, {"ActionKey": "Key_UI_Action1", "Gamepad": ["Gamepad_FaceButton_Top"], "Keyboard": ["R"]}, {"ActionKey": "Key_UI_Action2", "Gamepad": ["Gamepad_FaceButton_Left"], "Keyboard": ["T"]}, {"ActionKey": "Key_UI_LeftThumbStick", "Gamepad": ["Gamepad_LeftThumbstick"], "Keyboard": ["T"]}, {"ActionKey": "Key_UI_ManualMenu", "Gamepad": ["Gamepad_Special_Left"], "Keyboard": ["Tab"]}, {"------": "--- 下面是哪个仅仅用于UI图片显示，无法进行操作判断"}, {"ActionKey": "Key_UI_UpAndDown", "Gamepad": ["Gamepad_DPad_UpAndDown"], "Keyboard": ["WS"]}, {"ActionKey": "Key_UI_LeftAndRight", "Gamepad": ["Gamepad_DPad_LeftAndRight"], "Keyboard": ["AD"]}, {"ActionKey": "Key_UI_UpDownLeftRight", "Gamepad": ["Gamepad_DPad_UpDownLeftRight"], "Keyboard": ["WSAD"]}, {"ActionKey": "Key_UI_LeftStick", "Gamepad": ["Gamepad_LeftStick"], "Keyboard": [""]}, {"ActionKey": "Key_UI_Rightstick", "Gamepad": ["Gamepad_Rightstick"], "Keyboard": [""]}, {"------": "-------------------- Rogue Game --------------------"}, {"ActionKey": "RogueKey_NormalAttack", "Gamepad": ["Gamepad_FaceButton_Left"], "Keyboard": ["LeftMouseButton"]}, {"ActionKey": "RogueKey_Ability1", "Gamepad": ["Gamepad_FaceButton_Top"], "Keyboard": ["Q"]}, {"ActionKey": "Rogue<PERSON>ey_Jump", "Gamepad": ["Gamepad_FaceButton_Bottom"], "Keyboard": ["SpaceBar"]}, {"ActionKey": "RogueKey_Interactive", "Gamepad": ["Gamepad_FaceButton_Bottom"], "Keyboard": ["F"]}, {"ActionKey": "Rogue<PERSON>ey_Dodge", "Gamepad": ["Gamepad_FaceButton_Right"], "Keyboard": ["C"]}, {"ActionKey": "RogueKey_Aim", "Gamepad": ["Gamepad_LeftTrigger"], "Keyboard": ["RightMouseButton"]}, {"ActionKey": "RogueKey_UseItem", "Gamepad": ["Gamepad_LeftShoulder"], "Keyboard": ["Two"]}, {"ActionKey": "RogueKey_UseItem2", "Gamepad": ["Gamepad_RightShoulder"], "Keyboard": ["Three"]}, {"ActionKey": "RogueKey_Ability2", "Gamepad": ["Gamepad_RightTrigger"], "Keyboard": ["E"]}, {"ActionKey": "RogueKey_Sprint", "Gamepad": ["Gamepad_LeftThumbstick"], "Keyboard": ["LeftShift"]}, {"ActionKey": "RogueKey_ResetCamera", "Gamepad": ["Gamepad_RightThumbstick"], "Keyboard": ["MiddleMouseButton"]}, {"ActionKey": "RogueKey_AwakeSkill", "Gamepad": ["Gamepad_LeftThumbstick", "Gamepad_RightThumbstick"], "Keyboard": ["V"]}, {"ActionKey": "RogueKey_DrinkPotion", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["One"]}, {"ActionKey": "RogueKey_ToggleWeapon", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["X"]}, {"ActionKey": "RogueKey_PauseMenu", "Gamepad": ["Gamepad_Special_Right"], "Keyboard": ["Escape"]}, {"ActionKey": "RogueKey_ManualMenu", "Gamepad": ["Gamepad_Special_Left"], "Keyboard": ["Tab"]}, {"ActionKey": "RogueKey_Share", "Gamepad": ["Gamepad_Special_Left"], "Keyboard": [""]}], "CanCustomAction": [{"KeyDesc": "NormalAttack", "ActionCmd": "NormalAttack", "ActionKeyMap": "RogueKey_NormalAttack", "CanChangeInGamepad": true}, {"KeyDesc": "Ability1", "ActionCmd": "Ability1", "ActionKeyMap": "RogueKey_Ability1", "CanChangeInGamepad": true}, {"KeyDesc": "Ability2", "ActionCmd": "Ability2", "ActionKeyMap": "RogueKey_Ability2", "CanChangeInGamepad": true}, {"KeyDesc": "Artifact_1", "ActionCmd": "UseItem", "ActionKeyMap": "RogueKey_UseItem", "CanChangeInGamepad": true}, {"KeyDesc": "Artifact_2", "ActionCmd": "UseItem2", "ActionKeyMap": "RogueKey_UseItem2", "CanChangeInGamepad": true}, {"KeyDesc": "AwakeSkill", "ActionCmd": "AwakeSkill", "ActionKeyMap": "RogueKey_AwakeSkill", "CanChangeInGamepad": false}, {"KeyDesc": "Jump", "ActionCmd": "Jump", "ActionKeyMap": "Rogue<PERSON>ey_Jump", "CanChangeInGamepad": true}, {"KeyDesc": "Dodge", "ActionCmd": "Dodge", "ActionKeyMap": "Rogue<PERSON>ey_Dodge", "CanChangeInGamepad": true}, {"KeyDesc": "Sprint", "ActionCmd": "Sprint", "ActionKeyMap": "RogueKey_Sprint", "CanChangeInGamepad": true}, {"KeyDesc": "Defense", "ActionCmd": "Aim", "ActionKeyMap": "RogueKey_Aim", "CanChangeInGamepad": true}, {"KeyDesc": "DrinkPotion_Rogue", "ActionCmd": "DrinkPotion", "ActionKeyMap": "RogueKey_DrinkPotion", "CanChangeInGamepad": true}, {"KeyDesc": "Interactive", "ActionCmd": "Interactive", "ActionKeyMap": "RogueKey_Interactive", "CanChangeInGamepad": false}, {"KeyDesc": "DrawWeapon", "ActionCmd": "ToggleWeapon", "ActionKeyMap": "RogueKey_ToggleWeapon", "CanChangeInGamepad": true}, {"KeyDesc": "ResettingTheLens", "ActionCmd": "ResetCamera", "ActionKeyMap": "RogueKey_ResetCamera", "CanChangeInGamepad": true}], "CanUseKey_Gamepad": ["Gamepad_LeftThumbstick", "Gamepad_FaceButton_Bottom", "Gamepad_FaceButton_Right", "Gamepad_FaceButton_Left", "Gamepad_FaceButton_Top", "Gamepad_LeftShoulder", "Gamepad_RightShoulder", "Gamepad_LeftTrigger", "Gamepad_RightTrigger", "Gamepad_DPad_Up", "Gamepad_DPad_Down", "Gamepad_DPad_Right", "Gamepad_DPad_Left", "Gamepad_RightThumbstick"], "CanUseKey_Keyboard": ["LeftMouseButton", "RightMouseButton", "ThumbMouseButton", "ThumbMouseButton2", "MiddleMouseButton", "SpaceBar", "LeftShift", "LeftControl", "LeftAlt", "Zero", "One", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "B", "C", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "T", "U", "V", "X", "Y", "Z"]}