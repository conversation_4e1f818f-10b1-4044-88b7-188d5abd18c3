{"Dialogs": [{"说明": "Bakura初次对话", "Id": "Rogue_Bakura_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_VeryFirstDialog"}], "Selections": [{"说明": "Rogue_Bakura故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueBakuraStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_Bakura初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog7"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_First_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Bakura_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Bakura 名字是好心人的默认开始对话", "Id": "Rogue_Bakura_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_FirstDialog2"}], "Selections": [{"说明": "Rogue_Bakura故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueBakuraStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_Bakura结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "Rogue_Bakura初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialog7"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_First_StoryDialog,1)"]}, {"说明": "Rogue_Bakura初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_StoryFirstDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_Bakura 死亡3次或遇见死骸骑士后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3Death1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3Death2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3Death3"}, {"Type": "GiveCurrencyToNPC", "Id": "Step3", "NextId": "", "Params": ["Rogue_Soul", "5", "GiveEchoSuccess", "GiveEchoFailure"]}], "Selections": []}, {"说明": "Rogue_Bakura 送魂之残响成功", "Id": "GiveEchoSuccess", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3Death5"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Shard,5)", "DialogAction.SetRogueSwitch(Bakura_GiveEcho,1)", "DialogAction.SetRogueSwitch(Bakura_AfterDeath_StoryDialog,1)"]}, {"说明": "Rogue_Bakura 送魂之残响失败", "Id": "GiveEchoFailure", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3Death7"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_AfterDeath_StoryDialog,1)"]}, {"说明": "Rogue_Bakura 死亡3次或遇见死骸骑士后 没有送魂之残响 的重复对话", "Id": "AfterDeathDialogRepeat1", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3DeathRepeat1"}, {"Type": "GiveCurrencyToNPC", "Id": "Step1", "NextId": "", "Params": ["Rogue_Soul", "100", "GiveEchoSuccess", "GiveEchoFailure"]}], "Selections": []}, {"说明": "Rogue_Bakura 死亡3次或遇见死骸骑士后 送了魂之残响 的重复对话", "Id": "AfterDeathDialogRepeat2", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_After3DeathRepeat6"}], "Selections": []}, {"说明": "Rogue_Bakura 出现了NPC卫士长后的对话", "Id": "FandralAppearDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterGuardShowup1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterGuardShowup2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterGuardShowup3"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Soul,5)", "DialogAction.SetRogueSwitch(Bakura_FandralAppear_StoryDialog,1)"]}, {"说明": "Rogue_Bakura 出现了NPC卫士长后的重复对话", "Id": "FandralAppearDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterGuardShowupRepeat1"}], "Selections": []}, {"说明": "Rogue_Bakura 和NPC卫士长第一次对话完后的对话", "Id": "AfterFirstTalkFandralDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterFirstTalkwithGuard1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterFirstTalkwithGuard2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterFirstTalkwithGuard3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_AfterFirstTalkFandral_StoryDialog,1)"]}, {"说明": "Rogue_Bakura 和NPC卫士长第一次对话完后的重复对话", "Id": "AfterFirstTalkFandralDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Kind_Passerby", "Text": "Bakura_AfterFirstTalkwithGuardRepeat1"}], "Selections": []}], "EndDialogScript": []}, {"说明": "Rogue_Bakura 名字是贼王的默认开始对话", "Id": "Rogue_Bakura_RealNameDefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_FirstDialog2"}], "Selections": [{"说明": "Rogue_Bakura故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueBakuraStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_Bakura结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "Rogue_Bakura 和NPC卫士长第二次对话完后的对话", "Id": "AfterSecondTalkFandralDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterSecondTalkwithGuard1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterSecondTalkwithGuard2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterSecondTalkwithGuard3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterSecondTalkwithGuard4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterSecondTalkwithGuard5"}, {"Type": "DoAction", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Disappear"}, {"Type": "Wait", "Id": "Step6", "NextId": "", "WaitSec": 4.5}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_AfterSecondTalkFandral_StoryDialog,1)"]}, {"说明": "Rogue_Bakura 击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterFinalBattle1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterFinalBattle2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterFinalBattle3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterFinalBattle4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Bakura_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Bakura 击败真·真死骸骑士的重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON>ra", "Text": "Bakura_AfterFinalBattle1"}], "Selections": []}], "EndDialogScript": []}]}