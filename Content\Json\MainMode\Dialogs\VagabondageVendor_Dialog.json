{"Dialogs": [{"说明": "VagabondageVendor_FirstDialog", "Id": "VagabondageVendor_FirstDialog", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "ShopKeeper_TurnLeft", "ActionId_TurnRight": "ShopKeeper_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShopKeeper_Showing_Goods_1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Wait", "Id": "<useless example>", "NextId": "SayIntro", "WaitSec": 0.6}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "VagabondageVendor_Speech2b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "VagabondageVendor_Name", "Text": "VagabondageVendor_Speech2b1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "VagabondageVendor_Speech2b2", "NextEventFunc": "DialogAction.DirectGoTo(VagabondageVendor_SecondDialog)"}]}, {"Id": "VagabondageVendor_SecondDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShopKeeper_Showing_Goods_1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Wait", "Id": "<useless example>", "NextId": "SayIntro", "WaitSec": 0.6}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "VagabondageVendor_Speech2b3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "VagabondageVendor_Name", "Text": "VagabondageVendor_Speech2b3"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "VagabondageVendor_Speech2b4"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "VagabondageVendor_Name", "Text": "VagabondageVendor_Speech2b4"}], "Selections": []}], "EndDialogScript": ["DialogAction.GivePlayerPackage(FivePotions)"]}]}