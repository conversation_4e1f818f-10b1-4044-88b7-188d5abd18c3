{"FSubtitleGroup": [{"SubtitleGroupId": "字幕组名", "SubtitleArray": [{"SpeakerKey": "说话者的名字Key (根据Chinese.json里的来)", "ContentKey": "说话内容Key (根据Chinese.json里的来)", "AudioId": "音频key (根据VoiceAudio.json里的来)", "AudioPlayType": "音频播放类型 共三种 PlaySound2D、PlaySoundAtLocation、PlaySoundAttachToTarget", "PlayAudioLocation": "X=0,Y=0,Z=0、播放位置 播放类型为 PlaySoundAtLocation 时使用", "AudioAttachToTargetIndex": "添加到目标对象数组的index", "IntervalTime": "0、每句话的间隔时间、这个时间是在每句话开头计算的"}]}, {"______________________________第一次来到无相之厅_____________": "__________________________________刚上岸_________________________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON><PERSON>_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON><PERSON>_FirstArriveHall2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_SecondArriveHall", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_SecondArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Warrior_Tierdagon_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "Warrior_Tierdagon", "ContentKey": "Warrior_Tierdagon_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON>_<PERSON>a_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON>_<PERSON>a", "ContentKey": "<PERSON><PERSON><PERSON>_Sola_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON>", "ContentKey": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Ma<PERSON>_<PERSON>_FirstArriveHall", "SubtitleArray": [{"SpeakerKey": "Ma<PERSON>_<PERSON>", "ContentKey": "Mage_<PERSON>_FirstArriveHall1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"______________________________第一次来到祭坛前_____________": "__________________________________刚上岸_________________________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON>sso_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_SecondArrivePray", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_SecondArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Warrior_Tierdagon_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "Warrior_Tierdagon", "ContentKey": "Warrior_Tierdagon_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON>_Sola_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON>_<PERSON>a", "ContentKey": "<PERSON><PERSON><PERSON>_Sola_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "Blade<PERSON><PERSON><PERSON>_<PERSON>_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON>", "ContentKey": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Ma<PERSON>_<PERSON>_FirstArrivePray", "SubtitleArray": [{"SpeakerKey": "Ma<PERSON>_<PERSON>", "ContentKey": "Mage_<PERSON>_FirstArrivePray1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"______________________________第一次进入房间_____________": "__________________________________刚上岸_________________________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON><PERSON>_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON><PERSON>_FirstArriveRoom2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Swordsman_<PERSON><PERSON><PERSON>_FirstArriveRoom3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_FirstArriveRoom4", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_SecondArriveRoom", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_SecondArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_SecondArriveRoom2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Warrior_Tierdagon_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "Warrior_Tierdagon", "ContentKey": "Warrior_Tierdagon_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON>_Sola_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON>_<PERSON>a", "ContentKey": "<PERSON><PERSON><PERSON>_Sola_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON><PERSON><PERSON>_<PERSON>_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "Blade<PERSON><PERSON><PERSON>_<PERSON>_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON>", "ContentKey": "<PERSON>_<PERSON><PERSON><PERSON>_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "Ma<PERSON>_<PERSON>_FirstArriveRoom", "SubtitleArray": [{"SpeakerKey": "Ma<PERSON>_<PERSON>", "ContentKey": "Mage_<PERSON>_FirstArriveRoom1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"______________________________遭遇魔物_____________": "__________________________________刚上岸_________________________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_InBattle", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_InBattle1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_InBattle2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_InBattle3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_InBattle4", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_InBattle5", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FightBoss", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FightBoss1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FightBoss2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_HurtBad", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_HurtBad1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_HurtBad2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_HurtBad3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dead", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_Dead1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_Dead2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Dead3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"______________________________第一次死亡后黑底白字_____________": "__________________________________刚上岸_________________________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>D<PERSON><PERSON>", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_FirstDeath2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"______________________________死骸骑士_____________": "__________________________________刚上岸_________________________________"}, {"___________________________   一般开场": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Deathlord_FirstFight", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_FirstFight1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_FirstFight2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   锁血跑路": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Deathlord_Escape", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_Escape1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   未在时间内到达": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Deathlord_ExceedTime", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_ExceedTime1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_ExceedTime2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   在时间内到达": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Deathlord_WithinTime", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_WithinTime1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "Deathlord_WithinTime2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   BOSS被打败": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_<PERSON>D<PERSON><PERSON>", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_FirstDeath1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "", "ContentKey": "Sword<PERSON>_<PERSON><PERSON><PERSON>_FirstDeath2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstDeath3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstDeath4", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "", "ContentKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>_FirstDeath5", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   剑盾第一次通关后回到大厅": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Gerasso_CompleteAll", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Gerasso_CompleteAll1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "Gerasso_CompleteAll2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   大剑第一次通关后回到大厅": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Tierdagon_CompleteAll", "SubtitleArray": [{"SpeakerKey": "Warrior_Tierdagon", "ContentKey": "Tierdagon_CompleteAll1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Warrior_Tierdagon", "ContentKey": "Tierdagon_CompleteAll2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________  长枪第一次通关后回到大厅": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "Sola_CompleteAll", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON>_<PERSON>a", "ContentKey": "Sola_CompleteAll1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON><PERSON>_<PERSON>a", "ContentKey": "Sola_CompleteAll2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"___________________________   双刀第一次通关后回到大厅": "_____________________死骸骑士_____________________"}, {"SubtitleGroupId": "<PERSON><PERSON>", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "Henrik_CompleteAll1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "Henrik_CompleteAll2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}]}