{"Mob": [{"Id": "LandDragon", "Tag": [], "AI": ["StopAIBuff", "LandDragon_GroundSkillAttack", "LandDragon_GroundJumpAttack", "LandDragon_TurnToStimulate_OnGround"], "BpPath": "Core/Characters/Rogue_Mob/AwakeDragon/RogueLandDragon_BP", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "Head", "OnBeKilled": [], "Flyable": false, "Name": "憨批之龙", "Portrait": "", "Potential": {"HP": 10, "PAtk": 1, "Balance": 0, "MoveSpeed": [565, 846, 1345], "BeStrikeRate": 0.75}, "PerceptionProp": {"ViewRadius": 0, "SightRadius": 50000, "SightZRadius": 5500, "SightHalfAngleDregee": 180, "LoseSightRadius": 9999999, "LoseSightHalfAngleDregee": 180, "NoiseRadius": 0, "HearingRadius": 50000, "LoseHearingRadius": 9999999}, "Buff": [], "Part": [{"Meat": {"Physical": 0}, "Part": "Head", "Durability": [20], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0}, "Part": "Body", "Durability": [20], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0}, "Part": "Wing", "Durability": [20], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat"}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "JustFall", "UnArmed": "JustFall"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": [""]}, "InitAction": true}, {"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "<PERSON><PERSON><PERSON>", "AwakeDragon_GroundAttack", "AwakeDragon_GroundSkillAttack"], "1": ["Move"]}, "Balance": 1, "Break": 1, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Animation/Monster/Dragon/UnkaDragon/BlendSpaces/Unka_Move_Ground"]}, "InitAction": true}, {"说明": "向左180度转身", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "<PERSON><PERSON><PERSON>", "AwakeDragon_GroundAttack", "AwakeDragon_GroundSkillAttack"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Animation/Monster/Dragon/UnkaDragon/Montages/Unka_RM_Turn_Left_180_Montage"]}}, {"说明": "向右180度转身", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "<PERSON><PERSON><PERSON>", "AwakeDragon_GroundAttack", "AwakeDragon_GroundSkillAttack"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Animation/Monster/Dragon/UnkaDragon/Montages/Unka_RM_Turn_Right_180_Montage"]}}, {"Line": "_______________________________龙攻击动作________________________________"}, {"Id": "<PERSON>l<PERSON><PERSON><PERSON>", "Cmds": ["_"], "Tags": [{"Tag": "AwakeDragon_GroundAttack", "From": 0}], "BeCancelledTags": {"0": ["AwakeDragon_GroundAttack", "AwakeDragon_SkillAttack"]}, "Balance": 1, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Animation/Monster/Dragon/UnkaDragon/Montages/Unka_RM_Attack_Tail_Left_Montage"]}, "InitAction": true}, {"Id": "JumpAttack_Ground", "Cmds": ["Action2"], "Tags": [{"Tag": "AwakeDragon_GroundAttack", "From": 0}], "BeCancelledTags": {"0": ["AwakeDragon_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Animation/Monster/Dragon/UnkaDragon/Montages/Unka_RM_Jump_Forward_Montage"]}, "InitAction": true}]}]}