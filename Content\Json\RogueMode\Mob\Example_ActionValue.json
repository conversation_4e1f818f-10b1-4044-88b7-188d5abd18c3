{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "<String>BUFF的ID", "BuffStack": "<int>需要的BUFF层数", "Weight": "<float>在该BUFF大于等于指定层数后的权重加成"}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "SwitchWeight": [{"SwitchId": "<String>Switch的Id", "MinRange": "<float>Switch的Value区间的最小值", "MaxRange": "<float>Switch的Value区间的最大值", "Weight": "<float>这个Switch的Value区间内，这个动作的权重加成"}], "OnGround": "<float>敌人在地面上时的权重加成", "InAir": "<float>敌人在空中时的权重加成", "InDodge": "<float>敌人正在翻滚时的权重加成", "InHurt": "<float>敌人正在受击时的权重加成", "ClearedGame": "<float>当局游戏是世界难度>0时的权重加成", "MinActionCD": "<float>最小CD时间", "MaxActionCD": "<float>最大CD时间"}]}}]}