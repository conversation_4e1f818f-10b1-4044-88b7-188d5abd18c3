{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "<PERSON>_<PERSON>kel<PERSON>_Crossbowman", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1500, "Weight": 10}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面", "MinRange": "-60", "MaxRange": "60", "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 1}, {"Id": "Action_Stare02", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"说明": "背面1", "MinRange": "-180", "MaxRange": "-90", "Weight": -99}, {"说明": "背面2", "MinRange": "90", "MaxRange": "180", "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 2}, {"Id": "Action_Stare03", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"说明": "背面1", "MinRange": "-180", "MaxRange": "-90", "Weight": -99}, {"说明": "背面2", "MinRange": "90", "MaxRange": "180", "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 2}, {"Id": "Action_Stare04", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面", "MinRange": "-60", "MaxRange": "60", "Weight": -99}, {"说明": "背面1", "MinRange": "-180", "MaxRange": "-90", "Weight": 99}, {"说明": "背面2", "MinRange": "90", "MaxRange": "180", "Weight": 99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 1}]}]}