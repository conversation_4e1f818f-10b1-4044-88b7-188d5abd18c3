{"AOE": [{"分割": "-------------------------------------------Ilm-----------------------------------------"}, {"说明": "动作强化-下砸", "Id": "Rogue_Aoe_Ilm4", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm4", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(9,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(9,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnActorLeave": []}, {"说明": "动作强化-位移", "Id": "Rogue_Aoe_Ilm6", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm6", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(6,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(6,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Ilm8", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm8", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_IceB<PERSON>t", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm3", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.5,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.5,0,<PERSON>,RogueJuniorDamage,ToMontageState,5)"], "OnActorLeave": []}, {"Id": "Rogue_IceArea", "Tag": [], "TickTime": 0.5, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_AOE_Ilm11", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.5,0,<PERSON>,RogueJuniorDamage,Keep)"], "OnRemoved": [], "OnCharacterEnter": [""], "OnCharacterLeave": [], "OnActorEnter": [""], "OnActorLeave": []}, {"说明": "动作强化-上挑，在蓝图Rogue_Aoe_Ilm13调整", "Id": "Rogue_Aoe_Ilm13", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm13", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Ilm17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Ilm18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Ilm/Rogue_Aoe_Ilm18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Igni-----------------------------------------"}, {"Id": "Rogue_FireBurst", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_AOE_Igni2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni3", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni3", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-下砸", "Id": "Rogue_Aoe_Igni4", "Tag": [], "TickTime": 0.6, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni4", "OnCreate": [], "OnTick": ["AOEScript.DealDamageByCsterPropOnTick(2,0,<PERSON>,RogueJuniorDamage,Keep)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-位移", "Id": "Rogue_Aoe_Igni6", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni6", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(5,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni10", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni10", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.5,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni11", "Tag": [], "TickTime": 1, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni11", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.7,0,<PERSON>,RogueJuniorDamage,Keep)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni13", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni13", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-上挑，在蓝图Rogue_Aoe_Igni13_2调整", "Id": "Rogue_Aoe_Igni13_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni13_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Igni18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Igni/Rogue_Aoe_Igni18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(0.85,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)", "AOEScript.AddBuffToEnemyOnHit(Rogue_Burning,1,8)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Poltick-----------------------------------------"}, {"Id": "Rogue_Aoe_Poltick3", "Tag": [], "TickTime": 0.4, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick3", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.4,0,<PERSON>,RogueJuniorDamage,ToMontageState,<PERSON>,3)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick4", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick4", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-位移", "Id": "Rogue_Aoe_Poltick6", "Tag": [], "TickTime": 0.3, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick6", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(2,0,<PERSON>,RogueJuniorDamage,ToMontageState,<PERSON>,3)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick8", "Tag": [], "TickTime": 0.5, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick8", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.4,0,<PERSON>,RogueJuniorDamage,ToMontageState,<PERSON>,3)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick10", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick10", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick11", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick11", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnActorLeave": []}, {"说明": "动作强化-下砸、上挑", "Id": "Rogue_Aoe_Poltick13", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick4", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(3.5,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(3.5,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON>oe_Poltick17_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick17_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.1,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)", "AOEScript.AddBuffToEnemyOnHit(Rogue_Thunder,1,8)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Poltick18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Poltick/Rogue_Aoe_Poltick18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Zantia-----------------------------------------"}, {"Id": "Rogue_Aoe_Zantia2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-下砸，在蓝图Rogue_Aoe_Zantia3调整", "Id": "Rogue_Aoe_Zantia3", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia3", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia4", "Tag": [], "TickTime": 0.3, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia4", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.2,0,Wind,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia5", "Tag": [], "TickTime": 0.3, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia5", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.2,0,Wind,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-位移，在Json中的Rogue_Bullet_Zantia6调整（特殊）", "Id": "Rogue_Aoe_Zantia6", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia6", "OnCreate": [], "OnTick": [""], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia8", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia8", "OnCreate": [], "OnTick": [""], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia10", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia10", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.35,0,Wind,RogueJuniorDamage)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.35,0,Wind,RogueJuniorDamage)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia11", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia11", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.8,0,Wind,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-上挑，在蓝图Rogue_Aoe_Zantia13调整", "Id": "Rogue_Aoe_Zantia13", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia13", "OnCreate": [], "OnTick": [""], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia17_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia17_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.2,0,<PERSON>,RogueJuniorDamage,Keep,3)", "AOEScript.AddBuffToEnemyOnHit(Rogue_Wind,1,8)", "AOEScript.DealMoveVelocityOnTouch(0,0,300,ToMontageState,KnockOut,5,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Zantia18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Zantia/Rogue_Aoe_Zantia18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(0.7,0,<PERSON>,RogueJuniorDamage,Keep,3)", "AOEScript.AddBuffToEnemyOnHit(Rogue_Wind,1,8)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Azem-----------------------------------------"}, {"Id": "Rogue_Aoe_Azem3", "Tag": [], "TickTime": 0.4, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem3", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.7,0,Light,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem4", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem4", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem6", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem6", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem10", "Tag": [], "TickTime": 0.5, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem10", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(0.5,0,Light,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem11", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem11", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem13", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem13", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Azem18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Azem/Rogue_Aoe_Azem18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.AddBuffToEnemyOnHit(Rogue_Light,1,8)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Eminendanis-----------------------------------------"}, {"Id": "Rogue_Aoe_Eminendanis2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON><PERSON>_Eminendanis2_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis2_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-下砸", "Id": "Rogue_Aoe_Eminendanis3", "Tag": [], "TickTime": 0.3, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis3", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(1.5,0,<PERSON>,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"说明": "动作强化-位移", "Id": "Rogue_Aoe_Eminendanis4", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis4", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(3,0,<PERSON>,RogueJuniorDamage)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(3,0,<PERSON>,RogueJuniorDamage)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Eminendanis6", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis6", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "<PERSON>_<PERSON><PERSON>_Eminendanis6_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis6_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2.1,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2.1,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Eminendanis10", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis10", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON>oe_Eminendanis10_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis10_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Eminendanis11", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis11", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON>oe_Eminendanis11_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis11_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON>oe_Eminendanis11_3", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis11_3", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(0.8,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DealDamageByCsterPropOnTouch(0.8,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)"], "OnActorLeave": []}, {"说明": "动作强化-上挑", "Id": "Rogue_Aoe_Eminendanis13", "Tag": [], "TickTime": 0.8, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis13", "OnCreate": [], "OnTick": ["AOESCript.DealDamageByCsterPropOnTick(2.4,0,<PERSON>,RogueJuniorDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Eminendanis17", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis17", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_Aoe_Eminendanis18", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Eminendanis/Rogue_Aoe_Eminendanis18", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"分割": "-------------------------------------------Other-----------------------------------------"}, {"Id": "<PERSON>_<PERSON><PERSON>_<PERSON>", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Pierce/<PERSON>_<PERSON><PERSON>_<PERSON>", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "<PERSON>_<PERSON><PERSON>_Bludgeon", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Bludgeon/Rogue_A<PERSON>_Bludgeon", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Rogue_<PERSON>oe_HealArea", "Tag": [], "TickTime": 1, "BpPath": "Core/Item/AOE/Rogue/Other/Rogue_Aoe_HealArea", "OnCreate": [], "OnTick": ["AOESCript.DealHealByCasterPropOnTick(3,0.02,ExtraDamage)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}]}