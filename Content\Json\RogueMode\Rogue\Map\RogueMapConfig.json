{"说明": "EA版本", "RougeRoomStepConfig1": [{"StepIndex": 1, "RoomRewardType": ["<PERSON><PERSON>(10)"]}, {"StepIndex": 2, "RoomRewardType": ["<PERSON><PERSON>(8)", "Soul(5)", "Key(2)", "Challenge(3)"]}, {"StepIndex": 3, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)"]}, {"StepIndex": 4, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)", "Challenge(5)", "Shop(5)"]}, {"StepIndex": 5, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)", "Challenge(5)"]}, {"StepIndex": 6, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)", "Challenge(5)", "Shop(5)"]}, {"StepIndex": 7, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)", "Challenge(5)"]}, {"StepIndex": 8, "RoomRewardType": ["<PERSON><PERSON>(8)", "<PERSON><PERSON>(2)", "Soul(5)", "Key(2)", "Challenge(5)", "Shop(20)"]}, {"StepIndex": 9, "RoomRewardType": ["Upgrade(20)"]}, {"StepIndex": 10, "RoomRewardType": ["Boss"]}], "RougeRoomStepConfig": [{"StepIndex": 1, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 2, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 3, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 4, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 5, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 6, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 7, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 8, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 9, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 10, "RoomRewardType": ["<PERSON>(10)"]}, {"StepIndex": 11, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 12, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 13, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 14, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 15, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 16, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 17, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 18, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 19, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 20, "RoomRewardType": ["<PERSON>(10)"]}, {"StepIndex": 21, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 22, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 23, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 24, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 25, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 26, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 27, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 28, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 29, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 30, "RoomRewardType": ["<PERSON>(10)"]}], "RougeClearedRoomStepConfig": [{"StepIndex": 1, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 2, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 3, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 4, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 5, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 6, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 7, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 8, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 9, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 10, "RoomRewardType": ["<PERSON>(10)"]}, {"StepIndex": 11, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 12, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 13, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 14, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 15, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 16, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 17, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 18, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 19, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 20, "RoomRewardType": ["<PERSON>(10)"]}, {"StepIndex": 21, "RoomRewardType": ["Normal(10)"]}, {"StepIndex": 22, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 23, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 24, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 25, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 26, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 27, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)"]}, {"StepIndex": 28, "RoomRewardType": ["Normal(10)", "Elite(10)", "Challenge(10)", "Shop(1000)"]}, {"StepIndex": 29, "RoomRewardType": ["Upgrade(10)"]}, {"StepIndex": 30, "RoomRewardType": ["<PERSON>(10)"]}, {"StepIndex": 31, "RoomRewardType": ["<PERSON>(10)"]}]}