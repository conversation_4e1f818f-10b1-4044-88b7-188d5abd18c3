{"Buff": [{"说明": "有这个buff，AI停止", "Id": "StopAI", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "有这个buff，AI会先播放登场动作", "Id": "WillDoDebut", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "在普通状态下，受击减少FightWill.Value值", "Id": "AddRageWhenBeHurt", "Tag": [], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.ModifyFightWillWhenNormalStage()"]}, {"说明": "有这个buff，AI会播放NoCombatIdle动作", "Id": "NPCNoCombat", "Tag": ["NPC", "NoCombat"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "有这个buff，AI会计算Break值,Break值为零时，播放Break受击动画", "Id": "MobBreakValue", "Tag": ["Break"], "Priority": 0, "MaxStack": 999999, "OnOccur": ["BuffUtils.InitBreakBuff(2)"], "OnBeHurt": ["BuffUtils.AccumulateBreakDamageAndHurt(BreakHurt)"]}, {"说明": "Rogue Boss 第一阶段标记BUFF", "Id": "Rogue_Boss_FirstStage", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "Rogue Boss 第二阶段标记BUFF", "Id": "Rogue_Boss_SecondStage", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "Rogue Boss 第三阶段标记BUFF", "Id": "Rogue_Boss_ThirdStage", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnRemoved": []}, {"说明": "Rogue Boss 第二阶段检测BUFF（当血量小于该BUFF层数时转阶段）", "Id": "Rogue_Boss_CheckSecondStage", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 100, "OnOccur": [], "OnRemoved": []}, {"说明": "Rogue Boss 第三阶段检测BUFF（当血量小于该BUFF层数时转阶段）", "Id": "Rogue_Boss_CheckThirdStage", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 100, "OnOccur": [], "OnRemoved": []}, {"说明": "Rogue Boss 转阶段锁血BUFF(50%血)", "Id": "Rogue_Boss_ChangeStage50", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnBeHurt": ["BuffUtils.IgnoreDamageWhenHpLess(0.49)"]}, {"说明": "Rogue Boss 转阶段锁血BUFF(67%血)", "Id": "Rogue_Boss_ChangeStage67", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnBeHurt": ["BuffUtils.IgnoreDamageWhenHpLess(0.66)"]}, {"说明": "Rogue Boss 转阶段锁血BUFF(34%血)", "Id": "Rogue_Boss_ChangeStage34", "Tag": ["AI", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnOccur": [], "OnBeHurt": ["BuffUtils.IgnoreDamageWhenHpLess(0.33)"]}]}