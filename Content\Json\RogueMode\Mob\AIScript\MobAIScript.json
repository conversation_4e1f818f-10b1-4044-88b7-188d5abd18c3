{"AIScript": [{"Id": "StopAI", "Condition": ["MobAIScript.CheckStopAI()", "MobAIScript.CheckPlayerOutRange(10000)"], "Action": ["MobAIScript.DoNothing()"]}, {"Id": "StopAISurvivor", "Condition": ["MobAIScript.CheckStopAI()", "MobAIScript.CheckPlayerOutRange(10000)"], "Action": ["MobAIScript.DoNothing()"]}, {"Id": "StopAIBuff", "Condition": ["MobAIScript.CheckStopAI()"], "Action": ["MobAIScript.DoNothing()"]}, {"Id": "MoveToClosetViewedEnemy", "Condition": ["MobAIScript.CheckAIMoveToClosetViewedEnemy()"], "Action": ["MobAIScript.AIMoveToClosetViewedEnemy()"]}, {"Id": "StareWhenRelax", "Condition": ["MobAIScript.CheckNotStimulateByView()"], "Action": []}, {"Id": "MoveToPlayerNear", "Condition": ["MobAIScript.CheckPlayerInRange(100,5000)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}, {"Id": "MoveToPlayer", "Condition": ["MobAIScript.CheckPlayerInRange(300,5000)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}, {"Id": "MoveToPlayerMid", "Condition": ["MobAIScript.CheckPlayerInRange(600,5000)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}, {"Id": "MoveToPlayerFar", "Condition": ["MobAIScript.CheckPlayerInRange(1000,5000)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}, {"Id": "MoveToPlayerEveryWhere", "Condition": [], "Action": ["MobAIScript.AIMoveToPlayer()"]}, {"Id": "MoveToAthena", "Condition": [], "Action": ["MobAIScript.AIMoveToAthena()"]}, {"Id": "MoveToAthenaAndEnemy", "Condition": [], "Action": ["MobAIScript.AIMoveToAthenaAndSearchEnemy()"]}, {"Id": "MoveToAthenaAndEnemyNear", "Condition": ["MobAIScript.CheckPlayerInRangeSvl(5000)"], "Action": ["MobAIScript.AIMoveToAthenaAndSearchEnemy(5000)"]}, {"说明": "RogueMob基础战斗模组", "Id": "RogueMob_BasicBattle", "Condition": ["MobAIScript.CheckRogueHasActionCanUse()"], "OnReady": [], "Action": ["MobAIScript.RogueMobBasicBattle()"]}, {"说明": "幸存者 Mob基础战斗模组", "Id": "SurvivorMob_BasicBattle", "Condition": ["MobAIScript.CheckHasActionCanUseSvl()"], "OnReady": [], "Action": ["MobAIScript.SurvivorMobBasicBattle()"]}]}