{"AIScript": [{"说明": "近距离攻击", "Id": "Titan_NearAttack_S1", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,600,-15,15)", "MobAIScript.CheckNotHasBuff(MobAttackCD)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,600,NormalAttack_X1)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "TitanTurnToStimulate", "Condition": ["OrcAIScript.CheckHasStimulate()", "MobAIScript.CheckHasEnemyInRange(200,5000)"], "OnReady": [], "Action": ["MobAIScript.AITurnToStimulateDoAction()"]}, {"说明": "发呆", "Id": "Titan_Stare01", "Condition": ["MobAIScript.Always()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(0,600,Stare01)"]}, {"Id": "Titan_Golem_SmallMoveToPlayer", "Condition": ["MobAIScript.CheckPlayerInRange(400,5000)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}]}