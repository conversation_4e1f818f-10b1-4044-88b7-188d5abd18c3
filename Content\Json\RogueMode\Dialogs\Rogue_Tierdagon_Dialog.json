{"Dialogs": [{"说明": "Rogue_Tierdagon初次对话", "Id": "Rogue_Tierdagon_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_VeryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_VeryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_VeryFirstDialog3"}], "Selections": [{"说明": "Rogue_Tierdagon故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueTierdagonStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_Tierdagon初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog7"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog8"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_First_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Tierdagon_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Tierdagon通关后初次对话", "Id": "Rogue_Tierdagon_WeeklyRoundFirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_WeeklyRound1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_WeeklyRound2"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Tierdagon_WeeklyRound_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Tierdagon默认开始对话", "Id": "Rogue_Tierdagon_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstDialog2"}], "Selections": [{"说明": "Rogue_Tierdagon故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueTierdagonStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_Tierdagon解锁职业", "特别说明": "为了实现解锁职业跳出另一个UI，然后解锁失败返回对话，解锁成功自动进入下一段对话，这里的NextEventFunc先为空，成功的NextEventFunc在解锁成功的UI蓝图里手动调用", "Conditions": ["DialogCondition.CheckCareerLock(Warrior_Tierdagon)"], "EnableChecks": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionUnlockClass", "NextEventFunc": "", "Actions": ["DialogAction.ShowUnlockCareerUI(Warrior_Tierdagon,100)"], "StillInDialog": true}, {"说明": "Rogue_Tierdagon切换职业", "Conditions": ["DialogCondition.CheckCareerUnlock(Warrior_Tierdagon)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionChangeClass", "NextEventFunc": "DialogAction.DirectGoTo(ChangeClass)", "Actions": []}, {"说明": "Rogue_Tierdagon结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "DialogAction.DirectGoTo(EndDialog)", "Actions": []}]}, {"说明": "Rogue_Tierdagon默认的重复对话", "Id": "DefaultStoryDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_GuaranteeDialog"}], "Selections": []}, {"说明": "Rogue_Tierdagon初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog7"}, {"Type": "Speak", "Id": "Step7", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialog8"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_First_StoryDialog,1)"]}, {"说明": "Rogue_Tierdagon初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialogRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialogRepeat2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_StoryFirstDialogRepeat3"}], "Selections": []}, {"说明": "Rogue_Tierdagon 别的角色死亡后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_AfterDeathDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_AfterDeathDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_AfterDeathDialog3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_AfterDeath_StoryDialog,1)"]}, {"说明": "Rogue_Tierdagon 别的角色死亡后的重复对话", "Id": "AfterDeathDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_AfterDeathDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 见到熔岩魔俑后的故事对话", "Id": "ArriveLevel20Dialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ArriveLevel10_1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ArriveLevel10_2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ArriveLevel10_3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ArriveLevel10_4"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Shard,5)", "DialogAction.SetRogueSwitch(Tierdagon_ArriveLevel20_StoryDialog,1)"]}, {"说明": "Rogue_Tierdagon 见到熔岩魔俑后的重复对话", "Id": "ArriveLevel20DialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ArriveLevel10Repeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 通关后的重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_WeeklyRoundRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 拿到1个圣杯碎片的对话", "Id": "FirstGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFirstGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFirstGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFirstGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel01_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 拿到1个圣杯碎片的重复对话", "Id": "FirstGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFirstGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 得知“圣杯誓言石”后的对话", "Id": "GetGrailInfoDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainGrailOathStone1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainGrailOathStone2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GetGrailInfo_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 得知“圣杯誓言石”后的重复对话", "Id": "GetGrailInfoDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainGrailOathStoneRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 拿到2个圣杯碎片的对话", "Id": "SecondGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainSecondGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainSecondGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel02_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 拿到2个圣杯碎片的重复对话", "Id": "SecondGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 拿到3个圣杯碎片的对话", "Id": "ThirdGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainThirdGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainThirdGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel03_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 拿到3个圣杯碎片的重复对话", "Id": "ThirdGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainThirdGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 拿到4个圣杯碎片的对话", "Id": "ForthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainForthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainForthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainForthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel04_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 拿到4个圣杯碎片的重复对话", "Id": "ForthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainForthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 拿到5个圣杯碎片的对话", "Id": "FifthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFifthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFifthGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel05_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 拿到5个圣杯碎片的重复对话", "Id": "FifthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_ObtainFifthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 解除圣杯封印的对话", "Id": "UnlockGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_UnlockGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_UnlockGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GrailLevel06_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 解除圣杯封印的重复对话", "Id": "UnlockGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_UnlockGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_KillRealDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_KillRealDeathLord2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_KillRealDeathLord3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Tierdagon 击败真·真死骸骑士的重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_KillRealDeathLordRepeat1"}], "Selections": []}, {"说明": "Rogue_Tierdagon 送魂之残响", "Id": "GiveEcho", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho2"}, {"Type": "GiveCurrencyToNPC", "Id": "Step2", "NextId": "", "Params": ["Rogue_Soul", "5", "GiveEchoSuccess", "GiveEchoFailure"]}], "Selections": []}, {"说明": "Rogue_Tierdagon 送魂之残响成功", "Id": "GiveEchoSuccess", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho4"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho5"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho6"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Tierdagon_GiveEcho,1)"]}, {"说明": "Rogue_Tierdagon 送魂之残响失败", "Id": "GiveEchoFailure", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_FirstGiveEcho8"}], "Selections": []}, {"说明": "Rogue_Tierdagon解锁职业成功并自动切换职业", "Id": "UnlockClassSuccess", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_SecondGiveEcho1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 1.5}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 2.5}], "EndClipScript": ["DialogAction.RogueChangePawn(Warrior_Tierdagon)"]}, {"说明": "Rogue_Tierdagon切换职业", "Id": "ChangeClass", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_SwitchCharacter1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 1.5}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 2.5}], "EndClipScript": ["DialogAction.RogueChangePawn(Warrior_Tierdagon)"]}, {"说明": "Rogue_Tierdagon结束对话", "Id": "EndDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Warrior_Tierdagon", "Text": "Tierdagon_EndDialog"}], "Selections": []}], "EndDialogScript": []}]}