{"ElementalTalent": [{"Id": "Fire_ChanceDamage", "Elemental": "Fire", "TriggerType": "OnHit", "BaseEffect": []}, {"Id": "Fire_BurnTarget", "Elemental": "Fire", "TriggerType": "OnHit", "BaseEffect": [], "WeatherEffect": [{"Weather": "Volcano", "Effect": ""}]}, {"Id": "Fire_Explosive", "Elemental": "Fire", "TriggerType": "OnUse", "BaseEffect": []}, {"Id": "Fire_BurningRestore", "Elemental": "Fire", "TriggerType": "Passive", "BaseEffect": [], "WeatherEffect": [{"Weather": "Volcano", "Effect": ""}]}, {"Id": "Fire_BurningPath", "Elemental": "Fire", "TriggerType": "OnUse", "BaseEffect": [], "TerrainEffect": [{"Terrain": "Grass", "Effect": ""}]}, {"Id": "Fire_Phonix", "Elemental": "Fire", "TriggerType": "Passive", "BaseEffect": [], "WeatherEffect": [{"Weather": "Volcano", "Effect": ""}]}]}