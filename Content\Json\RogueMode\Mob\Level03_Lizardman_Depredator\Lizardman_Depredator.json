{"Mob": [{"Id": "<PERSON>_Lizardman_Depredator", "MobRank": "Elite", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "Lizardman_DepredatorMoveToPlayer"], "BpPath": "Core/Characters/<PERSON>_Mob/<PERSON><PERSON><PERSON>_Depredator/Lizard<PERSON>_Depredator", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "<PERSON>_Lizardman_Depredator", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 12, "PAtk": 17, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "SpeedDownResistance", "Stack": 6000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 3000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Lizard<PERSON>_Depredator/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Lizardman_Depredator/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Hit/Break_Down"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "挑飞攻击", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "突进攻击", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "挑飞攻击2", "Id": "NormalAttack_S4", "Cmds": ["NormalAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttack_S4"]}, "InitAction": true}, {"说明": "空中攻击", "Id": "NormalAttackAir_S1", "Cmds": ["NormalAttackAir_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttackAir_S1"]}, "InitAction": true}, {"说明": "空中攻击2", "Id": "NormalAttackAir_S2", "Cmds": ["NormalAttackAir_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizard<PERSON>_Depredator/Battle/NormalAttackAir_S2"]}, "InitAction": true}, {"说明": "左瞬步", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Battle/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "右瞬步", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Battle/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "前瞬步", "Id": "Dodge_DashStep_Front", "Cmds": ["Dodge_DashStep_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/<PERSON><PERSON>/Monster/Lizard<PERSON>_Depredator/Battle/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "发呆", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Lizardman_Depredator/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "左走走", "Id": "Walk_Left", "Cmds": ["Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Movement/Walk_Left"]}, "InitAction": true}, {"说明": "右走走", "Id": "Walk_Right", "Cmds": ["Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Lizard<PERSON>_Depredator/Movement/Walk_Right"]}, "InitAction": true}]}]}