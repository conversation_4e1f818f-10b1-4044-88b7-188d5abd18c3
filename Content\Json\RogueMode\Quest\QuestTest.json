{"QuestInfo": [{"id": "TestQuest", "Desc": "Test", "State": "InActive", "Type": "Main", "StartStage": "Stage1", "StageList": [{"id": "Stage1", "Desc": "测试任务阶段1", "StageLevel": "", "InStageAction": ["进入该Stage会调用的行为"], "TargetList": ["Target1"], "NextStagePolicy": ""}, {"id": "Stage2", "Desc": "测试任务阶段2", "StageLevel": "", "InStageAction": ["进入该Stage会调用的行为"], "TargetList": ["Target2", "Target3"], "NextStagePolicy": ""}], "LootList": [], "ActiveConditions": ["TriggerScript.CheckSwitchInRoleGreater(RodianStory01,2)"], "AcceptConditions": ["TriggerScript.CheckSwitchInRoleLess(RodianStory01,4)"], "ActiveActions": ["TriggerScript.PlayerGetItemByPackageIds(Elven_GreatSword)"], "AcceptActions": ["TriggerScript.SetSwitchValueInRole(RodianStory02,3)"], "FailedActions": ["TriggerScript.SetSwitchValueInRole(RodianStory01,1)"], "CompleteActions": ["TriggerScript.PlayerGetItemByPackageIds(Elven_GreatSword)"]}], "QuestTargetInfo": [{"id": "Target1", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Kill", "UIType": "Text", "FailedCondition": [], "StartProgress": "0", "MaxProgress": "1"}, {"id": "Target2", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Kill", "UIType": "Text", "FailedCondition": [], "StartProgress": "1", "MaxProgress": "2"}, {"id": "Target3", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Kill", "UIType": "Text", "FailedCondition": [], "StartProgress": "0", "MaxProgress": "1"}]}