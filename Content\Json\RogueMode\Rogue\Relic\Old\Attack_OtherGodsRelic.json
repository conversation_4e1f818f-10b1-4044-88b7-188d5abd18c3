{"RelicInfo1": [{"分割": "-------------------------------------------Other-----------------------------------------"}, {"id": "BurnOfIgni_14", "描述": "元素伤害增加", "RelicType": "Attack", "RecordId": "88", "Desc": "BurnOfIgni_14_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_ElementalDamage"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_ElementalDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ElementalDamageUp,Rogue_ElementalDamageUp,Rogue_ElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_22", "描述": "一级元素伤害增强30", "RelicType": "Attack", "RecordId": "90", "Desc": "BurnOfIgni_22_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_BasicElementalDamage"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_BasicElementalDamageUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(BasicElementalDamageUp,Rogue_BasicElementalDamageUp,Rogue_BasicElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_23", "描述": "一级元素伤害增强20", "RelicType": "Attack", "RecordId": "91", "Desc": "BurnOfIgni_23_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_BasicElementalDamage"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_BasicElementalDamageUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(BasicElementalDamageUp,Rogue_BasicElementalDamageUp,Rogue_BasicElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_24", "描述": "一级元素伤害增强10", "RelicType": "Attack", "RecordId": "92", "Desc": "BurnOfIgni_24_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_BasicElementalDamage"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_BasicElementalDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(BasicElementalDamageUp,Rogue_BasicElementalDamageUp,Rogue_BasicElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_25", "描述": "二级元素伤害增强30", "RelicType": "Attack", "RecordId": "93", "Desc": "BurnOfIgni_25_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_AdvancedElementalDamage"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_AdvancedElementalDamageUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_26", "描述": "二级元素伤害增强20", "RelicType": "Attack", "RecordId": "94", "Desc": "BurnOfIgni_26_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_AdvancedElementalDamage"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_AdvancedElementalDamageUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_27", "描述": "二级元素伤害增强10", "RelicType": "Attack", "RecordId": "95", "Desc": "BurnOfIgni_27_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_AdvancedElementalDamage"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_AdvancedElementalDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageDown,100,1)"]}, {"id": "BurnOfIgni_28", "描述": "法器攻击力增加50%", "RelicType": "Attack", "RecordId": "177", "Desc": "BurnOfIgni_28_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_ItemDamageUp(50)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)"]}, {"id": "BurnOfIgni_29", "描述": "法器攻击力增加30%", "RelicType": "Attack", "RecordId": "188", "Desc": "BurnOfIgni_29_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_ItemDamageUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(ItemRecoverUp,RogueItemExtend,RogueItemRecover,100,-1,Other)"]}, {"id": "BurnOfIgni_30", "描述": "法器攻击力增加10%", "RelicType": "Attack", "RecordId": "189", "Desc": "BurnOfIgni_30_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_ItemDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)"]}, {"id": "ElementalExchange1", "描述": "一级元素伤害增强50%，二级元素伤害减少30%", "RelicType": "Attack", "RecordId": "190", "Desc": "ElementalExchange1_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_BasicElementalDamage", "Word_AdvancedElementalDamage"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_BasicElementalDamageUp(50)", "Rogue_AdvancedElementalDamageDown(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(BasicElementalDamageUp,Rogue_BasicElementalDamageUp,Rogue_BasicElementalDamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageDown,Rogue_AdvancedElementalDamageUp,100,-1)"]}, {"id": "ElementalExchange3", "描述": "二级元素伤害增强50%，一级元素伤害减少30%", "RelicType": "Attack", "RecordId": "192", "Desc": "ElementalExchange3_Desc", "Tags": ["OtherGods", "Group_Azouk", "Word_BasicElementalDamage", "Word_AdvancedElementalDamage"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_BasicElementalDamageDown(30)", "Rogue_AdvancedElementalDamageUp(50)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(BasicElementalDamageUp,Rogue_BasicElementalDamageDown,Rogue_BasicElementalDamageUp,100,-1)", "RelicScript.OnRelicPreview_BuffStack(AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageUp,Rogue_AdvancedElementalDamageDown,100,1)"]}, {"分割": "-------------------------------------------原暗-----------------------------------------"}, {"id": "CorruptionOfErminda_14", "描述": "对生命值低敌人攻击提升20%", "Desc": "CorruptionOfErminda_14_Desc", "RelicType": "Attack", "RecordId": "82", "Tags": ["Group_Azouk"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_DamageUpToLowHPEnemy(20)"]}, {"id": "CorruptionOfErminda_15", "描述": "自身生命值越低伤害越高", "Desc": "CorruptionOfErminda_15_Desc", "RelicType": "Attack", "RecordId": "83", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_BackWater(30)"]}, {"id": "KillOrDead", "描述": "造成伤害增加100%，受到伤害增加70%", "RelicType": "Attack", "RecordId": "85", "Desc": "KillOrDead_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 500, "RelicRarity": "Legend", "EffectBuff": ["Rogue_DamageUp(100)", "Rogue_HurtUp(70)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"id": "KillOrDead1", "描述": "造成伤害增加60%，受到伤害增加30%", "RelicType": "Attack", "RecordId": "86", "Desc": "KillOrDead1_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Rogue_DamageUp(60)", "Rogue_HurtUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"id": "KillOrDead2", "描述": "造成伤害增加30%，受到伤害增加10%", "RelicType": "Attack", "RecordId": "87", "Desc": "KillOrDead2_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_DamageUp(30)", "Rogue_HurtUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"分割": "-------------------------------------------原火-----------------------------------------"}, {"id": "BurnOfIgni_15", "描述": "火生命越高攻击力越高", "Desc": "BurnOfIgni_15_Desc", "Tags": ["Group_Azouk"], "RelicType": "Attack", "RecordId": "11", "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_NoInjury(30)"]}, {"id": "WeapeonOrGod1", "描述": "元素伤害增强50%，物理伤害减少30%", "RelicType": "Attack", "RecordId": "104", "Desc": "WeapeonOrGod1_Desc", "Tags": ["Group_Azouk", "Word_ElementalDamage"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Rogue_PhysicalDamageDown(30)", "Rogue_ElementalDamageUp(50)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ElementalDamageUp,Rogue_ElementalDamageUp,Rogue_ElementalDamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(PhysicalDamageUp,Rogue_PhysicalDamageDown,Rogue_PhysicalDamageUp,100,-1)"]}, {"id": "WeapeonOrGod2", "描述": "物理伤害增强50%，元素伤害减少30%", "RelicType": "Attack", "RecordId": "105", "Desc": "WeapeonOrGod2_Desc", "Tags": ["Group_Azouk", "Word_ElementalDamage"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Rogue_PhysicalDamageUp(50)", "Rogue_ElementalDamageDown(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(PhysicalDamageUp,Rogue_PhysicalDamageUp,Rogue_PhysicalDamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(ElementalDamageUp,Rogue_ElementalDamageDown,Rogue_ElementalDamageUp,100,-1)"]}, {"分割": "-------------------------------------------原冰-----------------------------------------"}, {"id": "ColdOfIlm_15", "描述": "冰杀敌人法器cd更快", "Desc": "ColdOfIlm_15_Desc", "RelicType": "Other", "RecordId": "27", "Tags": ["Group_Azouk"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_CD"], "IconPath": ["Rogue_Ice", "I18"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["AddItemRecoverOnKill(5)"]}, {"id": "ColdOfIlm_19", "描述": "冰对有debuff的角色伤害提高,敌人每有一个Debuff,伤害就提高10%", "RelicType": "Attack", "RecordId": "23", "Desc": "ColdOfIlm_19_Desc", "Tags": ["Group_Azouk", "Word_ElementalDebuff"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Ice", "I08"], "Value": 500, "RelicRarity": "Legend", "EffectBuff": ["Rogue_DamageUpByTargetDebuffNum(10)"]}, {"id": "ColdOfIlm_20", "描述": "冰对有debuff的角色伤害提高,敌人每有一个Debuff,伤害就提高6%", "Desc": "ColdOfIlm_20_Desc", "RelicType": "Attack", "RecordId": "24", "Tags": ["Group_Azouk", "Word_ElementalDebuff"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Ice", "I08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Rogue_DamageUpByTargetDebuffNum(6)"]}, {"id": "ColdOfIlm_21", "描述": "冰对有debuff的角色伤害提高,敌人每有一个Debuff,伤害就提高4%", "Desc": "ColdOfIlm_21_Desc", "RelicType": "Attack", "RecordId": "25", "Tags": ["Group_Azouk", "Word_ElementalDebuff"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Ice", "I08"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_DamageUpByTargetDebuffNum(4)"]}, {"id": "ColdOfIlm_22", "描述": "冰对有debuff的角色伤害提高,敌人每有一个Debuff,伤害就提高2%", "Desc": "ColdOfIlm_22_Desc", "RelicType": "Attack", "RecordId": "26", "Tags": ["Group_Azouk", "Word_ElementalDebuff"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Ice", "I08"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_DamageUpByTargetDebuffNum(2)"]}, {"分割": "-------------------------------------------原光-----------------------------------------"}, {"id": "LightOfAzem_14", "描述": "每5秒不攻击，下一击增伤50%", "RelicType": "Attack", "RecordId": "68", "Desc": "LightOfAzem_14_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Light", "L08"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_TimeIntervalDamageUp(100)"]}, {"id": "LightOfAzem_15", "描述": "光受到伤害降低", "RelicType": "Survive", "RecordId": "69", "Desc": "LightOfAzem_15_Desc", "Tags": ["Group_Azouk"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Defence"], "IconPath": ["Rogue_Light", "L11"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_HurtDown(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtDown,Rogue_HurtUp,0,1,Survive)"]}, {"id": "LightOfAzem_19", "描述": "光受到伤害降低，但造成伤害也降低", "RelicType": "Survive", "RecordId": "70", "Desc": "LightOfAzem_19_Desc", "Tags": ["Group_Azouk"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Defence"], "IconPath": ["Rogue_Light", "L11"], "Value": 160, "RelicRarity": "Normal", "EffectBuff": ["Rogue_HurtDown(20)", "Rogue_DamageDown(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtDown,Rogue_HurtUp,0,1,Survive)", "RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageDown,Rogue_DamageUp,100,-1)"]}, {"分割": "-------------------------------------------原雷-----------------------------------------"}, {"id": "ThunderOfPoltick_19", "描述": "BreakDown时提升受击伤害提升50%", "Desc": "ThunderOfPoltick_19_Desc", "RelicType": "Attack", "RecordId": "39", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 500, "RelicRarity": "Legend", "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(5000)"]}, {"id": "ThunderOfPoltick_20", "描述": "BreakDown时提升受击伤害提升30%", "Desc": "ThunderOfPoltick_20_Desc", "RelicType": "Attack", "RecordId": "40", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(3000)"]}, {"id": "ThunderOfPoltick_21", "描述": "BreakDown时提升受击伤害提升20%", "Desc": "ThunderOfPoltick_21_Desc", "RelicType": "Attack", "RecordId": "41", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(2000)"]}, {"id": "ThunderOfPoltick_15", "描述": "暴击回血", "Desc": "ThunderOfPoltick_15_Desc", "RelicType": "Survive", "RecordId": "42", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["HealHpOnCrit"]}, {"分割": "-------------------------------------------原风-----------------------------------------"}, {"id": "WindOfZantia_14", "描述": "对滞空和风切敌人伤害增加20%", "Desc": "WindOfZantia_14_Desc", "RelicType": "Attack", "RecordId": "54", "Tags": ["Group_Azouk", "Word_RogueWind"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Wind", "W08"], "Value": 360, "RelicRarity": "Epic", "EffectBuff": ["Wind_Effect_14"]}, {"id": "WindOfZantia_15", "描述": "出血", "Desc": "WindOfZantia_15_Desc", "RelicType": "Attack", "RecordId": "55", "Tags": ["Group_Azouk", "Word_RogueBlood"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Wind", "W07"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["BloodlyHit"]}, {"id": "WindOfZantia_19", "描述": "风延长一级元素Buff持续时间3s", "Desc": "WindOfZantia_19_Desc", "RelicType": "Other", "RecordId": "57", "Tags": ["Group_Azouk", "Word_ElementalDebuff"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_BuffTime"], "IconPath": ["Rogue_Wind", "W19"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_BasicElementalDurationUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_ElementalBuffExtraDuration(BuffDurationUp,Rogue_BasicElementalDurationUp,0.1,1,Other)"]}]}