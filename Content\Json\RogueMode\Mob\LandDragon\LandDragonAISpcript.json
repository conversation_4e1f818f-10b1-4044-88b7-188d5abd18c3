{"AIScript": [{"说明": "地面近距离攻击", "Id": "LandDragon_GroundSkillAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,200,-30,30)", "MobAIScript.CheckSelfOnGround()"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,300,Tai<PERSON><PERSON><PERSON><PERSON>)"]}, {"说明": "地面中远距离跳跃攻击", "Id": "LandDragon_GroundJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(100,50000,-45,45)", "AwakeDragonAIScript.CheckActionCoolDown(JumpAttack_Ground)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AddActionCoolDown(JumpAttack_Ground,30)", "MobAIScript.AITurnToClosetEnemyInRangeDoAction(100,50000,JumpAttack_Ground)"]}, {"说明": "基础地面战斗套组", "Id": "LandDragonBasicBattleOnGround", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,200)", "MobAIScript.CheckSelfOnGround()"], "OnReady": [], "Action": ["AwakeDragonAIScript.AwakeDragonBasicBattle(0,100,200)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "LandDragon_TurnToStimulate_OnGround", "Condition": ["MobAIScript.CheckHasEnemyInRange(0,500000)"], "OnReady": [], "Action": ["MobAIScript.AIMoveToClosetEnemy(0,500000)"]}, {"说明": "180度左右转身动作", "Id": "LandDragon_Turn", "Condition": ["GoblinAIScript.CheckCanTurn(0,500000,-60,60)"], "OnReady": [], "Action": ["GoblinAIScript.AttackClosetNoViewedEnemy(0,500000,TurnRight_180,TurnLeft_180)"]}]}