{"RelicInfo1": [{"分割": "-------------------------------------------物理-15个-----------------------------------------"}, {"id": "PhyscialDamage1", "描述": "物理伤害增加", "RelicType": "Attack", "RecordId": "89", "Desc": "PhyscialDamage1_Desc", "Tags": ["Group_Erictus"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_PhysicalDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(PhysicalDamageUp,Rogue_PhysicalDamageUp,Rogue_PhysicalDamageDown,100,1)"]}, {"id": "SkyDancer1", "描述": "空中物理伤害提升30%", "RelicType": "Attack", "RecordId": "96", "Desc": "SkyDancer1_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_SkyDamageUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(SkyPhyDamageUp,Rogue_SkyDamageUp,Rogue_SkyDamageDown,100,1)"]}, {"id": "SkyDancer2", "描述": "空中物理伤害提升20%", "RelicType": "Attack", "RecordId": "97", "Desc": "SkyDancer2_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_SkyDamageUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(SkyPhyDamageUp,Rogue_SkyDamageUp,Rogue_SkyDamageDown,100,1)"]}, {"id": "SkyDancer3", "描述": "空中物理伤害提升10%", "RelicType": "Attack", "RecordId": "98", "Desc": "SkyDancer3_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_SkyDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(SkyPhyDamageUp,Rogue_SkyDamageUp,Rogue_SkyDamageDown,100,1)"]}, {"id": "GroundDancer1", "描述": "地面物理伤害提升30%", "RelicType": "Attack", "RecordId": "99", "Desc": "GroundDancer1_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_GroundDamageUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(GroundPhyDamageUp,Rogue_GroundDamageUp,Rogue_GroundDamageDown,100,1)"]}, {"id": "GroundDancer2", "描述": "地面物理伤害提升20%", "RelicType": "Attack", "RecordId": "100", "Desc": "GroundDancer2_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_GroundDamageUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(GroundPhyDamageUp,Rogue_GroundDamageUp,Rogue_GroundDamageDown,100,1)"]}, {"id": "GroundDancer3", "描述": "地面物理伤害提升10%", "RelicType": "Attack", "RecordId": "101", "Desc": "GroundDancer3_Desc", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_GroundDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(GroundPhyDamageUp,Rogue_GroundDamageUp,Rogue_GroundDamageDown,100,1)"]}, {"id": "Lunara_BlowSpeed1", "描述": "上挑后2s物理伤害提升50%", "Desc": "Lunara_BlowSpeed1_Desc", "RelicType": "Attack", "RecordId": "149", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Below"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rise_AddPhysicalDamage01"]}, {"id": "Lunara_BlowSpeed2", "描述": "上挑后2s物理伤害提升30%", "Desc": "Lunara_BlowSpeed2_Desc", "RelicType": "Attack", "RecordId": "150", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Below"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rise_AddPhysicalDamage02"]}, {"id": "Lunara_SmashSpeed1", "描述": "下砸后2s物理伤害提升50%", "Desc": "Lunara_SmashSpeed1_Desc", "RelicType": "Attack", "RecordId": "153", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Smash"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H05"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Smash_AddPhysicalDamage01"]}, {"id": "Lunara_SmashSpeed2", "描述": "下砸后2s物理伤害提升30%", "Desc": "Lunara_SmashSpeed2_Desc", "RelicType": "Attack", "RecordId": "154", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Smash"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H05"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Smash_AddPhysicalDamage02"]}, {"id": "Lunara_DashSpeed1", "描述": "位移后2s物理伤害提升50%", "Desc": "Lunara_DashSpeed1_Desc", "RelicType": "Attack", "RecordId": "151", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Dash"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H04"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Dash_AddPhysicalDamage01"]}, {"id": "Lunara_DashSpeed2", "描述": "位移后2s物理伤害提升30%", "Desc": "Lunara_DashSpeed2_Desc", "RelicType": "Attack", "RecordId": "152", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Dash"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H04"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Dash_AddPhysicalDamage02"]}, {"id": "Lunara_JustDodgeSpeed1", "描述": "完美闪避后2s物理伤害提升50%", "Desc": "Lunara_JustDodgeSpeed1_Desc", "RelicType": "Attack", "RecordId": "157", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Power"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H06"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["JustDodge_AddPhysicalDamage01"]}, {"id": "Lunara_JustDodgeSpeed2", "描述": "完美闪避后2s物理伤害提升30%", "Desc": "Lunara_JustDodgeSpeed2_Desc", "RelicType": "Attack", "RecordId": "158", "Tags": ["OtherGods", "Group_Erictus", "BattleState_Power"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H06"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["JustDodge_AddPhysicalDamage02"]}]}