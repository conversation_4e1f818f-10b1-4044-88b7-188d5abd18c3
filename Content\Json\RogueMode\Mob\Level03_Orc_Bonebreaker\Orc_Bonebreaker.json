{"Mob": [{"Id": "<PERSON>_Orc_Bonebreaker", "MobRank": "Boss", "Tag": ["Orc"], "AI": ["StopAIBuff", "Or<PERSON>_Bonebreaker_ChangeStage", "RogueMob_BasicBattle", "Orc_Bonebreaker_TurnToStimulate"], "BpPath": "Core/Characters/Rogue_Mob/Orc/Orc_Bonebreaker/Orc_Bonebreaker", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "OrcNormal", "MountsTypeRotate": false, "Name": "<PERSON>_Orc_Bonebreaker", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 27, "PAtk": 17, "Balance": 10, "MoveSpeed": [165, 375, 455], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "Rogue_Boss_FirstStage", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "Rogue_Boss_CheckSecondStage", "Stack": 50, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 8000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 9000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Orc/Orc_Bonebreaker/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Hit/Break_Down"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "冲锋攻击", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "冲锋攻击", "Id": "NormalAttack_S5", "Cmds": ["NormalAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_S5"]}, "InitAction": true}, {"说明": "小沙尘暴", "Id": "MagicAttack_S1", "Cmds": ["MagicAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_M3"]}, "InitAction": true}, {"说明": "召唤小龙卷", "Id": "MagicAttack_S2", "Cmds": ["MagicAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_M1"]}, "InitAction": true}, {"说明": "骨刺攻击", "Id": "MagicAttack_S3", "Cmds": ["MagicAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_S4"]}, "InitAction": true}, {"说明": "二阶段骨刺攻击", "Id": "MagicAttack_S3_2", "Cmds": ["MagicAttack_S3_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/RageAttack_S4"]}, "InitAction": true}, {"说明": "召唤流沙", "Id": "MagicAttack_S4", "Cmds": ["MagicAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_M2"]}, "InitAction": true}, {"说明": "召唤落石", "Id": "MagicAttack_S6", "Cmds": ["MagicAttack_S6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_M4"]}, "InitAction": true}, {"说明": "二阶段召唤落石", "Id": "MagicAttack_S6_2", "Cmds": ["MagicAttack_S6_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/RageAttack_M4"]}, "InitAction": true}, {"说明": "转阶段大沙尘暴", "Id": "MagicAttack_S5", "Cmds": ["MagicAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_L1"]}, "InitAction": true}, {"说明": "召唤术1", "Id": "Summon01", "Cmds": ["Summon01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1a"]}, "InitAction": true}, {"说明": "召唤术2", "Id": "Summon02", "Cmds": ["Summon02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1b"]}, "InitAction": true}, {"说明": "召唤术3", "Id": "Summon03", "Cmds": ["Summon03"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1c"]}, "InitAction": true}, {"说明": "召唤术4", "Id": "Summon04", "Cmds": ["Summon04"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1d"]}, "InitAction": true}, {"说明": "召唤术5", "Id": "Summon05", "Cmds": ["Summon05"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1e"]}, "InitAction": true}, {"说明": "召唤术6", "Id": "Summon06", "Cmds": ["Summon06"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B1f"]}, "InitAction": true}, {"说明": "Add<PERSON><PERSON>(上Buff)", "Id": "AddBuff", "Cmds": ["AddBuff"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Battle/NormalAttack_B2"]}, "InitAction": true}, {"说明": "向左转90度", "Id": "Turn_Left90", "Cmds": ["Turn_Left90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/Turn_Left90"]}, "InitAction": true}, {"说明": "向左转180度", "Id": "Turn_Left180", "Cmds": ["Turn_Left180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/Turn_Left180"]}, "InitAction": true}, {"说明": "向右转90度", "Id": "Turn_Right90", "Cmds": ["Turn_Right90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/Turn_Right90"]}, "InitAction": true}, {"说明": "向右转180度", "Id": "Turn_Right180", "Cmds": ["Turn_Right180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/Turn_Right180"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/BoneBreaker_Idle1"]}, "InitAction": true}, {"说明": "发呆2", "Id": "Stare02", "Cmds": ["Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/BoneBreaker_Idle2"]}, "InitAction": true}, {"说明": "向左走", "Id": "WalkLeft", "Cmds": ["Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/BoneBreaker_Walk_Left"]}, "InitAction": true}, {"说明": "向右走", "Id": "WalkRight", "Cmds": ["Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Bonebreaker/Action/BoneBreaker_Walk_Right"]}, "InitAction": true}]}], "Buff": [{"Id": "Rogue_Orc_Bonebreaker_SummonCD", "Tag": ["Orc_Bonebreaker"], "Priority": 0, "MaxStack": 1}, {"Id": "<PERSON>_<PERSON><PERSON>_Bonebreaker_NextSummon", "Tag": ["Orc_Bonebreaker"], "Priority": 0, "MaxStack": 1}, {"Id": "Rogue_<PERSON>c_Bonebreaker_IgnoreAttack09", "Tag": ["Orc_Bonebreaker"], "Priority": 0, "MaxStack": 1}, {"说明": "角色造成伤害提高，每层0.01%", "Id": "<PERSON>_<PERSON><PERSON>_BoneBreaker_Summon_DamageUp", "Tag": ["<PERSON><PERSON><PERSON>", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnHit": ["BuffUtils.DamageTimesUp(0.0001)"], "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Root,Temp/Effect/ParagonZ/FX_Rampage/Particles/Abilities/JungleKing/FX/P_Rampage_Passive_Attack,Mesh)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Root)"]}], "AOE": [{"Id": "Orc_BoneBreaker_Attack05_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack05_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,3.3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_Attack06_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack06_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_BoneSpike_S_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_BoneSpike_S_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(2.7,0)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_BoneSpike_L_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_BoneSpike_L_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,3.3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_RangeBoneSpike_AOE_1", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_RangeBoneSpike_AOE_1", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,2.7)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_RangeBoneSpike_AOE_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_RangeBoneSpike_AOE_2", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_RangeBoneSpike_AOE_3", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_RangeBoneSpike_AOE_3", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,3.3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_Attack08_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack08_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_Attack09_PillarAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack09_PillarAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,1.7)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_Attack09_IgnoreDamageAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack09_IgnoreDamageAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_Attack09_AllDamageAOE", "Tag": [], "TickTime": 0.25, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack09_AllDamageAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Orc_BoneBreaker_FallingStoneAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_FallingStoneAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCasterProp(3.3,1,Physical,ExtraDamage,Blow,300,0,100,5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}], "SceneItem": [{"Id": "Orc_BoneBreaker_BoneSpike", "Tag": ["BoneSpike"], "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_BoneSpike", "LifeSpan": 12, "Part": [], "Tween": ""}, {"Id": "Orc_BoneBreaker_RangeBoneSpike", "Tag": ["BoneSpike"], "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_RangeBoneSpike", "LifeSpan": 5, "Part": [], "Tween": ""}, {"Id": "<PERSON><PERSON>_BoneBreaker_Pillar", "Tag": ["BoneSpike"], "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_Attack09_Pillar", "LifeSpan": 12, "Part": [], "Tween": ""}, {"Id": "Orc_BoneBreaker_FallingStoneManager", "Tag": ["BoneSpike"], "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_FallingStoneManager", "LifeSpan": 10, "Part": [], "Tween": ""}], "Bullet": [{"Id": "Orc_BoneBreaker_FallingStone", "Tag": [], "BpPath": "Core/Item/Monster/Orc_BoneBreaker/Orc_BoneBreaker_FallingStone", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(Orc_BoneBreaker_FallingStoneAOE,0.2)"], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(Orc_BoneBreaker_FallingStoneAOE,0.2)"]}]}