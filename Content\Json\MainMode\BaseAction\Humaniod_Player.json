{"CharacterBaseAction": [{"Id": "Player_Warrior", "Actions": [{"1": "----------------------使用道具------------------------------------------------------------------------"}, {"说明": "丢东西的瞄准", "Id": "AimThrow", "Cmds": ["Aim"], "Tags": [{"Tag": "ThrowItem_Aim", "From": 0}], "BeCancelledTags": {"0": ["ThrowItem_Aim", "UnarmedDodge", "UnarmedJump", "UnarmedJump_GSword", "UnarmedJump_TwinSword", "UnarmedJump_Spear", "UnarmedJump_SwordShield"]}, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/ThrowAim"]}}, {"说明": "瞄准时把道具丢出去", "Id": "AimThrowOut", "Cmds": ["Action4"], "Tags": [{"Tag": "ThrowItem_Aim", "From": 0}], "BeCancelledTags": {"0": ["ThrowItem_Aim"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/ThrowItem_Aim"]}}, {"说明": "直接把道具丢出去", "Id": "ThrowOut", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}], "BeCancelledTags": {"0": ["UnarmedMove", "UnarmedDodge", "UnarmedJump"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/ThrowItem"]}}, {"说明": "火球术卷轴", "Id": "FireBallScroll", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/ThrowItem_FireExploScroll"]}}, {"说明": "举着火把", "Id": "<PERSON><PERSON>_<PERSON>ch", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/HoldItem_LHand_Torch"]}}, {"说明": "喝药水的动作", "Id": "DrinkPotion", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Drink"]}}, {"说明": "交互", "Id": "Interact", "Cmds": ["Interactive"], "Tags": [{"Tag": "UnarmedMove", "From": 0}, {"Tag": "UnarmedJump_GSword", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/PickUp"]}}]}, {"Id": "Player_Warrior_PreLoad", "Actions": [{"Line": "_______________________________战士_觉醒动作_______________________________"}, {"Id": "Swordsman_AwakeSkill_1", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Awake/Protecter_Awake1_Montage"]}, "Cost": {"AP": 200}}, {"Id": "Swordsman_AwakeSkill_2", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Awake/Protecter_Awake2_Montage"]}, "Cost": {"AP": 200}}, {"Id": "Swordsman_AwakeSkill_1_Cancel", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Awake/Protecter_Awake_Cancel_Montage"]}, "Cost": {}}]}]}