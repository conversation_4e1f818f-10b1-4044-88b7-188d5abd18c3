{"Shop_Example": [{"Id": "这个商店的id", "FailSFX": "交易失败时候发出的音效，例如：Audio/Sound_Cue/Character/Female/Chara_Voice/Mage_Effort_Land_Cue", "Deals": [{"说明": "这个商店每一样东西卖的是什么", "Id": "这笔交易的id，之后做log时候要用，其实填空没啥关系，就是查bug会够你受的", "Name": "这笔交易的显示名称", "Icon": "这样东西陈列的图标路径，ArtResource及后续路径", "SFX": "交易成功时发出的音效，例如：Audio/Sound_Cue/Character/Female/Chara_Voice/Hurt", "VFX": "交易成功时的视觉特效", "Description": "这笔交易的描述信息，如果没有填写，就会把Good里面第一个作为内容（如果Good存在的话，不然就会是一句不太好听的话）", "Price": [{"说明": "买这个东西的标价的things", "Type": "建议填写（也是默认值）为Currency，其他的不建议出现在标价，但不是不行", "Id": "对应type下对应id的东西", "Count": "需要多少个这个东西"}], "Good": [{"说明": "能买到的东西things", "Type": "是一个什么类型的东西，推荐范围：Equipment,Item,WeaponModel", "Id": "对应type下对应id的东西", "Count": "<int>个数，当然一笔交易推荐（且默认为1个）"}], "Appearance": [{"说明": "放在店铺货柜里的物品模型，这是允许多个放一块算一个的", "BluePrintPath": "模型的蓝图路径，例如：Core/Characters/Equipment/HealingPotion", "Position": {"X": "<float>这个模型对于要放下去的位置的偏移坐标", "Y": 0, "Z": 0}, "Rotation": {"Roll": "<float>这个模型放下去的旋转", "Yaw": 0, "Pitch": 0}, "Scale": {"X": "<float>这个模型放下去的缩放", "Y": 3.5, "Z": 3.5}}], "Condition": ["这笔交易的额外条件，(FDeal Deal, FAwRoleInfo Role, TArray<FString>)=>bool", "必须condition的每个函数都返回true，这笔交易才可以进行"], "Times": "<int>这笔交易最多可以进行多少次，买完一辈子就没得再买了，除非数据遭到刷新", "Infinity": "<bool>这笔交易是否无限次，如果是true，那么times写多少都没意义   "}], "OnDeal": ["每次完成一笔交易会回调的函数，(FTrading Trading, FDeal Deal, TArray<FString> Param)=>FTrading", "每一项会依次执行，其return值会被赋值给这个商店，传入的第一个参数则是赋值前、交易后的商店信息", "建议一个都不写，如果要交易完成来一句“刀模阿里嘎多”，就写个简单的"], "Currency": [{"描述": "这家商店里使用的货币的图标，其实每一家都应该统一的，但是未必是如此的，策划可以配置不一样的图标给货币，但是仅限货币有效", "Id": "Currency的id，比如Gold之类的", "Icon": "这种货币的图标路径，如果写了才会被显示，不显示的货币，并不影响其逻辑有效"}]}]}