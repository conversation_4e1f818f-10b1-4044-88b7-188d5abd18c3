{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "<String>BUFF的ID", "BuffStack": "<int>需要的BUFF层数", "Weight": "<float>在该BUFF大于等于指定层数后的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "OnGround": "<float>敌人在地面上时的权重加成", "InAir": "<float>敌人在空中时的权重加成", "InDodge": "<float>敌人正在翻滚时的权重加成", "InHurt": "<float>敌人正在受击时的权重加成", "MinActionCD": "<float>最小CD时间", "MaxActionCD": "<float>最大CD时间"}]}}, {"Id": "<PERSON>_GiantSpider", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 350, "Weight": -99}, {"MinRange": 350, "MaxRange": 700, "Weight": 5}, {"MinRange": 700, "MaxRange": 9999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-60", "MaxRange": "60", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 0}], "OutofCamera": -6, "MinActionCD": 0, "MaxActionCD": 10}, {"Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": 7}, {"MinRange": 200, "MaxRange": 99999, "Weight": -999999}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 0}], "MinActionCD": 0, "MaxActionCD": 8}, {"Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 700, "Weight": -99}, {"MinRange": 700, "MaxRange": 1500, "Weight": 5}, {"MinRange": 1500, "MaxRange": 9999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-60", "MaxRange": "60", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 0}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 5, "MaxActionCD": 15}, {"Id": "Action_Stare01", "BaseWeight": 5, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 5}, {"MinRange": 500, "MaxRange": 9999, "Weight": -99}], "Front": 1, "Back": 1, "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 9}, {"Id": "Action_Stare02", "BaseWeight": 5, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1500, "Weight": 5}, {"MinRange": 1500, "MaxRange": 99999, "Weight": -99}], "Front": 1, "Back": 1, "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 9}]}]}