{"Class": [{"说明": "勇士-精灵施琳-<PERSON><PERSON><PERSON>", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["BladeDancer", "<PERSON><PERSON><PERSON>", "Warrior", "Warrior"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Tierdagon", "StateActions": {"Ground": {"Armed": "GSword_Move", "UnArmed": "GSword_Unarmed_Move"}, "Flying": {"Armed": "GSword_Move", "UnArmed": "GSword_Unarmed_Move"}, "Falling": {"Armed": "GSword_Fall", "UnArmed": "GSword_Unarmed_Fall"}, "Attached": {"Armed": "GSword_Ride", "UnArmed": "GSword_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "GSword_Hurt", "UnArmed": "GSword_Hurt"}, "Blow": {"Armed": "GSword_Blow", "UnArmed": "GSword_Blow"}, "Frozen": {"Armed": "GSword_Frozen", "UnArmed": "GSword_Frozen"}, "Bounced": {"Armed": "Warrior_Bounced", "UnArmed": "Warrior_Bounced"}, "Dead": {"Armed": "GSword_Dead", "UnArmed": "GSword_Dead"}, "Landing": {"Armed": "GSword_JustFall", "UnArmed": "GS<PERSON>_Unarmed_JustFall"}, "SecondWind": {"Armed": "GSword_SecWind", "UnArmed": "GSword_SecWind"}, "GetUp": {"Armed": "GSword_RevivedOnSecWind", "UnArmed": "GSword_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 890], "BeStrikeRate": 1.0, "CriticalChance": 0.05, "CriticalRate": 1.4, "AirDodgePoint": 1}, "WeaponType": "BigSword", "DefaultWeapons": ["Iron_GreatSword"], "ActionOnChangeTo": "ChangeToGreatSworder", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________大剑徒手基础动作________________________________"}, {"说明": "大剑徒手走路站立", "Id": "GSword_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive", "GSword_SkillAttack"], "1": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive"]}, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/UnarmedMove", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/BS_Throw_Aim"]}}, {"说明": "大剑徒手起跳", "Id": "GSword_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "GSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["GSword_Air_DrawAttack", "GSword_AirSkillAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "大剑徒手翻滚", "Id": "GS<PERSON>_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GS<PERSON>_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["GSword_Unarmed_Move", "GSword_Unarmed_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Dodge_F_Unarm"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "大剑徒手下落", "Id": "GSword_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "GSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/UnarmedFall"]}, "Priority": 1}, {"说明": "大剑徒手下落着地", "Id": "GS<PERSON>_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "GSword_Unarmed_Jump", "Interactive", "GSword_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/UnarmedJustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "大剑收刀", "Id": "SheathGreatSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "GSword_SheathWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/SheathWeapon"]}}, {"说明": "大剑拔刀", "Id": "DrawGreatSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "GSword_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/DrawWeapon"]}}, {"Line": "_______________________________大剑(LevelSquencer)动作________________________________"}, {"说明": "Rogue大厅开场动作", "Id": "Rogue_Hall_Begin", "Cmds": ["Rogue_Hall_Begin"], "Tags": [{"Tag": "Rogue_Hall_Begin", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_Begin_Hall"]}, "InitAction": true}, {"说明": "Rogue大厅重生动作", "Id": "<PERSON>_<PERSON>_Respawn", "Cmds": ["<PERSON>_<PERSON>_Respawn"], "Tags": [{"Tag": "<PERSON>_<PERSON>_Respawn", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_Begin_DeathRespawn_Hall"]}, "InitAction": true}, {"说明": "Rogue房间开始动作", "Id": "Rogue_Room_Start", "Cmds": ["Rogue_Room_Start"], "Tags": [{"Tag": "Rogue_Room_Start", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_Begin_Dungeon"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_01", "Cmds": ["Rogue_SecondRoundEndSeq_01"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Sequence"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_SecondRoundEndSeq_01"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_02", "Cmds": ["Rogue_SecondRoundEndSeq_02"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_SecondRoundEndSeq_02"]}, "InitAction": true}, {"说明": "Rogue31关最终结束动画", "Id": "Rogue_FinalSeq", "Cmds": ["Rogue_FinalSeq"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Rogue_FinalSeq"]}, "InitAction": true}, {"Line": "_______________________________大剑(持武器)基础动作________________________________"}, {"Id": "GSword_Move", "Cmds": ["Move"], "Tags": [{"Tag": "GSword_Move", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"], "1": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/Move_AimState"]}}, {"Id": "GSword_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["GSword_Dodge"], "2": ["GSword_QS_B"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Hurt_Air"]}}, {"Id": "GSword_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_QS_B"], "1": ["GSword_QS_F"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Blow_Front"]}}, {"Id": "GSword_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Death_Loop"]}, "InitAction": true}, {"Id": "GSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "GSword_Jump", "From": 0}, {"Tag": "GSword_Dodge", "From": 0}, {"Tag": "GSword_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_Air_Dodge_Step"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "GSword_Fall", "Cmds": [], "Tags": [{"Tag": "GSword_Jump", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/Fall"]}, "Priority": 1}, {"Id": "GSword_JustFall", "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump", "GSword_Dodge", "Interactive", "GSword_InitAttack"]}, "CanUseOnFalling": true, "Priority": 1, "Anim": {"Period": true, "StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/JustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Movement/JustFallMoving"]}, "CanStopSprint": false}, {"Id": "GSword_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Move", "GSword_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "GSword_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "GSword_Air_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Air_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_Air_Dodge_Step_Second"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Air_Dodge_Dash"]}, "InitAction": true}, {"Id": "GSword_Air_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Air_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Air_Dodge_Dash_Second"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "GSword_Just_Dodge_Success", "Cmds": ["_"], "Tags": [{"Tag": "GSword_Just_Dodge_Success", "From": 0}], "BeCancelledTags": {"0": ["GSword_SkillAttack", "GSword_Dodge_Step", "GSword_Just_Dodge_CounterAtk", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Dodge_F_Just_Success"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "GSword_Just_Dodge_Success_CounterAtk", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "GSword_Just_Dodge_CounterAtk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_JustDodge_CounterAtk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "GSword_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "GSword_QS_B", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge", "GSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身动作前翻", "Id": "GSword_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "GSword_QS_F", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge", "GSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "GSword_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/SecondWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "GSword_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 999, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/RevivedOnSecondWind"]}}, {"Line": "_______________________________大剑_防御_动作_______________________________"}, {"Id": "Warrior_Defense", "Cmds": ["Aim"], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "GSword_Defense", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Defense"]}}, {"说明": "防御成功", "Id": "Warrior_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge", "GSword_Defense", "Unarm_UseItem"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Defense_Success"]}}, {"Line": "_______________________________大剑受击(特殊)动作________________________________"}, {"Id": "GSword_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Hit/Hurt_Frozen"]}}, {"Line": "_______________________________大剑基础(附加)动作________________________________"}, {"说明": "弹刀动作", "Id": "Warrior_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive"]}, "Priority": 30, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Bounced"]}}, {"说明": "瞄准动作", "Id": "GSword_Aim", "Cmds": [], "Tags": [{"Tag": "GSword_Aim", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge", "GSword_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Warrior_Aim"]}}, {"Line": "_______________________________大剑战斗触发动作________________________________"}, {"说明": "连招后续:命中时的升飞斩(自动触发的移动到上面去了)", "Id": "Warrior_StingUpperSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingUpperCut"]}}, {"说明": "连招后续:冲刺斩命中时的连续斩(自动触发的移动到上面去了)", "Id": "Warrior_StingComboSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingComboSlash"]}}, {"Line": "_______________________________法器反击_______________________________"}, {"说明": "法器反击成功了就自动变成这个了(仅用于justblock)", "Id": "FrozenParry_JustBlock", "Cmds": [], "Tags": [{"Tag": "FrozenParry_JustBlock", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_Parry_CounterAtk"]}}, {"Line": "_______________________________大剑命令AI队友________________________________"}], "RogueBattleActions": [{"Line": "_______________________________大剑_普通攻击_动作_______________________________"}, {"Line": "_______________________________大剑_普攻_地面_______________________________"}, {"Id": "Warrior_LAttack01", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_LAttack1", "From": 0}, {"Tag": "GSword_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_LAttack2"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["GSword_AttackB1", "GSword_LAttack2_T1"], "4": ["GSword_SpcSkillAttack"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Slash0"]}, "InitAction": true}, {"Id": "Warrior_LAttack02", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack3", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Slash1"]}, "InitAction": true}, {"Id": "Warrior_LAttack03", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_SkillAttack", "Unarm_UseItem", "GSword_InitAttack"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack"], "3": ["-"], "4": ["GSword_SpcSkillAttack"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Slash2"]}, "InitAction": true}, {"说明": "大剑分支普通攻击1", "Id": "Warrior_AttackB1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "GSword_AttackB1", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_AttackB2"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_SlashB1"]}, "InitAction": true}, {"说明": "大剑分支普通攻击2", "Id": "Warrior_AttackB2", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "GSword_AttackB2", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_AttackB3"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_SlashB2"]}, "InitAction": true}, {"说明": "大剑分支普通攻击3", "Id": "Warrior_AttackB3", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "GSword_AttackB3", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_InitAttack"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_SlashB3"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击2", "Id": "Warrior_LAttack02_T1", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack2_T1", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack3_T1"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Slash1_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击3", "Id": "Warrior_LAttack03_T1", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack3_T1", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack4_T1", "GSword_InitAttack"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Slash2_T1"]}, "InitAction": true}, {"Line": "_______________________________大剑_普攻_空中_______________________________"}, {"Id": "Warrior_AirLAttack1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack1", "GSword_Air_Dodge_Step"], "1": ["GSword_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_Slash0"]}, "InitAction": true}, {"Id": "Warrior_AirLAttack2", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "AirLAttack1", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack"], "1": ["GSword_AirSkillAttack", "GSword_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_Slash1"]}, "InitAction": true}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/PickUp"]}}, {"Line": "_______________________________大剑战斗动作_______________________________"}, {"Line": "_______________________________大剑_新技能_地面空中Action________________________________"}, {"说明": "狼跃斩", "Id": "Warrior_RiseUpSlash", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["GSword_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Berserk_RiseSlash2"]}, "Cost": {"MP": 0}}, {"说明": "落鹰击", "Id": "Warrior_BeatDownSlash", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_BeatDownSlash"]}, "Cost": {"MP": 0}}, {"说明": "破魔律法", "Id": "Warrior_DashDownSlash", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_DashDownSlash"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_技能A(蓄力)_地面Action2________________________________"}, {"Id": "Warrior_ChargeAttack", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_PowerSlash"]}, "Cost": {"MP": 0}}, {"Id": "Warrior_ChargeJumpAttack", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_PowerJumpSlash"]}}, {"Id": "Warrior_ChargeSlashSwordAttack", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_PowerSword"]}, "Cost": {"MP": 0}}, {"Id": "Warrior_ChargeWhirlwind", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_PowerWhirlwind"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_技能A(蓄力)_空中Action2________________________________"}, {"Id": "Warrior_AirChargeAttack", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge_Step", "GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_PowerSlash1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_技能B(位移)_地面Action3________________________________"}, {"说明": "提剑前戳突刺，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingDashAttack"]}, "Cost": {"MP": 0}}, {"说明": "", "Id": "Warrior_StingDashAttack_UpperSlash", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingDashAttack_UpperSlash"]}, "Cost": {"MP": 0}}, {"说明": "", "Id": "<PERSON>_Sting<PERSON>ash<PERSON><PERSON>ck_ComboSlash", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingDashAttack_ComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "连招后续:命中时的升飞斩(自动触发的移动到上面去了)", "Id": "<PERSON>_StingUpperSlash_temp", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingUpperCut"]}}, {"说明": "连招后续:冲刺斩命中时的连续斩(自动触发的移动到上面去了)", "Id": "<PERSON>_StingComboSlash_temp", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_StingComboSlash"]}}, {"说明": "向前冲刺横扫，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "Warrior_DashSlashAttack1", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "Warrior_DashSlashAttack2", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_DashSlash0"]}}, {"说明": "向前冲刺横扫第二下", "Id": "Warrior_DashSlashAttack2", "Cmds": [], "Tags": [{"Tag": "Warrior_DashSlashAttack2", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_DashSlash1"]}}, {"Line": "_______________________________大剑_技能B(位移)_空中Action3________________________________"}, {"Id": "Warrior_AirDoubleStrike0", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["Warrior_AirDoubleStrike1"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_Strike0"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Warrior_AirDoubleStrike1", "Cmds": [], "Tags": [{"Tag": "Warrior_AirDoubleStrike1", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge_Step", "GSword_AirInitAttack", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_Strike1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_技能C(特殊)_地面Action4________________________________"}, {"说明": "防御动作，成功后进入反击动作", "Id": "<PERSON><PERSON><PERSON>", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "GSword_SpcSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Guard"]}, "Cost": {"MP": 0}}, {"说明": "防御成功了就自动变成这个了(仅用于justblock)", "Id": "Warrior_CounterDashAttack_JustBlock", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_CounterDashAttack_JustBlock"]}}, {"说明": "防御成功了就自动变成这个了", "Id": "Warrior_CounterDashAttack", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_CounterDashAttack"]}}, {"说明": "防御动作B，成功后进入反击动作", "Id": "Warrior_ParryB", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_Guard_B"]}}, {"说明": "防御B成功了就自动变成这个动作了", "Id": "Warrior_CounterStep_Back", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/G_CounterStep_Back"]}}, {"Line": "_______________________________大剑_技能C(特殊)_空中Action4________________________________"}, {"说明": "猎魔突袭斩", "Id": "Warrior_AirRush_DemonSlayer", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "DemonSlayer", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_Rush"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "下落斩", "Id": "Warrior_Air_FallSlash", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/A_FallSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑(狂战士型态)________________________________"}, {"Line": "_______________________________大剑(狂战士型态)基础动作________________________________"}, {"Id": "GSword_Berserk_Move", "Cmds": ["Move"], "Tags": [{"Tag": "GSword_Move_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_Jump_Bsk", "GSword_Dodge_Bsk", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"], "1": ["GSword_InitAttack_Bsk", "GSword_Jump_Bsk", "GSword_Dodge_Bsk", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/Berserk_Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/WarriorElf/Move_AimState"]}}, {"Line": "_______________________________大剑(狂战士型态)基础动作________________________________"}, {"Id": "GSword_Berserk_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Dodge_F_Berserk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "GS<PERSON>_<PERSON><PERSON><PERSON>_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Step_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Dodge/Step_F_Berserk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Line": "_______________________________大剑(狂战士型态)战斗动作________________________________"}, {"Line": "_______________________________大剑_普攻(狂战士型态)_地面Action1________________________________"}, {"Id": "<PERSON>_<PERSON><PERSON>rk_LAttack01", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_LAttack2", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"], "3": ["GSword_AttackB1_Bsk"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_Slash0"]}, "InitAction": true}, {"Id": "Warrior_<PERSON><PERSON>rk_LAttack02", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack2_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack3_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_Slash1"]}, "InitAction": true}, {"Id": "<PERSON>_<PERSON><PERSON><PERSON>_LAttack03", "Cmds": [], "Tags": [{"Tag": "GSword_LAttack3_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Berserk_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_Slash2"]}, "InitAction": true}, {"说明": "大剑分支普通攻击2", "Id": "Warrior_Be<PERSON>rk_LAttackB2", "Cmds": [], "Tags": [{"Tag": "GSword_AttackB1_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_AttackB2_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_WolfJumpSlash"]}, "InitAction": true}, {"说明": "大剑分支普通攻击3", "Id": "Warrior_<PERSON><PERSON>rk_LAttackB3", "Cmds": [], "Tags": [{"Tag": "GSword_AttackB2_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_AttackB3_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_Dash2CombpSlash"]}, "InitAction": true}, {"说明": "大剑分支普通攻击4", "Id": "Warrior_<PERSON><PERSON>rk_LAttackB4", "Cmds": [], "Tags": [{"Tag": "GSword_AttackB3_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_SlashB3"]}, "InitAction": true}, {"Line": "_______________________________大剑_普攻(狂战士型态)_空中Action1________________________________"}, {"Id": "Warrior_Berserk_AirLAttack0", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack1_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"CheckOnTick": false, "StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_Slash0"]}, "InitAction": true}, {"Id": "Warrior_Berserk_AirLAttack1", "Cmds": [], "Tags": [{"Tag": "AirLAttack1_Bsk", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack2_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_Slash1"]}, "InitAction": true}, {"Id": "Warrior_Berserk_AirLAttack2", "Cmds": [], "Tags": [{"Tag": "AirLAttack2_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"], "3": ["GSword_AirSkillAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_Slash2"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能A(狂战士型态、飞升)_地面Action2________________________________"}, {"Id": "<PERSON>_<PERSON><PERSON>rk_SkillA", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_RiseSlash2"]}}, {"Line": "_______________________________大剑_技能A(狂战士型态、下落)_空中Action2________________________________"}, {"Id": "Warrior_Berserk_AirSkillA", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_DownSlash"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能B(狂战士型态、位移)_地面Action3________________________________"}, {"说明": "提剑前戳突刺，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "<PERSON>_<PERSON><PERSON><PERSON>_SkillB", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_DashUpSlash_Slow"]}}, {"Line": "_______________________________大剑_技能B(狂战士型态、位移)_空中Action3________________________________"}, {"Id": "Warrior_Berserk_AirSkillB", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_CircleDownSlash"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能C(狂战士型态、开启/关闭)_地面Action4________________________________"}, {"说明": "进入狂战士型态", "Id": "Warrior_Berserk_BerserkState", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}, {"Tag": "GSword_SpcSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/G_Berserk_State"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_技能C(狂战士型态、开启)_空中Action4________________________________"}, {"说明": "进入狂战士型态", "Id": "Warrior_Berserk_AirBerserkState", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}, {"Tag": "GSword_SpcSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Berserk/A_Berserk_State"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_动作连招式_轻重击_______________________________"}, {"说明": "大剑地面普攻1", "Id": "Warrior_Attack_GN1", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN1", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN2", "GSword_Attack_GH2"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["GSword_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN1"]}, "InitAction": true}, {"说明": "大剑地面普攻2", "Id": "Warrior_Attack_GN2", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN2", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN3", "GSword_Attack_GH3"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN2"]}, "InitAction": true}, {"说明": "大剑地面普攻3", "Id": "Warrior_Attack_GN3", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN3", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN4", "GSword_Attack_GH4"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN3"]}, "InitAction": true}, {"说明": "大剑地面普攻4", "Id": "Warrior_Attack_GN4", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN4", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN5", "GSword_Attack_GH5"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN4"]}, "InitAction": true}, {"说明": "大剑地面普攻5", "Id": "Warrior_Attack_GN5", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN5", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN6", "GSword_Attack_GH6"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN5"]}, "InitAction": true}, {"说明": "大剑地面普攻6", "Id": "Warrior_Attack_GN6", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN6", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN7", "GSword_Attack_GH7"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN6"]}, "InitAction": true}, {"说明": "大剑地面普攻7", "Id": "Warrior_Attack_GN7", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GN7", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GN7"]}, "InitAction": true}, {"说明": "大剑地面重攻1", "Id": "Warrior_Attack_GH1", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH1", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_AN1", "GSword_Attack_AH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻2", "Id": "Warrior_Attack_GH2", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH2", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻3", "Id": "Warrior_Attack_GH3", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH3", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_AN1", "GSword_Attack_AH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻4", "Id": "Warrior_Attack_GH4", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH4", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH4"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻5", "Id": "Warrior_Attack_GH5", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH5", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH5"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻6", "Id": "Warrior_Attack_GH6", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH6", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH6"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面重攻7", "Id": "Warrior_Attack_GH7", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_GN1", "GSword_Attack_GH1"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_GH7"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面空中普攻1", "Id": "Warrior_Attack_AN1", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_AN1", "From": 0}, {"Tag": "GSword_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_AN2", "GSword_Attack_AH1", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Dodge_Step", "GSword_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_AN1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面空中普攻2", "Id": "Warrior_Attack_AN2", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_AN2", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_AN3", "GSword_Attack_AH1", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Dodge_Step", "GSword_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_AN2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面空中普攻3", "Id": "Warrior_Attack_AN3", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_AN3", "From": 0}], "BeCancelledTags": {"0": ["GSword_Attack_AH1", "GSword_AirSkillAttack", "GSword_Air_Dodge_Step"], "1": ["GSword_Dodge_Step", "GSword_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_AN3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "大剑地面空中重攻1", "Id": "Warrior_Attack_AH1", "Cmds": [], "Tags": [{"Tag": "GSword_Attack_AH1", "From": 0}, {"Tag": "GSword_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "Unarm_UseItem"], "1": ["GSword_Dodge_Step", "GSword_Defense"], "2": ["GSword_SkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Battle/Attack_AH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________大剑_普攻_攀爬Action1________________________________"}, {"Line": "_______________________________大剑_普攻_疾跑Action1________________________________"}, {"Line": "_______________________________？________________________________"}], "RogueBattleStyle": ["WarriorStyle_1", "WarriorStyle_2", "WarriorStyle_3"]}], "Buff": [], "Aoe": []}