{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Rogue_Fire_Element_Wolf", "Actions": [{"Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": -99}, {"MinRange": 500, "MaxRange": 1000, "Weight": 5}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 1}, {"MinRange": 300, "MaxRange": 800, "Weight": 5}, {"MinRange": 800, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 1}, {"MinRange": 300, "MaxRange": 800, "Weight": 5}, {"MinRange": 800, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 800, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 13, "MaxActionCD": 28}, {"Id": "Action_Stare03", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 5, "MaxActionCD": 7}, {"Id": "Action_Stare04", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 600, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 7}, {"Id": "Action_Stare05", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 7}, {"Id": "Dodge_DashStep_Right", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 4}, {"Id": "Dodge_DashStep_Left", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 4}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 4}]}]}