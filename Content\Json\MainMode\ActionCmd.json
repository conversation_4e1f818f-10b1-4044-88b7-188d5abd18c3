{"ActionCmd": [{"------": "-------------------- Game --------------------"}, {"Action": "Action0", "ActionKey": ["Key_Action0", "AI_Action0"], "State": ["Game"]}, {"Action": "Action1", "ActionKey": ["Key_Rect", "AI_Action1"], "State": ["Game"]}, {"Action": "Action2", "ActionKey": ["Key_Triangle", "AI_Action2"], "State": ["Game"]}, {"Action": "Action3", "ActionKey": ["Key_R1", "AI_Action3"], "State": ["Game"]}, {"Action": "Action4", "ActionKey": ["Key_R2", "AI_Action4"], "State": ["Game"]}, {"Action": "Action5", "ActionKey": ["Key_Action5", "AI_Action5"], "State": ["Game"]}, {"Action": "Action6", "ActionKey": ["Key_Action6", "AI_Action6"], "State": ["Game"]}, {"Action": "Action7", "ActionKey": ["Key_Action7", "AI_Action7"], "State": ["Game"]}, {"Action": "Action8", "ActionKey": ["Key_Action8", "AI_Action8"], "State": ["Game"]}, {"Action": "Action9", "ActionKey": ["Key_Action9", "AI_Action9"], "State": ["Game"]}, {"Action": "Action10", "ActionKey": ["Key_Rect0", "AI_Action10"], "State": ["Game"]}, {"Action": "Action11", "ActionKey": ["Key_Rect1", "AI_Action11"], "State": ["Game"]}, {"Action": "Action12", "ActionKey": ["Key_Rect2", "AI_Action12"], "State": ["Game"]}, {"Action": "Action13", "ActionKey": ["Key_Rect3", "AI_Action13"], "State": ["Game"]}, {"Action": "Action14", "ActionKey": ["Key_Rect4", "AI_Action14"], "State": ["Game"]}, {"Action": "Action15", "ActionKey": ["Key_Rect5", "AI_Action15"], "State": ["Game"]}, {"Action": "Action16", "ActionKey": ["Key_Rect6", "AI_Action16"], "State": ["Game"]}, {"Action": "Action17", "ActionKey": ["Key_Rect7", "AI_Action17"], "State": ["Game"]}, {"Action": "Action18", "ActionKey": ["Key_Rect8", "AI_Action18"], "State": ["Game"]}, {"Action": "Action19", "ActionKey": ["Key_Rect9", "AI_Action19"], "State": ["Game"]}, {"Action": "Action20", "ActionKey": ["Key_Action20", "AI_Action20"], "State": ["Game"]}, {"Action": "Action21", "ActionKey": ["Key_Action21", "AI_Action21"], "State": ["Game"]}, {"Action": "Action22", "ActionKey": ["Key_Action22", "AI_Action22"], "State": ["Game"]}, {"Action": "Action23", "ActionKey": ["Key_Action23", "AI_Action23"], "State": ["Game"]}, {"Action": "Action24", "ActionKey": ["Key_Action24", "AI_Action24"], "State": ["Game"]}, {"Action": "Action25", "ActionKey": ["Key_Action25", "AI_Action25"], "State": ["Game"]}, {"Action": "AwakeSkill", "ActionKey": ["Key_AwakeSkill"], "State": ["Game"]}, {"Action": "Sprint", "ActionKey": ["Key_L3", "AI_Sprint"], "State": ["Game"]}, {"Action": "Jump", "ActionKey": ["Key_Cross", "AI_Jump"], "State": ["Game"]}, {"Action": "Dodge", "ActionKey": ["Key_Eclipse", "AI_Dodge"], "State": ["Game"]}, {"Action": "<PERSON>rab", "ActionKey": ["Key_ComboRectCross", "AI_Grab"], "State": ["Game"]}, {"Action": "Hook", "ActionKey": ["Key_ComboTriangleEclipse", "AI_Hook"], "State": ["Game"]}, {"Action": "Aim", "ActionKey": ["Key_L2"], "State": ["Game"]}, {"Action": "PauseMenu", "ActionKey": ["Key_Start"], "State": ["Game"]}, {"Action": "ResetCamera", "ActionKey": ["Key_R3"], "State": ["Game"]}, {"Action": "UseItem", "ActionKey": ["Key_UseItem", "AI_UseItem"], "State": ["Game"]}, {"Action": "ItemLeft", "ActionKey": ["Key_ItemLeft", "AI_ItemLeft"], "State": ["Game"]}, {"Action": "ItemRight", "ActionKey": ["Key_ItemRight", "AI_ItemRight"], "State": ["Game"]}, {"Action": "ToggleWeapon", "ActionKey": ["Key_ToggleWeapon"], "State": ["Game"]}, {"Action": "Interactive", "ActionKey": ["Key_L1", "AI_Interactive"], "State": ["Game"]}, {"Action": "ShowBigMap", "ActionKey": ["Key_BigMap", "AI_Interactive"], "State": ["Game"]}, {"Action": "HideBigMap", "ActionKey": ["Key_BigMap", "AI_Interactive"], "State": ["PauseMenu"]}, {"------": "-------------------- Game 弃用 --------------------"}, {"Action": "MainMenu", "ActionKey": ["Key_SelectX", "AI_MainMenu"], "State": ["Game"]}, {"Action": "SystemMenu", "ActionKey": ["Key_SystemMenu", "AI_SystemMenu"], "State": ["Game"]}, {"Action": "Map", "ActionKey": ["Key_Select", "AI_Map"], "State": ["Game"]}, {"------": "-------------------- PauseMenu --------------------"}, {"Action": "PauseResume", "ActionKey": ["Key_Start"], "State": ["PauseMenu"]}, {"Action": "Menu_Up", "ActionKey": ["Key_Up"], "State": ["PauseMenu"]}, {"Action": "Menu_Down", "ActionKey": ["Key_Down"], "State": ["PauseMenu"]}, {"Action": "Menu_Left", "ActionKey": ["Key_Left"], "State": ["PauseMenu"]}, {"Action": "Menu_Right", "ActionKey": ["Key_Right"], "State": ["PauseMenu"]}, {"Action": "Menu_Confirm", "ActionKey": ["Key_Cross"], "State": ["PauseMenu"]}, {"Action": "<PERSON>u_Refuse", "ActionKey": ["Key_Eclipse"], "State": ["PauseMenu"]}, {"------": "-------------------- ChangeSkill<PERSON>rClass --------------------"}, {"Action": "ChangeSkillOrClass_Up", "ActionKey": ["Key_Up"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Down", "ActionKey": ["Key_Down"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Left", "ActionKey": ["Key_Left"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Right", "ActionKey": ["Key_Right"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Confirm", "ActionKey": ["Key_Cross"], "State": ["ChangeSkillOrClass"]}, {"Action": "ChangeSkillOrClass_Refuse", "ActionKey": ["Key_Eclipse"], "State": ["ChangeSkillOrClass"]}, {"------": "-------------------- Dialog --------------------"}, {"Action": "StopDialog", "ActionKey": ["Key_Eclipse"], "State": ["Dialog"]}, {"Action": "DialogConfirm", "ActionKey": ["Key_Cross"], "State": ["Dialog"]}, {"Action": "DialogUp", "ActionKey": ["Key_Up"], "State": ["Dialog"]}, {"Action": "DialogDown", "ActionKey": ["Key_Down"], "State": ["Dialog"]}, {"------": "-------------------- MessageDialog --------------------"}, {"Action": "MessageDialog_Yes", "ActionKey": ["Key_Cross"], "State": ["MessageDialog"]}, {"Action": "MessageDialog_Cancel", "ActionKey": ["Key_Eclipse"], "State": ["MessageDialog"]}, {"------": "-------------------- NewbieHint --------------------"}, {"Action": "<PERSON><PERSON>_Confirm", "ActionKey": ["Key_Cross"], "State": ["NewbieHint"]}, {"Action": "<PERSON><PERSON>_Left", "ActionKey": ["Key_Left"], "State": ["NewbieHint"]}, {"Action": "<PERSON>bie_Right", "ActionKey": ["Key_Right"], "State": ["NewbieHint"]}, {"------": "-------------------- Shopping --------------------"}, {"Action": "Shop_Up", "ActionKey": ["Key_Up"], "State": ["Shopping"]}, {"Action": "Shop_Down", "ActionKey": ["Key_Down"], "State": ["Shopping"]}, {"Action": "Shop_Left", "ActionKey": ["Key_Left"], "State": ["Shopping"]}, {"Action": "Shop_Right", "ActionKey": ["Key_Right"], "State": ["Shopping"]}, {"Action": "Shop_Confirm", "ActionKey": ["Key_Cross"], "State": ["Shopping"]}, {"Action": "Shop_Refuse", "ActionKey": ["Key_Eclipse"], "State": ["Shopping"]}, {"------": "-------------------- Title --------------------"}, {"Action": "Title_Up", "ActionKey": ["Key_Up"], "State": ["Title"]}, {"Action": "Title_Down", "ActionKey": ["Key_Down"], "State": ["Title"]}, {"Action": "Title_Left", "ActionKey": ["Key_Left"], "State": ["Title"]}, {"Action": "Title_Right", "ActionKey": ["Key_Right"], "State": ["Title"]}, {"Action": "Title_Eclipse", "ActionKey": ["Key_Eclipse"], "State": ["Title"]}, {"Action": "Title_Cross", "ActionKey": ["Key_Cross"], "State": ["Title"]}, {"------": "-------------------- Death --------------------"}, {"Action": "Death_Confirm", "ActionKey": ["Key_Cross"], "State": ["Death"]}, {"===============": "============================= Rougelike ============================================="}, {"------": "-------------------- SelectionRoom --------------------"}, {"Action": "SelectionRoom_Left", "ActionKey": ["Key_Left"], "State": ["SelectionRoom"]}, {"Action": "SelectionRoom_Right", "ActionKey": ["Key_Right"], "State": ["SelectionRoom"]}, {"Action": "SelectionRoom_Confirm", "ActionKey": ["Key_Cross"], "State": ["SelectionRoom"]}, {"------": "-------------------- AwakeSkill --------------------"}, {"Action": "AwakeSkill_Left", "ActionKey": ["Key_Left"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Right", "ActionKey": ["Key_Right"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Confirm", "ActionKey": ["Key_Cross"], "State": ["AwakeSkill"]}, {"Action": "AwakeSkill_Refuse", "ActionKey": ["Key_Eclipse"], "State": ["AwakeSkill"]}, {"------": "-------------------- RogueRewards --------------------"}, {"Action": "RogueRewards_Left", "ActionKey": ["Key_Left"], "State": ["RogueRewards"]}, {"Action": "RogueRewards_Right", "ActionKey": ["Key_Right"], "State": ["RogueRewards"]}, {"Action": "RogueRewards_Confirm", "ActionKey": ["Key_Cross"], "State": ["RogueRewards"]}, {"------": "-------------------- 通用 确认或取消时的情况用 Reminder --------------------"}, {"Action": "Reminder_Confirm", "ActionKey": ["Key_Cross"], "State": ["Reminder"]}, {"Action": "Reminder_Refuse", "ActionKey": ["Key_Eclipse"], "State": ["Reminder"]}, {"------": "-------------------- Rogue --------------------"}, {"Action": "DrinkPotion_Rogue", "ActionKey": ["Key_UseItem"], "State": ["Game"]}, {"Action": "UseItem_Rogue", "ActionKey": ["Key_L2"], "State": ["Game"]}], "DefaultKeyMapping": [{"ActionKey": "Key_Start", "Gamepad": ["Gamepad_Special_Right"], "Keyboard": ["Escape"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Options_Lined_128x128", "KeyboardIcon": "P"}, {"ActionKey": "Key_L2", "Gamepad": ["Gamepad_LeftTrigger"], "Keyboard": ["RightMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_L2_Lined_128x128", "KeyboardIcon": "MouseR"}, {"ActionKey": "Key_R1", "Gamepad": ["Gamepad_RightShoulder"], "Keyboard": ["Two"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_R1_Lined_128x128", "KeyboardIcon": "R1"}, {"ActionKey": "Key_R2", "Gamepad": ["Gamepad_RightTrigger"], "Keyboard": ["Three"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_R2_Lined_128x128", "KeyboardIcon": "RT"}, {"ActionKey": "Key_L1", "Gamepad": ["Gamepad_LeftShoulder"], "Keyboard": ["F"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_L1_Lined_128x128", "KeyboardIcon": "F"}, {"ActionKey": "Key_L3", "Gamepad": ["Gamepad_LeftThumbstick"], "Keyboard": ["LeftShift"], "Key_LeftGamepKey_UpadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_LS_L3_Lined_128x128", "KeyboardIcon": "Shift"}, {"ActionKey": "Key_R3", "Gamepad": ["Gamepad_RightThumbstick"], "Keyboard": ["MiddleMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_RS_R3_Lined_128x128", "KeyboardIcon": "Shift"}, {"ActionKey": "Key_Rect", "Gamepad": ["Gamepad_FaceButton_Left"], "Keyboard": ["LeftMouseButton"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Sq_Lined_128x128", "KeyboardIcon": "MouseL"}, {"ActionKey": "Key_Triangle", "Gamepad": ["Gamepad_FaceButton_Top"], "Keyboard": ["One"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Tri_Lined_128x128", "KeyboardIcon": "MouseM"}, {"ActionKey": "Key_Cross", "Gamepad": ["Gamepad_FaceButton_Bottom"], "Keyboard": ["SpaceBar"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_X_Lined_128x128", "KeyboardIcon": "SpaceBar"}, {"ActionKey": "Key_Eclipse", "Gamepad": ["Gamepad_FaceButton_Right"], "Keyboard": ["LeftControl"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_El_Lined_128x128", "KeyboardIcon": "Z"}, {"ActionKey": "Key_Eclipse2", "Gamepad": [], "Keyboard": ["LeftAlt"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_El_Lined_128x128", "KeyboardIcon": "Z"}, {"ActionKey": "Key_ComboRectCross", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Base_Sq_Lined_128x128", "KeyboardIcon": "E"}, {"ActionKey": "Key_ComboTriangleEclipse", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Base_El_Lined_128x128", "KeyboardIcon": "Q"}, {"ActionKey": "Key_Select", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Share_Lined_128x128", "KeyboardIcon": "TAB"}, {"ActionKey": "Key_Up", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["W"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "Key_Left", "Gamepad": ["Gamepad_DPad_Left"], "Keyboard": ["A"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UD_Lined_128x128", "KeyboardIcon": "Left"}, {"ActionKey": "Key_Right", "Gamepad": ["Gamepad_DPad_Right"], "Keyboard": ["D"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Right"}, {"ActionKey": "Key_Down", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["S"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"ActionKey": "Key_SelectX", "Gamepad": [], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Tpad_Lined_128x128", "KeyboardIcon": "M"}, {"ActionKey": "Key_AwakeSkill", "Gamepad": ["Gamepad_LeftThumbstick", "Gamepad_RightThumbstick"], "Keyboard": ["E"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Base_Sq_Lined_128x128", "KeyboardIcon": "E"}, {"ActionKey": "Key_UseItem", "Gamepad": ["Gamepad_DPad_Up"], "Keyboard": ["C"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "Key_ItemLeft", "Gamepad": ["Gamepad_DPad_Left"], "Keyboard": [], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UD_Lined_128x128", "KeyboardIcon": "Left"}, {"ActionKey": "Key_ItemRight", "Gamepad": ["Gamepad_DPad_Right"], "Keyboard": ["Tab"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Right"}, {"ActionKey": "Key_ToggleWeapon", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["X"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"ActionKey": "Key_BigMap", "Gamepad": ["Gamepad_DPad_Down"], "Keyboard": ["M"], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_UR_Lined_128x128", "KeyboardIcon": "Down"}, {"v": "Action的LeftStick和RightStick的Up Left Right Down会有特别处理，因为本来输入方式就不太一样"}, {"ActionKey": "LeftStick_Up", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Down", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Left", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "LeftStick_Right", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Up", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Down", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Left", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}, {"ActionKey": "RightStick_Right", "Gamepad": [""], "Keyboard": [""], "GamepadIcon": "ArtResource/UI/Icon/Controller/Gamepad/Macro_Icons/DS4_Controller/Outline/T_DS_Dpad_U_Lined_128x128", "KeyboardIcon": "Up"}]}