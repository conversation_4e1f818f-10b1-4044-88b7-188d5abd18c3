{"AIScript": [{"Id": "OgreProvoke", "Condition": ["OgreAIScript.AllPlayerDown()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(TauntAction01)", "OgreAIScript.TestShowState(OgreProvoke)"], "Tag": "Stare"}, {"说明": "测测动作", "Id": "OgreTestAction", "Condition": [], "OnReady": [], "Action": ["OgreAIScript.OgreBasicBattle()"], "_Action": ["MobAIScript.ModifyFightingWillLevel(0)", "OgreAIScript.OgreBasicBattle()"], "Tag": "Stare"}, {"说明": "测测动作", "Id": "OgreTest", "Condition": [""], "OnReady": [], "XAction": ["MobAIScript.AIDoAction(CallPillar)"], "Action": ["OgreAIScript.OgreBasicBattle()"], "Tag": "Stare"}, {"说明": "基础战斗套组", "Id": "OgreBasicBattle", "Condition": [""], "OnReady": [], "Action": ["OgreAIScript.OgreBasicBattle()"], "Tag": "Stare"}, {"说明": "Rogue基础战斗套组", "Id": "RogueOgreBasicBattle", "Condition": [""], "OnReady": [], "Action": ["OgreAIScript.OgreBasicBattle(Rogue_Ogre)"], "Tag": "Stare"}, {"说明": "把柱子丢出去", "Id": "OgreThrowPillar", "Condition": ["MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["MobAIScript.AITurnToPlayerThenDoAction(Attack_XL1)", "OgreAIScript.TestShowState(OgreThrowPillar)"], "Tag": "Attack"}, {"说明": "已经走到柱子附近了，看看能不能抱起来", "Id": "OgrePullUpPillar", "Condition": ["OgreAIScript.CheckNearPillar()", "MobAIScript.CheckNotHasBuff(OgreWithPillar)", "MobAIScript.CheckHasBuff(CanPullUpPillar,1)"], "OnReady": [], "Action": ["OgreAIScript.PullUpPillar(PickUpPillar)", "OgreAIScript.TestShowState(OgrePickupPillar)"], "Tag": "Special"}, {"说明": "检查是否要走到柱子附近", "Id": "OgreMoveToNearestPillar", "OldCondition": ["OgreAIScript.CheckHasPillarInArea(1000)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "Condition": ["OgreAIScript.CheckWillingToTakePillar(1000,1.2)"], "OnReady": [], "Action": ["OgreAIScript.MoveToNearestPillar()", "OgreAIScript.TestShowState(MoveToNearestPillar)"], "Tag": "Move"}, {"Id": "OgreRage", "Condition": ["MobAIScript.CheckFightingWillLevelEquals(2)", "MobAIScript.CheckFightingWillValueGreater(9000)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageState)", "OgreAIScript.TestShowState(Rage)"], "Tag": "Special"}, {"Id": "OgreWeak", "Condition": ["MobAIScript.CheckFightingWillLevelEquals(0)", "MobAIScript.CheckFightingWillValueGreater(9000)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(ChangeToWeak)", "OgreAIScript.TestShowState(Weak)"], "Tag": "Special"}, {"说明": "召唤柱子", "Id": "OgreCreatePillar", "Condition": ["MobAIScript.CheckFightingWillLevelEquals(2)", "OgreAIScript.CheckNoPillarInArea(6)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)", "MobAIScript.CheckHasBuff(OgreRage,1)"], "_Condition": [], "OnReady": [], "Action": ["MobAIScript.AIDoAction(CallPillar)", "OgreAIScript.TestShowState(OgreCreatePillar)"], "Tag": "Special"}, {"Id": "OgreAttackVeryNearViewdEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,300)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,300,Attack_S1)", "MobAIScript.SetComboTag(AttackNearEnemyCombo)", "OgreAIScript.TestShowState(Attack Very Near)"], "Tag": "Attack"}, {"Id": "OgreAttackVeryNearViewdEnemy_WithPillar", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,300)", "MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,300,Pillar_Attack_S1)", "MobAIScript.SetComboTag(PillarAttackNearEnemyCombo)"], "Tag": "Attack"}, {"Id": "OgreAttackNearViewdEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,500)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,500,Attack_M1)", "OgreAIScript.TestShowState(Attack Near)"], "Tag": "Attack"}, {"Id": "OgreAttackNearViewdEnemy_WithPillar", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,500)", "MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,500,Pillar_Attack_M1)"], "Tag": "Attack"}, {"Id": "OgreAttackMiddleViewdEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1000)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackMiddleViewdEnemy(0,1000,Attack_L1,Attack_L2)", "OgreAIScript.TestShowState(Attack Middle Range)"], "Tag": "Attack"}, {"Id": "OgreAttackMiddleViewdEnemy_WithPillar", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1000)", "MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,1000,Pillar_Attack_L1)"], "Tag": "Attack"}, {"Id": "OgreAttackFarViewdEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1000,5000)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(1000,5000,Attack_L1)", "OgreAIScript.TestShowState(Attack Long Range)"], "Tag": "Attack"}, {"Id": "OgreAttackFarViewdEnemy_WithPillar", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1000,5000)", "MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(1000,5000,Attack_XL1)"], "Tag": "Attack"}, {"Id": "OgreAttackHigherEnemy", "Condition": ["OgreAIScript.CheckViewedEnemyHeight(50)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackHigherEnemy(50,Attack_L1)", "OgreAIScript.TestShowState(Attack Higher)"], "Tag": "Attack"}, {"Id": "OgreAttackHigherEnemy_WithPillar", "Condition": ["OgreAIScript.CheckViewedEnemyHeight(50)", "MobAIScript.CheckHasBuff(OgreWithPillar,1)"], "OnReady": [], "Action": ["OgreAIScript.AttackHigherEnemy(50,Attack_XL1)"], "Tag": "Attack"}, {"Id": "OgreStare", "Condition": ["MobAIScript.CheckFightingWillLevelEquals(0)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(Stare03,Stare05)", "OgreAIScript.TestShowState(Stare)"], "Tag": "Stare"}, {"Id": "OgreStare_WithPillar", "Condition": ["MobAIScript.CheckFightingWillLevelEquals(0)", "MobAIScript.CheckHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Stare01)"], "Tag": "Stare"}, {"Id": "OgreAttackOutViewEnemy", "Condition": ["MobAIScript.CheckNotStimulateByView()", "MobAIScript.CheckHasEnemyInRange(500)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackClosetNoViewedEnemy(0,500,Attack_S3_TurnRAtk,Attack_S4_TurnLAtk)", "OgreAIScript.TestShowState(Attack Out RAnge)"], "Tag": "Attack"}, {"Id": "OgreAttackOutViewEnemy_WithPillar", "Condition": ["MobAIScript.CheckNotStimulateByView()", "MobAIScript.CheckHasEnemyInRange(500)", "MobAIScript.CheckHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackClosetNoViewedEnemy(0,500,<PERSON><PERSON>_Attack_S3_TurnRAtk,<PERSON><PERSON>_Attack_S4_TurnLAtk)"], "Tag": "Attack"}, {"Id": "OgreTurn", "Condition": ["MobAIScript.CheckNotStimulateByView()", "MobAIScript.CheckHasEnemyInRange(500,5000)", "MobAIScript.CheckNotHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackClosetNoViewedEnemy(500,5000,TurnR,TurnL)", "OgreAIScript.TestShowState(Turn)"], "Tag": "Stare"}, {"Id": "OgreTurn_WithPillar", "Condition": ["MobAIScript.CheckNotStimulateByView()", "MobAIScript.CheckHasEnemyInRange(500,5000)", "MobAIScript.CheckHasBuff(OgreWithPillar)"], "OnReady": [], "Action": ["OgreAIScript.AttackClosetNoViewedEnemy(500,5000,<PERSON><PERSON>_TurnR,<PERSON>llar_TurnL)"], "Tag": "Stare"}, {"Id": "OgreTestAction", "Condition": [], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Attack_S1)"], "Tag": ""}]}