{"AiActionDetails范例": [{"AiActionId": "一个Action的Id 对应Acitions字段内的对象Id", "ActionDurationPolicy解释": "Durantion的配置策略 对应一个脚本内返回值为float的函数,配置参数外默认有Action拥有角色的指针传入,配置参数任意 最终为字符串数组 由策略函数解析 会覆盖配置的Duration值", "ActionDurationPolicy": "ParamPolicyScript.FloatFromRange(1,2) 例为 从1-2中随机一个float作为durantion", "ActionType解释": "动作类型 为Instant,HasDuration,Loop 三种所有动作不配置情况下默认为Instant", "Instant解释": "只执行一次,动作动画播完后就结束", "HasDuration解释": "具有持续时间,如果动作动画时间大于Duration则和Instant没什么区别,如果持续时间大于>动作动画时间则在动作后保持基础动画停留直到目标时间(动作时间消耗算在总停留时间内)", "Loop解释": "循环动作,此时Durantion意味着循环间隔时间,如果<=0则是无缝循环,直到有新的动作意愿能打断当前动作,此类型尚未完全实现 不要使用", "ActionType": "HasDuration", "Duration": "<float> 例如 1 "}]}