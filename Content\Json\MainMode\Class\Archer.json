{"Class": [{"说明": "神射手", "Id": "<PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["_"], "Buffs": ["Warrior_Passive", "Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "LongBow_Move", "UnArmed": "LongBow_Unarmed_Move"}, "Flying": {"Armed": "LongBow_Move", "UnArmed": "LongBow_Unarmed_Move"}, "Falling": {"Armed": "LongBow_Fall", "UnArmed": "LongBow_Unarmed_Fall"}, "Attached": {"Armed": "LongBow_Ride", "UnArmed": "LongBow_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "LongBow_Hurt", "UnArmed": "LongBow_Hurt"}, "Blow": {"Armed": "LongBow_Blow", "UnArmed": "LongBow_Blow"}, "Frozen": {"Armed": "LongBow_Frozen", "UnArmed": "LongBow_Frozen"}, "Bounced": {"Armed": "LongBow_Bounced", "UnArmed": "LongBow_Bounced"}, "Dead": {"Armed": "LongBow_Dead", "UnArmed": "LongBow_Dead"}, "Landing": {"Armed": "LongBow_JustFall", "UnArmed": "<PERSON>B<PERSON>_<PERSON>rmed_JustFall"}, "SecondWind": {"Armed": "LongBow_SecWind", "UnArmed": "LongBow_SecWind"}, "GetUp": {"Armed": "LongBow_RevivedOnSecWind", "UnArmed": "LongBow_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "LongBow", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ClassBuff": [], "Actions": [{"Line": "_______________________________长弓徒手基础动作________________________________"}, {"说明": "长弓徒手走路站立", "Id": "LongBow_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Unarmed_Jump", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Dodge", "LongBow_Aim", "LongBow_DrawWeapon", "Unarm_UseItem", "LongBow_DrawAttack", "Interactive"], "1": ["LongBow_Unarmed_Jump", "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Dodge", "LongBow_Aim", "LongBow_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "长弓徒手起跳", "Id": "LongBow_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "LongBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "长弓徒手翻滚", "Id": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON><PERSON>_<PERSON><PERSON>_Dodge", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Move", "LongBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"说明": "长弓徒手下落", "Id": "LongBow_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "LongBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/UnarmedFall"]}, "Priority": 1}, {"说明": "长弓徒手下落着地", "Id": "<PERSON>B<PERSON>_<PERSON>rmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "LongBow_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "长弓收刀", "Id": "SheathLongBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "LongBow_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/SheathWeapon"]}}, {"说明": "长弓拔刀", "Id": "DrawLongBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "LongBow_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/DrawWeapon"]}}, {"Line": "_______________________________长弓(LevelSquencer)动作________________________________"}, {"说明": "长弓趴地上_LevelSquencer用", "Id": "FallDown_Loop", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_LongBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Archer_FallDown_Loop"]}}, {"说明": "长弓趴地上起来_LevelSquencer用", "Id": "FallDown_End", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_LongBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Archer_FallDown_End"]}}, {"说明": "长弓_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________长弓(持武器)基础动作________________________________"}, {"Id": "LongBow_Move", "Cmds": ["LongBow_Move"], "Tags": [{"Tag": "LongBow_Move", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_Jump", "LongBow_Dodge", "LongBow_Aim", "LongBow_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["LongBow_InitAttack", "LongBow_Jump", "LongBow_Dodge", "LongBow_Aim", "LongBow_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/Defense", "ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/Defense"]}}, {"Id": "LongBow_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_HurtCounter"], "1": ["LongBow_Dodge"], "2": ["LongBow_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Hurt_Air"]}}, {"Id": "LongBow_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_QS_B"], "1": ["LongBow_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/Blow_Front"]}}, {"Id": "LongBow_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "LongBow_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "LongBow_Jump", "From": 0}, {"Tag": "LongBow_Dodge", "From": 0}, {"Tag": "LongBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "LongBow_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirInitAttack"], "1": ["LongBow_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Archer_<Type>/Fall"]}, "Priority": 1}, {"Id": "LongBow_JustFall", "BeCancelledTags": {"0": ["LongBow_Move", "LongBow_Jump", "LongBow_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "LongBow_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "LongBow_Dodge", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Move", "LongBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON><PERSON>_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Move", "LongBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 800}, "InitAction": true}, {"Id": "LongBow_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "LongBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_Jump", "LongBow_Dodge", "LongBow_Aim", "LongBow_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 1200}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "LongBow_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "LongBow_QS_B", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Dodge", "LongBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 1600}}, {"说明": "受身动作前翻", "Id": "LongBow_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "LongBow_QS_F", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Dodge", "LongBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 1600}}, {"说明": "倒地动作", "Id": "LongBow_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "LongBow_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________长弓特殊动作________________________________"}, {"Id": "LongBow_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "LongBow_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Jump/AttachOnTarget"]}}, {"Id": "LongBow_Ride", "Cmds": [], "Tags": [{"Tag": "LongBow_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["LongBow_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________长弓受击(特殊)动作________________________________"}, {"Id": "LongBow_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________长弓基础(附加)动作________________________________"}, {"说明": "剑与盾盾", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/ChangeToArcher1"]}}, {"说明": "长弓弹刀动作", "Id": "LongBow_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_Jump", "LongBow_Dodge", "LongBow_Aim", "LongBow_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/Bounced"]}}, {"说明": "瞄准动作", "Id": "LongBow_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_SkillAttack", "From": 0}, {"Tag": "LongBow_Defense", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge"], "2": ["LongBow_Defense_Attack1", "LongBow_Defense_Attack2", "LongBow_Defense_Attack3"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/PickUp"]}}, {"Line": "_______________________________长弓命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "LongBow_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "LongBow_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["LongBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/OrderBuddy/OrderBuddyMoveToTarget"]}}, {"Line": "_______________________________长弓战斗动作_______________________________"}, {"Line": "_______________________________长弓_普攻_地面Action1_______________________________"}, {"Id": "Archer_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_LAttack1", "From": 0}, {"Tag": "LongBow_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_LAttack2"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"], "3": ["LongBow_BranchAttack2"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Slash0"]}, "InitAction": true}, {"Id": "Archer_LAttack02", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_LAttack3"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Slash1"]}, "InitAction": true}, {"Id": "Archer_LAttack03", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_LAttack1"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Slash2"]}, "InitAction": true}, {"说明": "长弓分支普通攻击2", "Id": "Archer_BranchAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_BranchAttack3"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_SlashB1"]}, "InitAction": true}, {"说明": "长弓分支普通攻击3", "Id": "Archer_BranchAttack3", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_BranchAttack4"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_SlashB2"]}, "InitAction": true}, {"说明": "长弓分支普通攻击4", "Id": "Archer_BranchAttack4", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_BranchAttack5"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_SlashB3"]}, "InitAction": true}, {"说明": "长弓分支普通攻击5", "Id": "Archer_BranchAttack5", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_BranchAttack5", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_InitAttack"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_SlashB4"]}, "InitAction": true}, {"Line": "_______________________________长弓_普攻_空中Action1_______________________________"}, {"Id": "Archer_AirAttack1", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_AirInitAttack", "From": 0}, {"Tag": "LongBow_AirAttack1", "From": 0}, {"Tag": "LongBow_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirAttack2", "LongBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/A_Slash0"]}, "InitAction": true}, {"Id": "Archer_AirAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirAttack1"], "1": ["_"], "2": ["LongBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________长弓_技能A_地面Action2_______________________________"}, {"Id": "Archer_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirInitAttack"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_RiseSlash"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Id": "Archer_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "LongBow_HurtCounter", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirInitAttack"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_RiseSlash_Counter"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"Id": "Archer_RiseComboSlash", "Cmds": ["_"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_AirInitAttack"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Rise2Slash"]}, "InitAction": true, "Cost": {"MP": 1200}}, {"Line": "_______________________________长弓_技能A_空中Action2_______________________________"}, {"Id": "Archer_AirDownSlashAttack1", "Cmds": ["Action2"], "Tags": [{"Tag": "LongBow_AirInitAttack", "From": 0}, {"Tag": "LongBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/A_DashSlash_FallDown"]}, "InitAction": true, "Cost": {"MP": 300}}, {"Line": "_______________________________长弓_技能B_地面Action3_______________________________"}, {"Id": "Archer_DashSlash", "Cmds": ["Action3"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_DashAttack2", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_DashSlash"]}, "Cost": {"MP": 1200}}, {"Id": "Archer_DashSlash2", "Cmds": ["Action3"], "Tags": [{"Tag": "LongBow_DashAttack2", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_DashSlash1"]}, "Cost": {"MP": 1200}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Archer_DashSpike_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_DashSpike_Combo"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON>_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_AirInitAttack", "LongBow_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_RiseSlash"]}}, {"Line": "_______________________________长弓_技能B_空中Action3_______________________________"}, {"Id": "Archer_AirDashSting", "Cmds": ["Action3"], "Tags": [{"Tag": "LongBow_AirInitAttack", "From": 0}, {"Tag": "LongBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/A_DashSting_Forward"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Line": "_______________________________长弓_技能C(防御)_地面Action4_______________________________"}, {"Id": "Archer_Defense", "Cmds": ["Action4"], "Tags": [{"Tag": "LongBow_InitAttack", "From": 0}, {"Tag": "LongBow_SkillAttack", "From": 0}, {"Tag": "LongBow_Defense", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_Defense_Attack1", "LongBow_Defense_Attack2", "LongBow_Defense_Attack3"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense"]}}, {"Line": "_______________________________长弓_防御_动作_______________________________"}, {"说明": "防御成功", "Id": "Archer_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "MontageAnimPickFunc.GetActionByPriorityDistance(4)", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_Success_Big", "ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_Success_Small"]}, "Cost": {"SP": 1800, "ReplaceAction": "Archer_Defense_Success_Broken"}}, {"说明": "防御成功_但体力不足_破防", "Id": "Archer_Defense_Success_Broken", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_Success_Broken"]}}, {"说明": "完美防御_成功(justblock)", "Id": "Archer_Defense_JustBlock", "Cmds": [], "Tags": ["LongBow_JustBlock"], "BeCancelledTags": {"0": ["LongBow_JustBlock_Success_Attack"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_JustBlock_Success"]}}, {"说明": "*完美防御_成功的后续动作1", "Id": "Archer_Defense_JustBlock_Success_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Defense_Attack1x", "LongBow_Defense_Attack2x", "LongBow_Defense_Attack3x"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_JustBlock_ShieldStrike"]}}, {"说明": "*完美防御_成功的后续动作2", "Id": "Archer_Defense_JustBlock_Success_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "LongBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Defense_Attack1x", "LongBow_Defense_Attack2x", "LongBow_Defense_Attack3x"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_JustBlock_ShieldUpperStrike"]}}, {"说明": "*完美防御_成功的后续动作3", "Id": "Archer_Defense_JustBlock_Success_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "LongBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Defense_Attack1x", "LongBow_Defense_Attack2x", "LongBow_Defense_Attack3x"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_JustBlock_DashSlash"]}}, {"说明": "*完美防御_成功的后续动作4", "Id": "Archer_Defense_JustBlock_Success_Attack4", "Cmds": ["Action4"], "Tags": [{"Tag": "LongBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack", "LongBow_Defense_Attack1x", "LongBow_Defense_Attack2x", "LongBow_Defense_Attack3x"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_JustBlock_GroundSmash"]}}, {"说明": "防御时Action1的动作", "Id": "Archer_Defense_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_Defense_Attack1", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_Defense_Attack1_2", "LongBow_Defense_Attack2", "LongBow_Defense_Attack3"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_InitAttack", "LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_ShieldSweapSmash0"]}, "InitAction": true}, {"说明": "防御时Action1的连招动作", "Id": "Archer_Defense_Attack1_2", "Cmds": ["Action1"], "Tags": [{"Tag": "LongBow_Defense_Attack1_2", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_Defense", "Archer_Defense_Attack1", "LongBow_Defense_Attack2", "LongBow_Defense_Attack3"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_InitAttack", "LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_ShieldSweapSmash1"]}, "InitAction": true}, {"说明": "防御时Action2的动作", "Id": "Archer_Defense_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "LongBow_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_Defense", "LongBow_Defense_Attack1", "LongBow_Defense_Attack3"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_InitAttack", "LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_ShieldUpperSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action3的动作", "Id": "Archer_Defense_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "LongBow_Defense_Attack3", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_Defense", "LongBow_Defense_Attack1", "LongBow_Defense_Attack2"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_InitAttack", "LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_ShieldChargeSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action2的动作", "Id": "Archer_Defense_Attack2B", "Cmds": [], "Tags": [{"Tag": "LongBow_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["LongBow_Defense"], "1": ["LongBow_Dodge_Step"], "2": ["LongBow_InitAttack", "LongBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_CounterSlash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时，Acion2的完美防御成功了就自动变成这个了(仅用于justblock)", "Id": "Archer_Defense_CounterSlash_JustBlock", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/G_Defense_CounterSlash_JustBlock3"]}}, {"Line": "_______________________________长弓_技能C_空中Action4_______________________________"}, {"Id": "Archer_AirDownShieldSmash", "Cmds": ["Action4"], "Tags": [{"Tag": "LongBow_AirInitAttack", "From": 0}, {"Tag": "LongBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["LongBow_InitAttack", "LongBow_SkillAttack"], "1": ["LongBow_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Archer_<Type>/Attack/LongBow/A_ShieldSmash_FallDown"]}, "InitAction": true, "Cost": {"MP": 1500}}]}], "Buff": [], "Aoe": []}