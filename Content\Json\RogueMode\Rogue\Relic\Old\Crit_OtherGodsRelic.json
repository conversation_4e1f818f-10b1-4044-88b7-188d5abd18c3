{"RelicInfo1": [{"分割": "-------------------------------------------暴击-14个-----------------------------------------"}, {"id": "Ardipeng_1", "描述": "每个圣遗物提升暴击率0.8%", "RelicType": "Attack", "RecordId": "123", "Desc": "Ardipeng_1_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_RelicCastCriticalChance(80)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtRate(CtRate,Rogue_RelicCastCriticalChance,0.0001,999,100)"]}, {"id": "Ardipeng_2", "描述": "每个神提升暴击率2%", "RelicType": "Attack", "RecordId": "124", "Desc": "Ardipeng_2_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_RelicGodCastCriticalChance(200)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_GodTagCastCtRate(CtRate,Rogue_RelicGodCastCriticalChance,0.0001,100)"]}, {"id": "Ardipeng_4", "描述": "每个圣遗物提升暴击伤害1.6%", "RelicType": "Attack", "RecordId": "126", "Desc": "Ardipeng_4_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_RelicCastCriticalRate(160)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtDmg(CtDmg,Rogue_RelicCastCriticalRate,0.0001,999,100)"]}, {"id": "Ardipeng_5", "描述": "每个神提升暴击伤害5%", "RelicType": "Attack", "RecordId": "127", "Desc": "Ardipeng_5_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_RelicGodCastCriticalRate(500)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_GodTagCastCtDmg(CtDmg,Rogue_RelicGodCastCriticalRate,0.0001,100)"]}, {"id": "Ardipeng_CriticalChanceUp1", "描述": "暴击率增加20%", "RelicType": "Attack", "RecordId": "131", "Desc": "Ardipeng_CriticalChanceUp1_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_CriticalChanceUp(2000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtRate,Rogue_CriticalChanceUp,100)"]}, {"id": "Ardipeng_CriticalChanceUp2", "描述": "暴击率增加12%", "RelicType": "Attack", "RecordId": "132", "Desc": "Ardipeng_CriticalChanceUp2_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_CriticalChanceUp(1200)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtRate,Rogue_CriticalChanceUp,100)"]}, {"id": "Ardipeng_CriticalChanceUp3", "描述": "暴击率增加8%", "RelicType": "Attack", "RecordId": "133", "Desc": "Ardipeng_CriticalChanceUp3_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_CriticalChanceUp(800)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtRate,Rogue_CriticalChanceUp,100)"]}, {"id": "Ardipeng_CriticalChanceUp4", "描述": "暴击率增加5%", "RelicType": "Attack", "RecordId": "134", "Desc": "Ardipeng_CriticalChanceUp4_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 4, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_CriticalChanceUp(500)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtRate,Rogue_CriticalChanceUp,100)"]}, {"id": "Ardipeng_CriticalRateUp1", "描述": "暴击伤害增加40%", "RelicType": "Attack", "RecordId": "135", "Desc": "Ardipeng_CriticalRateUp1_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_CriticalRateUp(4000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtDmg,Rogue_CriticalRateUp,100)"]}, {"id": "Ardipeng_CriticalRateUp2", "描述": "暴击伤害增加25%", "RelicType": "Attack", "RecordId": "136", "Desc": "Ardipeng_CriticalRateUp2_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 2, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_CriticalRateUp(2500)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtDmg,Rogue_CriticalRateUp,100)"]}, {"id": "Ardipeng_CriticalRateUp3", "描述": "暴击伤害增加16%", "RelicType": "Attack", "RecordId": "137", "Desc": "Ardipeng_CriticalRateUp3_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 3, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_CriticalRateUp(1600)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtDmg,Rogue_CriticalRateUp,100)"]}, {"id": "Ardipeng_CriticalRateUp4", "描述": "暴击伤害增加10%", "RelicType": "Attack", "RecordId": "138", "Desc": "Ardipeng_CriticalRateUp4_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 4, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_CriticalRateUp(1000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(CtDmg,Rogue_CriticalRateUp,100)"]}, {"id": "Ardipeng_ElementalCrit1", "描述": "一级元素伤害可以暴击", "RelicType": "Attack", "RecordId": "194", "Desc": "Ardipeng_ElementalCrit1_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_EnableJuniorElementalCrit"]}, {"id": "Ardipeng_ElementalCrit2", "描述": "二级元素伤害可以暴击", "RelicType": "Attack", "RecordId": "195", "Desc": "Ardipeng_ElementalCrit2_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_EnableSeniorElementalCrit"]}]}