{"ElementalTalent": {"说明": "元素天赋，其实就是元素的效果，每个元素在不同等级都有对应的效果可以解锁的，这个是实际的效果，UI上的在ElementalTalentDesc", "Id": "这个元素天赋的id，和ElementalTalentDesc的id一一对应，对上了就是同一个", "Elemental": "所属的元素类别，Fire、Water、Wind这种的", "TriggerType": "触发类型，可选值：OnHit, OnUse, Passive, Action", "BaseEffect": ["基础的天赋效果的函数，要注意回调点，跟TriggerType有关，《目前尚未敲定此细节！！！》"], "TerrainEffect": [{"Terrain": "地形，目前有: Air, Dirt, Grass, Ice等，还没敲定，但是你air尽量别用，基本就是忽悠", "Effect": "函数，《目前尚未敲定回调接口！！！》值的注意的是，一个地形就支持一个函数《待改进》"}], "WeatherEffect": [{"Weather": "所属的天气，目前就有Sunny, Rainy, Snowy, Volcano", "Effect": "函数，《目前尚未敲定回调接口！！！》值的注意的是，一个天气就支持一个函数《待改进》"}]}, "ElementalTalentDesc": {"说明": "元素天赋的配套说明，UI什么也都要用的", "Id": "这个元素天赋说明的id，和ElementalTalent的id一一对应，对上了就是同一个", "Elemental": "所属的元素类别，Fire、Water、Wind这种的", "TriggerType": "触发类型，可选值：OnHit, OnUse, Passive, Action", "Level": "<int>所在等级", "EP": "<int>学费", "Icon": "图标路径，ArtResource开始", "Name": "总得有个名儿吧", "BaseEffect": "基础效果的描述文字", "TerrainEffect": [{"Terrain": "地形，目前有: Air, Dirt, Grass, Ice等，还没敲定，但是你air尽量别用，基本就是忽悠", "Desc": "描述文字，其实没有的话，这个数组都可以没有，尽量也别太多废话，UI也不好做，效果也麻烦"}], "WeatherEffect": [{"Weather": "所属的天气，目前就有Sunny, Rainy, Snowy, Volcano", "Desc": "描述文字，其实没有的话，这个数组都可以没有，尽量也别太多废话，UI也不好做，效果也麻烦"}]}}