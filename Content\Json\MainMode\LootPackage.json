{"LootPackage": [{"________________________________________掉落__": "__鼠人通用______________________________________________________"}, {"Id": "WereRat", "说明": "", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 2}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 3}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 5}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"________________________________________掉落__": "__普通兽人______________________________________________________"}, {"Id": "OrcNormal", "说明": "", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 4}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 6}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 10}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"________________________________________掉落__": "__精英兽人______________________________________________________"}, {"Id": "OrcElite", "说明": "", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 4}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 6}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 10}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"________________________________________掉落__": "__巨魔______________________________________________________"}, {"Id": "Ogre", "说明": "暂时先去除掉落装备，之后看这个装备放到什么地方给玩家 Warrior02_Helmet", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 20}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"________________________________________掉落__": "__测试______________________________________________________"}, {"Id": "TestDungeon", "说明": "", "EachCheckLoot": [], "MustLoot": [], "MustCount": 0, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"Id": "TestEquipment", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior02_Helmet", "Count": 1}, {"Type": "Equipment", "Id": "Warrior02_Armor", "Count": 1}, {"Type": "Equipment", "Id": "Warrior02_Glove", "Count": 1}, {"Type": "Equipment", "Id": "Warrior02_Boot", "Count": 1}, {"Type": "Equipment", "Id": "Warrior03_Helmet", "Count": 1}, {"Type": "Equipment", "Id": "Warrior03_Armor", "Count": 1}, {"Type": "Equipment", "Id": "Warrior03_<PERSON>love", "Count": 1}, {"Type": "Equipment", "Id": "Warrior03_Boot", "Count": 1}], "Rate": 3000, "MaxTimes": 1, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"Id": "TestWeapon", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Skeleton_Sword", "Count": 1}, {"Type": "WeaponModel", "Id": "Legolas_Sword", "Count": 1}, {"Type": "WeaponModel", "Id": "Elven_GreatSword", "Count": 1}, {"Type": "WeaponModel", "Id": "Legolas_Claymore", "Count": 1}, {"Type": "WeaponModel", "Id": "HolyTree_Shield", "Count": 1}, {"Type": "WeaponModel", "Id": "Skeleton_Buckler", "Count": 1}, {"Type": "WeaponModel", "Id": "Golden<PERSON><PERSON>_Spear", "Count": 1}, {"Type": "WeaponModel", "Id": "DarkSpear", "Count": 1}], "Rate": 3000, "MaxTimes": 1, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 0, "MayNotRate": 200}, {"________________________________________掉落__": "__宝箱______________________________________________________"}, {"Id": "NormalChestMoney", "说明": "普通宝箱", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 5}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 10}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 15}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 1, "MayNotRate": 200}, {"Id": "NormalChestPotion", "说明": "普通宝箱", "EachCheckLoot": [], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 2}], "Rate": 2000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 1}], "Rate": 3000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1, "MayLoot": [], "MayCount": 1, "MayNotRate": 200}, {"Id": "RareChest", "说明": "华丽宝箱", "EachCheckLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 10}], "Rate": 9000, "MaxTimes": 1, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 20}], "Rate": 6000, "MaxTimes": 1, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 30}], "Rate": 3000, "MaxTimes": 1, "BuffCheckers": []}, {"Things": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 3}], "Rate": 7000, "MaxTimes": 1, "BuffCheckers": []}], "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 3}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "MoneyChest", "说明": "很多钱", "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 100}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"________________________________________掉落__": "__装备______________________________________________________"}, {"Id": "Warrior02_Helmet", "说明": "神殿精灵卫士的镶金重盔", "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior02_Helmet", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Warrior02_Armor", "说明": "神殿精灵卫士的镶金胸铠", "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior02_Armor", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Warrior02_Glove", "说明": "流浪骑士的臂铠", "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior02_Glove", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Warrior02_Boot", "说明": "流浪骑士的腿铠", "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior02_Boot", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Warrior03_Armor", "说明": "冰魄收割者的爪痕", "MustLoot": [{"Things": [{"Type": "Equipment", "Id": "Warrior03_Armor", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"________________________________________掉落__": "__武器______________________________________________________"}, {"Id": "Elven_GreatSword", "说明": "精灵大剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Elven_GreatSword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Legolas_Claymore", "说明": "莱格里斯骑士大剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Legolas_Claymore", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Iron_Sword", "说明": "铁制直剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Iron_Sword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Legolas_Sword", "说明": "莱格里斯骑士剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Legolas_Sword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "HolyTree_Shield", "说明": "圣树铁盾", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "HolyTree_Shield", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Skeleton_Buckler", "说明": "骷髅圆盾", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Skeleton_Buckler", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Iron_Spear", "说明": "铁质长枪", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Iron_Spear", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Golden<PERSON><PERSON>_Spear", "说明": "黄金十字枪", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Golden<PERSON><PERSON>_Spear", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "DarkSpear", "说明": "黑尖枪", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "DarkSpear", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "<PERSON><PERSON>_<PERSON>", "说明": "精灵长枪", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "<PERSON><PERSON>_<PERSON>", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"________________________________________掉落__": "__武器__未使用__________________________________________________"}, {"Id": "OldFashionedNoble_GreatSword", "说明": "旧式贵族大剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "OldFashionedNoble_GreatSword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "DarkKnight_GreatSword", "说明": "黑骑士大剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "DarkKnight_GreatSword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "KingsKnight_GreatSword", "说明": "王之骑士大剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "KingsKnight_GreatSword", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "<PERSON><PERSON><PERSON>", "说明": "蜂刃", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "<PERSON><PERSON><PERSON>", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Ceremony_Saber", "说明": "礼仪用剑", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Ceremony_Saber", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "Harpoon", "说明": "鱼叉枪", "MustLoot": [{"Things": [{"Type": "WeaponModel", "Id": "Harpoon", "Count": 1}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"________________________________________掉落__": "__物品______________________________________________________"}, {"Id": "ThreeFireBallScroll", "说明": "3个火球卷轴", "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 3}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"Id": "FivePotions", "说明": "5瓶药水", "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 5}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}, {"________________________________________掉落__": "__任务奖励______________________________________________________"}, {"Id": "MainQuest2eReward", "说明": "主线任务2e的奖励", "MustLoot": [{"Things": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 50}], "Rate": 5000, "MaxTimes": 1, "IsInfinity": false, "BuffCheckers": []}], "MustCount": 1}]}