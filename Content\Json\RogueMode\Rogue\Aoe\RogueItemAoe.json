{"AOE": [{"Id": "Rogue_AoE_BeamExplosion", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/LightBeam_AoE_Explosion", "OnTick": [], "OnCharacterEnter": []}, {"Id": "Rogue_Aoe_CannonExplosion", "Tag": ["Explsion"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_CannonExplosion", "OnTick": [], "OnCharacterEnter": []}, {"Id": "Rogue_Aoe_EarthQuakeWave", "Tag": ["EarthQuake"], "BpPath": "Core/Item/AOE/Rogue/Item/AOE_EarthQuake"}, {"Id": "Rogue_Aoe_LightShield", "Tag": ["Light"], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_LightShield"}, {"Id": "Rogue_<PERSON><PERSON>_LightningHammerController", "Tag": ["Lightning"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_LightningHammerController"}, {"Id": "<PERSON>_<PERSON><PERSON>_LightningHammer", "Tag": ["Lightning"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_LightningHammer"}, {"Id": "Rogue_Aoe_WindProtect", "Tag": ["Wind"], "TickTime": 0.65, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_WindProtect"}, {"Id": "<PERSON>_<PERSON><PERSON>_ShadowKnifeLauncher", "Tag": ["Dark"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_ShadowKnife"}, {"Id": "Rogue_<PERSON><PERSON>_FrozenDash", "Tag": ["Ice"], "TickTime": 0.2, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_FrozenDash", "OnTick": []}, {"Id": "Rogue_Aoe_FireDash", "Tag": ["Fire"], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_FireDash"}, {"Id": "Rogue_<PERSON>oe_FireDash_2", "Tag": [], "TickTime": 0.1, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_FireDash_2"}, {"Id": "Rogue_Aoe_ScytheExplosion", "Tag": ["Slash"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_ScytheExplosion", "OnTick": []}, {"Id": "Rogue_Aoe_CircleSlashBlow", "Tag": ["Slash"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_CricleSlashBlow"}, {"Id": "Rogue_Aoe_CircleSlashCenter", "Tag": ["Slash"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_CricleSlashCenter"}, {"Id": "Rogue_<PERSON><PERSON>_DarkHole", "Tag": [], "TickTime": 1, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_DarkHole"}, {"Id": "Rogue_Aoe_WindHole", "Tag": [], "TickTime": 1, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_WindHole"}, {"Id": "Rogue_Aoe_WindAroundKnife", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_WindAroundKnife"}, {"Id": "<PERSON>_<PERSON>oe_RangeThunder", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_RangeThunder", "OnTick": []}, {"Id": "Rogue_<PERSON><PERSON>_RangeThunder_2", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_RangeThunder_2", "OnTick": []}, {"Id": "Rogue_Aoe_ReflectThunder", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_ReflectThunder", "OnTick": []}, {"Id": "<PERSON>_<PERSON><PERSON>_FrozenParry", "Tag": ["Slash"], "BpPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_FrozenParry"}]}