{"ActionSelection说明": {"Id": "这个替换技能组的id", "ClassId": "属于哪个职业的，这还没具体定义值", "CmdAction": "是属于那个Cmd，和Action的cmd是呼应的", "ActionId": ["哪些技能id对应的技能在这一组里可供替换"]}, "ActionLink说明": {"Id": "是为了和UI挂钩的", "MainActionId": "启动用的Action的Id，比如战士格挡的id", "SetTag": "<bool> 改变方式是否是改变CancelTag （缺省值true就是改变CancelTag），当一个动作是自动连接后续的时候这里应该是false，这里有一个缺陷：就是一个Action不能同时成为一个改变tag和改变id的后续动作", "SetToActionId": "如果SetTag==false，那么就得把对应的Action设置到一个id（这是一个有缺陷的临时做法），就是设置到这个值了，原本id为这个值的动作将会启用现在被设置进去的动作的原本id（id交换）", "LinkTag": {"Tag": "用来链接后续动作的那个Be Cancelled Tag，原来的后续动作的CancelTag中（Tag值相等的）将被删除掉这个值，新的后续动作的CancelTag会被加入这个值", "FromSec": "<float> Cancel后下一个动作的起始时间（秒），之后会被改成SectionName"}, "LinkActionId": ["所有可选后续动作列表，毕竟原本这些动作中是没有这个CancelTag的，得记录下来"], "LinkEffectKeys": ["在AwCharacter中开启的一些Key，当这些Key激活时，Montage里面会有特殊处理（比如激活某些元素的爆发点）", "当然光这个地方有这个Key是不行的，要选中的动作也有这个Key才行，并且动作中有对应的Notify，才能发动"]}, "ActionSelections": [{"line": "------------------------------ Warrior ------------------------------"}, {"Id": "Warrior_Action2", "ClassId": "Warrior", "CmdAction": "Action2", "ActionId": ["Warrior_ChargeAttack", "Warrior_ChargeJumpAttack"]}, {"Id": "Warrior_Action2_Air", "ClassId": "Warrior", "CmdAction": "Action2", "ActionId": ["Warrior_AirChargeAttack"]}, {"Id": "Warrior_Action3", "ClassId": "Warrior", "CmdAction": "Action3", "ActionId": ["<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Warrior_DashSlashAttack1"]}, {"Id": "Warrior_Action3_Air", "ClassId": "Warrior", "CmdAction": "Action3", "ActionId": ["Warrior_AirDoubleStrike0"]}, {"Id": "Warrior_Action4", "ClassId": "Warrior", "CmdAction": "Action4", "ActionId": ["<PERSON><PERSON><PERSON>"]}, {"Id": "Warrior_Action4_Air", "ClassId": "Warrior", "CmdAction": "Action4", "ActionId": ["Warrior_AirRush_DemonSlayer", "Warrior_Air_FallSlash"]}, {"line": "------------------------------ BladeDancer ------------------------------"}, {"Id": "BladeDancer_Action2", "ClassId": "BladeDancer", "CmdAction": "Action2", "ActionId": ["BladeDancer_RiseComboSlash", "BladeDancer_RiseSlash"]}, {"Id": "BladeDancer_Action2_Air", "ClassId": "BladeDancer", "CmdAction": "Action2", "ActionId": ["BladeDancer_AirTwiceComboAttack"]}, {"Id": "BladeDancer_Action3", "ClassId": "BladeDancer", "CmdAction": "Action3", "ActionId": ["BladeDancer_DashAttack"]}, {"Id": "BladeDancer_Action3_Air", "ClassId": "BladeDancer", "CmdAction": "Action3", "ActionId": ["BladeDancer_AirDashAttack", "BladeDancer_AirDashComboAttack"]}, {"Id": "BladeDancer_Action4", "ClassId": "BladeDancer", "CmdAction": "Action4", "ActionId": ["BladeDancer_SwrodDanceComboAttack"]}, {"Id": "BladeDancer_Action4_Air", "ClassId": "BladeDancer", "CmdAction": "Action4", "ActionId": ["BladeDancer_AirSwrodDanceComboAttack_Fall", "BladeDancer_AirSwrodDanceComboAttack_Dash"]}, {"line": "------------------------------ Spearman ------------------------------"}, {"Id": "Spearman_Action2", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action2", "ActionId": ["Spearman_SweapAttack1"]}, {"Id": "<PERSON><PERSON>man_Action2_Air", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action2", "ActionId": ["Spearman_AirDownSpikeAttack"]}, {"Id": "Spearman_Action3", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action3", "ActionId": ["S<PERSON><PERSON>_DashSpike"]}, {"Id": "<PERSON><PERSON>man_Action3_Air", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action3", "ActionId": ["<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown"]}, {"Id": "Spearman_Action4", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action4", "ActionId": ["S<PERSON><PERSON>_DashSweapSlash", "S<PERSON><PERSON>_BackJumpSweapSlash"]}, {"Id": "<PERSON><PERSON>man_Action4_Air", "ClassId": "<PERSON><PERSON><PERSON>", "CmdAction": "Action4", "ActionId": ["Spearman_AirDashSweapSlash"]}, {"line": "------------------------------ Swordsman ------------------------------"}, {"Id": "Swordsman_Action2", "ClassId": "Swordsman", "CmdAction": "Action2", "ActionId": ["Swordsman_RiseSlash", "Swordsman_RiseComboSlash"]}, {"Id": "Swordsman_Action2_Air", "ClassId": "Swordsman", "CmdAction": "Action2", "ActionId": ["Swordsman_AirDownSlashAttack1"]}, {"Id": "Swordsman_Action3", "ClassId": "Swordsman", "CmdAction": "Action3", "ActionId": ["Swordsman_DashSlash"]}, {"Id": "Swordsman_Action3_Air", "ClassId": "Swordsman", "CmdAction": "Action3", "ActionId": ["Swordsman_AirDashSting"]}, {"Id": "Swordsman_Action4", "ClassId": "Swordsman", "CmdAction": "Action4", "ActionId": ["Swordsman_Defense"]}, {"Id": "Swordsman_Action4_Air", "ClassId": "Swordsman", "CmdAction": "Action4", "ActionId": ["Swordsman_AirDownShieldSmash"]}], "ActionLink": [{"line": "------------------------------ Warrior ------------------------------"}, {"Id": "Warrior_Action2_<PERSON>_StingDashAttack", "MainActionId": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "SetTag": false, "SetToActionId": "Warrior_StingUpperSlash", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Warrior_StingUpperSlash", "Warrior_StingComboSlash"], "LinkEffectKeys": []}, {"Id": "Warrior_DashSlashAttack1", "MainActionId": "Warrior_DashSlashAttack1", "SetTag": false, "SetToActionId": "Warrior_DashSlashAttack2", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Warrior_DashSlashAttack2"], "LinkEffectKeys": []}, {"Id": "Warrior_AirDoubleStrike0", "MainActionId": "Warrior_AirDoubleStrike0", "SetTag": false, "SetToActionId": "Warrior_AirDoubleStrike1", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Warrior_AirDoubleStrike1"], "LinkEffectKeys": []}, {"Id": "<PERSON><PERSON><PERSON>", "MainActionId": "<PERSON><PERSON><PERSON>", "SetTag": false, "SetToActionId": "Warrior_CounterDashAttack", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Warrior_CounterDashAttack", "Warrior_CounterDashAttack_JustBlock"], "LinkEffectKeys": []}, {"line": "------------------------------ BladeDancer ------------------------------"}, {"Id": "BladeDancer_Action2_BladeDancer_RiseComboSlash_JustAttack", "MainActionId": "BladeDancer_RiseComboSlash", "SetTag": false, "SetToActionId": "BladeDancer_RiseComboSlash_AJ", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["BladeDancer_RiseComboSlash_AJ"], "LinkEffectKeys": []}, {"Id": "BladeDancer_Action2_BladeDancer_RiseSlash_JustAttack", "MainActionId": "BladeDancer_RiseSlash", "SetTag": false, "SetToActionId": "BladeDancer_RiseSlash_AJ", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["BladeDancer_RiseSlash_AJ"], "LinkEffectKeys": []}, {"Id": "BladeDancer_Action3_BladeDancer_DashAttack_JustAttack", "MainActionId": "BladeDancer_DashAttack", "SetTag": true, "SetToActionId": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "LinkTag": {"Tag": "TS_AttackAJ3", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "BladeDancer_DashAttackLeft_AJ", "BladeDancer_DashAttackRight_AJ"], "LinkEffectKeys": []}, {"Id": "BladeDancer_Action4_BladeDancer_SwrodDanceComboAttack_JustAttack", "MainActionId": "BladeDancer_SwrodDanceComboAttack", "SetTag": false, "SetToActionId": "BladeDancer_SwrodDanceDashComboAttack_AJ", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["BladeDancer_SwrodDanceDashComboAttack_AJ"], "LinkEffectKeys": []}, {"line": "------------------------------ Spearman ------------------------------"}, {"Id": "Spearman_Action2_Spearman_SweapAttack1", "MainActionId": "Spearman_SweapAttack1", "SetTag": false, "SetToActionId": "Spearman_SweapAttack2", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Spearman_SweapAttack2"], "LinkEffectKeys": []}, {"Id": "<PERSON><PERSON><PERSON>_Action3_<PERSON>pearman_DashSpike", "MainActionId": "S<PERSON><PERSON>_DashSpike", "SetTag": false, "SetToActionId": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON>_DashSpike_Combo", "<PERSON><PERSON><PERSON>_RiseSlash_Combo"], "LinkEffectKeys": []}, {"Id": "S<PERSON><PERSON>_Action4_Spearman_DashSweapSlash", "MainActionId": "S<PERSON><PERSON>_DashSweapSlash", "SetTag": false, "SetToActionId": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON>_DashSpike_HitJump", "<PERSON><PERSON><PERSON>_DashSweap_HitJump2"], "LinkEffectKeys": []}, {"Id": "S<PERSON><PERSON>_Action4_Spearman_BackJumpSweapSlash", "MainActionId": "S<PERSON><PERSON>_BackJumpSweapSlash", "SetTag": false, "SetToActionId": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "<PERSON><PERSON><PERSON>_DashSweap_HitJump2"], "LinkEffectKeys": []}, {"Id": "Spearman_Action3_Air_Spearman_AirDashSpike_ForwardDown", "MainActionId": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown", "SetTag": false, "SetToActionId": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON>_DashSpike_HitJump"], "LinkEffectKeys": []}, {"Id": "Spearman_Action4_Air_Spearman_AirDashSweapSlash", "MainActionId": "Spearman_AirDashSweapSlash", "SetTag": false, "SetToActionId": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "<PERSON><PERSON><PERSON>_DashSweap_HitJump2"], "LinkEffectKeys": []}, {"line": "------------------------------ Swordsman ------------------------------"}, {"Id": "Swordsman_Action2_Swordsman_RiseSlash", "MainActionId": "Swordsman_RiseSlash", "SetTag": false, "SetToActionId": "_", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["_"], "LinkEffectKeys": []}, {"Id": "Swordsman_Action3_Swordsman_DashSlash", "MainActionId": "Swordsman_DashSlash", "SetTag": false, "SetToActionId": "Swordsman_DashSlash2", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["Swordsman_DashSlash2"], "LinkEffectKeys": []}, {"Id": "Swordsman_Action4_Swordsman_Defense", "MainActionId": "Swordsman_Defense", "SetTag": false, "SetToActionId": "???", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["???"], "LinkEffectKeys": []}, {"Id": "Swordsman_Action3_Air_Swordsman_AirDashSting", "MainActionId": "Swordsman_AirDashSting", "SetTag": false, "SetToActionId": "_", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["_"], "LinkEffectKeys": []}, {"Id": "Swordsman_Action4_Air_Swordsman_AirDownShieldSmash", "MainActionId": "Swordsman_AirDownShieldSmash", "SetTag": false, "SetToActionId": "_", "LinkTag": {"Tag": "", "FromSec": 0}, "LinkActionId": ["_"], "LinkEffectKeys": []}]}