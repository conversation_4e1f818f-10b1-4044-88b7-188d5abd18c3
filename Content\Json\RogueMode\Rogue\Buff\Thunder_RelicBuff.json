{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个带电", "Id": "An<PERSON>_ThunderOfPoltick_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Thunder,1,8,true,false)"]}, {"说明": "动画中引发电涌", "Id": "Anim_ThunderHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(1,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10)"]}, {"说明": "动画中引发无消耗电涌", "Id": "Anim_ThunderHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(1,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10,<PERSON>T<PERSON><PERSON>,false)"]}, {"说明": "动画中概率引发电涌", "Id": "Anim_ChanceThunderHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(0.5,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10)"]}, {"说明": "动画中引发闪电", "Id": "<PERSON><PERSON>_JustDodgeThunder", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Poltick11,0.7,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeThunder,1,0,true)"]}, {"说明": "动画中生成持续到动画结束的闪电Aoe，AOE蓝图tick检测消失", "Id": "Anim_<PERSON>ick<PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick8,256,root,NoTween,true)"]}, {"分割": "-------------------------------------------Thunder-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个带电", "Id": "ThunderOfPoltick_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(An<PERSON>_ThunderOfPoltick_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(An<PERSON>_ThunderOfPoltick_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率引爆带电产生一次电涌", "Id": "ThunderOfPoltick_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceThunderHit,1,0,true)"]}, {"说明": "下砸时候,向前方产生一道电流Aoe", "Id": "Smash_ThunderAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick3,2,Root)"]}, {"说明": "下砸时候产生一个落雷", "Id": "Smash_ThunderAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick4,0.5,Root)"]}, {"说明": "位移技能时造成伤害则触发电击转换", "Id": "Dash_ThunderCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHit,1,0,true)"]}, {"说明": "蓄力时,周围周期性产生一圈电", "Id": "Power_TickCreateThunderAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>Tick<PERSON><PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(An<PERSON>_ThunderTick<PERSON>oe,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次电涌", "Id": "Power_ThunderHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个闪电Aoe", "Id": "ThunderDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Poltick10,0.5,None)"]}, {"说明": "动画中完美闪避生成闪电Aoe", "Id": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Poltick11,0.7,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeThunder,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeThunder,1,0,true)"]}, {"说明": "击飞技能时对击中的目标产生一次电涌", "Id": "Rise_ThunderCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHit,1,0,true)"]}, {"说明": "造成雷元素伤害的时候概率对雷元素的主动道具进行额外充能", "Id": "AddItemRecover_Thunder", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Thunder)"]}, {"说明": "每过TickTime在目标生成一个Aoe", "Id": "Launch_ThunderTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Poltick17,0,0.3,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON><PERSON>_<PERSON>oe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Poltick18,0.5,Root)"]}, {"说明": "敌人在breakDown的时候伤害提升1%", "Id": "Rogue_DamageUpWhenTargetBreakDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.BreakDownEnemyDamageChanceUp(0.0001)"]}]}