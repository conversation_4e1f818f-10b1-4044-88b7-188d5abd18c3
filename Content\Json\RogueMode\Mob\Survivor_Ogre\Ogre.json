{"Mob": [{"Id": "Survivor_Ogre", "MobRank": "Boss", "Tag": ["Ogre"], "BpPath": "Core/Characters/Rogue_Mob/Ogre/Ogre_Rogue", "XAI": ["OgreTestAction"], "AI": ["StopAIBuff", "OgreProvoke", "OgreRage", "OgreWeak", "RogueOgreBasicBattle"], "AIOrder": [], "DungeonCampImpact": [], "OnBeKilled": [], "LootPackageId": "Ogre", "ExpGiven": 500, "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "Rogue_Ogre", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 500, "SightHalfAngleDregee": 120, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 130, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 17, "PAtk": 9, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "AddRageWhenBeHurt", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "OgreAlwaysRage", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 7000, "Time": 0, "Infinity": true}, {"Id": "ImmuneLava", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Type": "Meat", "OnPartBroken": []}, {"说明": "这是石柱子的部位", "Id": "<PERSON><PERSON>", "Meat": {"Physical": 1}, "Breakable": {"Physical": 1.0}, "Part": "Weapon", "Durability": [75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75], "CanBeDestroy": true, "Type": "Rock", "HideSightPart": ["RockPillar"], "OnPartBroken": ["OgreAIScript.PillarBroken()"]}], "NotInitChaParts": ["<PERSON><PERSON>"], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Ogre/Move"]}, "InitAction": true}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Ogre/Fall_Loop"]}, "Priority": 1}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/Dead"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/HurtFromFront", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/HurtFromBehind", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/HurtFromFront", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/HurtFromBehind"]}, "InitAction": true}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "FightingWill0时跳跃落地会摔倒，用这个", "Id": "SlipOnGround", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Ogre/Hurt/SlipOnGround"]}, "InitAction": true}, {"说明": "FightingWill0时前冲类会摔倒，用这个", "Id": "FallOnGround", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/FallOnGround"]}, "InitAction": true}, {"说明": "被自己召唤的石头砸中", "Id": "StunByPillar", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/FallOnGround"]}, "InitAction": true}, {"notes": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Roar"]}, "InitAction": true}, {"notes": "切换到虚弱状态", "Id": "ChangeToWeak", "Cmds": ["ChangeToWeak"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/StateChange_Weak"]}, "InitAction": true}, {"notes": "Attack_S1", "Id": "Attack_S1", "Cmds": ["Attack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S1"]}, "InitAction": true}, {"notes": "Attack_S2", "Id": "Attack_S2", "Cmds": ["Attack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S2"]}, "InitAction": true}, {"notes": "Attack_M1", "Id": "Attack_M1", "Cmds": ["Attack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_M1"]}, "InitAction": true}, {"notes": "Attack_L1", "Id": "Attack_L1", "Cmds": ["Attack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L1"]}, "InitAction": true}, {"notes": "Attack_L1_Down", "Id": "Attack_L1_Down", "Cmds": ["Attack_L1_Down"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L1_Down"]}, "InitAction": true}, {"notes": "跳", "Id": "Attack_L2", "Cmds": ["Attack_L2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L2", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_L2"]}, "InitAction": true}, {"notes": "跳", "Id": "Attack_L2_Down", "Cmds": ["Attack_L2_Down"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L2_Down"]}, "InitAction": true}, {"notes": "Attack_S5", "Id": "Attack_S5", "Cmds": ["Attack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S5_TurnRAtk"]}, "InitAction": true}, {"notes": "Attack_S6", "Id": "Attack_S6", "Cmds": ["Attack_S6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S6"]}, "InitAction": true}, {"notes": "Attack_S7", "Id": "Attack_S7", "Cmds": ["Attack_S7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S7", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S7"]}, "InitAction": true}, {"说明": "丢柱子 throw pillar", "notes": "Attack_XL1", "Id": "Attack_XL1", "Cmds": ["Attack_XL1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Normal_Attack_XL1", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Rage_Attack_XL1"]}, "InitAction": true}, {"notes": "Attack_S3_TurnRAtk", "Id": "Attack_S3_TurnRAtk", "Cmds": ["Attack_S3_TurnRAtk"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S3_TurnRAtk", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S3_TurnRAtk"]}, "InitAction": true}, {"notes": "Attack_S4_TurnLAtk", "Id": "Attack_S4_TurnLAtk", "Cmds": ["Attack_S4_TurnLAtk"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S4_TurnLAtk", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S4_TurnLAtk"]}, "InitAction": true}, {"notes": "TurnL", "Id": "TurnL", "Cmds": ["TurnL"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_TurnL"]}, "InitAction": true}, {"notes": "TurnR", "Id": "TurnR", "Cmds": ["TurnR"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_TurnR"]}, "InitAction": true}, {"notes": "Pillar_TurnL", "Id": "Pillar_TurnL", "Cmds": ["Pillar_TurnL"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_TurnL"]}, "InitAction": true}, {"notes": "Pillar_TurnR", "Id": "Pillar_TurnR", "Cmds": ["Pillar_TurnR"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_TurnR"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作", "Id": "CallPillar", "Cmds": ["CallPillar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["CallPillar1"], "1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作第二次", "Id": "CallPillar1", "Cmds": ["CallPillar1"], "Tags": [{"Tag": "CallPillar1", "From": 0.9}], "BeCancelledTags": {"0": ["CallPillar2"], "1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar1"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作第三次", "Id": "CallPillar2", "Cmds": ["CallPillar2"], "Tags": [{"Tag": "CallPillar2", "From": 0.9}], "BeCancelledTags": {"1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar2"]}, "InitAction": true}, {"notes": "拔起柱子动作", "Id": "PickUpPillar", "Cmds": ["PickUpPillar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_PickUpPillar"]}, "InitAction": true}, {"notes": "发呆动作01", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare01", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare01"]}, "InitAction": true}, {"notes": "发呆动作02", "Id": "Stare02", "Cmds": ["Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare02", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare02"]}, "InitAction": true}, {"notes": "发呆动作03", "Id": "Stare03", "Cmds": ["Stare03"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare03", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare03"]}, "InitAction": true}, {"notes": "发呆动作04", "Id": "Stare04", "Cmds": ["Stare04"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare04", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare04"]}, "InitAction": true}, {"notes": "发呆动作05", "Id": "Stare05", "Cmds": ["Stare05"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare05", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare05"]}, "InitAction": true}, {"notes": "发呆动作06", "Id": "Stare06", "Cmds": ["Stare06"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare06", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare06"]}, "InitAction": true}, {"notes": "挑衅动作01", "Id": "Taunt01", "Cmds": ["Taunt01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_Taunt01", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_Taunt01"]}, "InitAction": true}, {"notes": "RageState", "Id": "RageState", "Cmds": ["RageState"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Unarmed_RageState", "ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Attack/Pillar_RageState"]}, "InitAction": true}, {"notes": "被石柱砸到的特殊受击", "Id": "HurtFromRock", "Cmds": ["HurtFromRock"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 51, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/HurtFromRock"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Ogre/Hurt/BreakHurt"]}, "InitAction": true}]}]}