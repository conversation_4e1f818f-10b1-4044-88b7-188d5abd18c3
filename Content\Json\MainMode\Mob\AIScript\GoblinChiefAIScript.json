{"AIScript": [{"说明": "进入战斗的吼叫", "Id": "GoblinChief_Roar", "Condition": ["MobAIScript.CheckNotHasBuff(Goblin_HasRoared)", "MobAIScript.CheckStimulateByView()"], "OnReady": [], "Action": ["GoblinAIScript.GoblinRoarWhenStartBattle(300,<PERSON><PERSON>,<PERSON>_<PERSON>)"]}, {"说明": "近距离攻击", "Id": "GoblinChief_NearAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,-60,60)"], "OnReady": [], "Action": ["GoblinAIScript.AttackNearViewedEnemy(0,400,-60,60,AttackS1,AttackS2)"]}, {"说明": "中距离攻击", "Id": "GoblinChief_MiddleAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,700,-60,60)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,700,-60,60,AttackM1)"]}, {"说明": "远距离攻击", "Id": "GoblinChief_FarAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,1000,-60,60)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,1000,-60,60,AttackL1)"]}, {"说明": "向右/左踱步", "Id": "GoblinChief_Pace", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(300,800)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveAroundViewedClosetEnemy(300,800,RightPace,LeftPace)"]}]}