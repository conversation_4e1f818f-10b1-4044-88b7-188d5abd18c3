{"Mob": [{"Id": "Rogue_Death_Lord_EX_Stand", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle"], "BpPath": "Core/Characters/Rogue_Mob/Death_Lord/Death_Lord_Stand", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Death_Lord", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 18, "PAtk": 6, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 7, "Infinity": false}, {"Id": "Rogue_Boss_FirstStage", "Stack": 67, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 10000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 10000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Bounced", "UnArmed": "Bounced"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Standard_Move", "UnArmed": "Standard_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Death_Lord/Move"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"Id": "Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Bounced"]}}, {"说明": "下劈", "Id": "StandAttack_S1", "Cmds": ["StandAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/StandAttack_S1"]}, "InitAction": true}, {"说明": "召唤冰塔", "Id": "StandAttack_M1", "Cmds": ["StandAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/StandAttack_M1"]}, "InitAction": true}, {"说明": "召唤冰塔(多)", "Id": "StandAttack_M1_2", "Cmds": ["StandAttack_M1_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/StandAttack_M1_2"]}, "InitAction": true}, {"说明": "王之财宝", "Id": "StandAttack_M2", "Cmds": ["StandAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/StandAttack_M2"]}, "InitAction": true}, {"说明": "原地发呆", "Id": "Stare", "Cmds": ["Stare"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/Stare02"]}, "InitAction": true}]}], "Buff": [{"Id": "Rogue_Death_Lord_IceTowerNum", "Tag": ["Death_Lord"], "Priority": 0, "MaxStack": 5}], "Bullet": [{"Id": "Death_Lord_IceSpear", "Tag": [], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceSpear", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 10, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(0.3,0,<PERSON>Damage,Ice)", "BulletScript.AddBuffOnHit(Standard_PlayFrozen,1,1)"], "OnRemoved": []}], "AOE": [{"Id": "Death_Lord_Stand_S1_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_StandAttack_S1_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Lord_Stand_S1_WarningAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_StandAttack_S1_WarningAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}], "SceneItem": [{"Id": "Death_Lord_<PERSON>T<PERSON>er", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceTower", "LifeSpan": 0, "Part": [], "Tween": ""}, {"Id": "Death_Lord_AutoIceSpear", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_Stand_AutoIceSpear", "LifeSpan": 0, "Part": [], "Tween": ""}]}