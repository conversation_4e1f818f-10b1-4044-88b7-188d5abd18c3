{"AIScript": [{"说明": "进入战斗的吼叫", "Id": "WereRat_Roar", "Condition": ["WereRatAIScript.CheckRatCanRoar()", "MobAIScript.CheckStimulateByView()"], "OnReady": [], "Action": ["WereRatAIScript.WereRatRoarWhenStartBattle(300,<PERSON>_<PERSON>,Roar)"]}, {"说明": "脱战时清除已吼叫BUFF", "Id": "WereRat_ClearRoaredBuff", "Condition": ["WereRatAIScript.CheckJustLeaveBattle()"], "OnReady": [], "Action": ["WereRatAIScript.ClearWereRatRoaredBuff()"]}, {"说明": "向奶酪移动的动作", "Id": "MoveToClosetCheese", "Condition": ["WereRatAIScript.CheckViewedCheeseAOE()", "MobAIScript.CheckFightingWillLevelLess(2)"], "OnReady": [], "Action": ["WereRatAIScript.AIMoveToNearestCheese()"]}, {"说明": "做登场动作", "Id": "WereRat_Debut", "Condition": ["MobAIScript.CheckHasBuff(WillDoDebut)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(Debut)", "MobAIScript.RemoveBuffObj(WillDoDebut)"]}, {"说明": "正常捡起奶酪的动作", "Id": "NormalPickUpCheese", "Condition": ["WereRatAIScript.CheckCanGetCheese()", "MobAIScript.CheckFightingWillLevelEquals(1)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(PickUpCheese)"]}, {"说明": "做奶酪被打掉的生气动作", "Id": "WereRatAngry", "Condition": ["WereRatAIScript.CheckAngry()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(WereRatAngry)"]}, {"说明": "鼠人转向刺激源（受击、同伴信号、声音）", "Id": "WereRatTurnToStimulate", "Condition": ["WereRatAIScript.CheckHasStimulate()", "MobAIScript.CheckHasEnemyInRange(1500)"], "OnReady": [], "Action": ["MobAIScript.AITurnToStimulateDoAction(TurnToStimulate)"]}, {"说明": "发呆：喘气", "Id": "WereRat_DeepBreathe", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(1)", "MobAIScript.CheckFightingWillValueLess(2001)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(WereRat_DeepBreathe)"]}, {"说明": "发呆：害怕", "Id": "WereRat_Fear", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(0)", "MobAIScript.CheckHasBuff(WereRat_AccumulateDamage,300)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(WereRat_Fear)"]}, {"说明": "发呆：累了", "Id": "WereRat_Tired", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(WereRat_Tired)"]}, {"说明": "向左突击，攻击的同时转向", "Id": "WereRat_LeftSpinAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,-90,-60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,400,-90,-60,Left<PERSON><PERSON><PERSON><PERSON><PERSON>)"]}, {"说明": "向右突击，攻击的同时转向", "Id": "WereRat_RightSpinAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,60,90)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,400,60,90,RightSpinAttack)"]}, {"说明": "后跳", "Id": "WereRat_DodgeBack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,150,-30,30)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Dodge_Back)"]}, {"说明": "侧滚躲避（左或者右）", "Id": "WereRat_DodgeLeftOrRight", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,150,-90,90)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(Dodge_Left,Dodge_Right)"]}, {"说明": "向右/左踱步", "Id": "WereRat_Pace", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(300,800)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveAroundViewedClosetEnemy(300,800,RightPace,LeftPace)"]}]}