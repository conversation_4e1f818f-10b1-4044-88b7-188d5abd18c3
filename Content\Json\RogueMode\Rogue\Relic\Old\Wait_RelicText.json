{"GameTexts1": [{"——————————————IgniRelic": "—————————————————————————————————————————————————伊格尼圣遗物————————————————————————————————————————————————————————————"}, {"——————————————IgniRelic": "—————————————————————————————————————————————————伊格尼圣遗物————————————————————————————————————————————————————————————"}, {"key": "BurnOfIgni_8", "Chinese": "被烈火吞噬的画卷", "English": "<PERSON><PERSON> Engulfed In Flames"}, {"key": "BurnOfIgni_9", "Chinese": "熔断的仪式剑", "English": "Fused Ceremonial Sword"}, {"key": "BurnOfIgni_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会</><Igni_BoldTypeface_16>燃烧</><Default_BoldTypeface_16>攻击自己的敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will </><Igni_BoldTypeface_14>Burning</><Default_BoldTypeface_16> the enemy who attacks yourself.</>"}, {"key": "BurnOfIgni_9_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Igni_BoldTypeface_16>爆燃</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Igni_BoldTypeface_14>Detonation</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————IlmRelic": "—————————————————————————————————————————————————伊尔姆圣遗物————————————————————————————————————————————————————————————"}, {"——————————————IlmRelic": "—————————————————————————————————————————————————伊尔姆圣遗物————————————————————————————————————————————————————————————"}, {"key": "ColdOfIlm_8", "Chinese": "降魔的手镜", "English": "Hand Mirror of Exorcism"}, {"key": "ColdOfIlm_9", "Chinese": "破碎的冰枪", "English": "Broken Ice Spear"}, {"key": "ColdOfIlm_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会</><Ilm_BoldTypeface_16>寒霜</><Default_BoldTypeface_16>攻击自己的敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will </><Ilm_BoldTypeface_16>Frost</><Default_BoldTypeface_16> the enemy who attacks yourself.</>"}, {"key": "ColdOfIlm_9_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Ilm_BoldTypeface_16>冻结</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Ilm_BoldTypeface_16>Freeze</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————PoltickRelic": "—————————————————————————————————————————————————波尔提克圣遗物——————————————————————————————————————————————————————————"}, {"——————————————PoltickRelic": "—————————————————————————————————————————————————波尔提克圣遗物——————————————————————————————————————————————————————————"}, {"key": "ThunderOfPoltick_8", "Chinese": "风暴之翼箭矢", "English": "Stormwing Arrow"}, {"key": "ThunderOfPoltick_9", "Chinese": "毁灭的风暴盾", "English": "Ruined Storm Shield"}, {"key": "ThunderOfPoltick_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会使周围产生一圈</><Poltick_BoldTypeface_16>电流区域</><Default_BoldTypeface_16>，</><Poltick_BoldTypeface_16>电击</><Default_BoldTypeface_16>进入范围的敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will create an </><Poltick_BoldTypeface_16>Electrical Area</><Default_BoldTypeface_16> around yourself.</><Poltick_BoldTypeface_16>Electrocuted</><Default_BoldTypeface_16> the enemy who entering this area.</>"}, {"key": "ThunderOfPoltick_9_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Poltick_BoldTypeface_16>电涌</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Poltick_BoldTypeface_16>Surge</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————ZantiaRelic": "—————————————————————————————————————————————————赞提娅圣遗物————————————————————————————————————————————————————————————"}, {"——————————————ZantiaRelic": "—————————————————————————————————————————————————赞提娅圣遗物————————————————————————————————————————————————————————————"}, {"key": "WindOfZantia_8", "Chinese": "抵御之风", "English": "Wind of Resistance"}, {"key": "WindOfZantia_9", "Chinese": "流风的宝珠", "English": "Orb of Flowing Wind"}, {"key": "WindOfZantia_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会产生一个</><Zantia_BoldTypeface_16>风盾</><Default_BoldTypeface_16>，反弹自身受到的伤害，并</><Zantia_BoldTypeface_16>风蚀</><Default_BoldTypeface_16>敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will create an </><Zantia_BoldTypeface_16>Wind Shield</><Default_BoldTypeface_16>.Reflects the damage taken and </><Zantia_BoldTypeface_16>Wind Erosion</><Default_BoldTypeface_16> the enemy.</>"}, {"key": "WindOfZantia_9_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Zantia_BoldTypeface_16>风切</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Zantia_BoldTypeface_16>Wind Blast</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————AzemRelic": "—————————————————————————————————————————————————阿泽姆圣遗物————————————————————————————————————————————————————————————"}, {"——————————————AzemRelic": "—————————————————————————————————————————————————阿泽姆圣遗物————————————————————————————————————————————————————————————"}, {"key": "LightOfAzem_8", "Chinese": "光明圣阳旗", "English": "Banner of Sunlight"}, {"key": "LightOfAzem_9", "Chinese": "日曜神剑", "English": "Divine Sword Of Sunlight"}, {"key": "LightOfAzem_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会减少自身受到的伤害，并</><Azem_BoldTypeface_16>耀光</><Default_BoldTypeface_16>攻击自己的敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will Reduce the damage taken and </><Azem_BoldTypeface_16>Shine</><Default_BoldTypeface_16> the enemy who attacks yourself.</>"}, {"key": "LightOfAzem_9_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Azem_BoldTypeface_16>闪光</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Azem_BoldTypeface_16>Blazing</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————EminendanisRelic": "—————————————————————————————————————————————————艾敏达妮斯圣遗物————————————————————————————————————————————————————————————"}, {"——————————————EminendanisRelic": "—————————————————————————————————————————————————艾敏达妮斯圣遗物————————————————————————————————————————————————————————————"}, {"key": "CorruptionOfErminda_8", "Chinese": "暗月手镰", "English": "<PERSON> Moon <PERSON>le"}, {"key": "CorruptionOfErminda_9", "Chinese": "枯萎面纱", "English": "Withered <PERSON>"}, {"key": "CorruptionOfErminda_8_Desc", "Chinese": "<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>会</><Eminendanis_BoldTypeface_16>侵蚀</><Default_BoldTypeface_16>攻击自己的敌人。</>", "English": "<Skill_BoldTypeface_16>Charge Skills</><Default_BoldTypeface_16> will </><Eminendanis_BoldTypeface_16>Erosed</><Default_BoldTypeface_16> the enemy who attacks yourself.</>"}, {"key": "CorruptionOfErminda_9_Desc", "Chinese": "<<Skill_BoldTypeface_16>蓄力技能</><Default_BoldTypeface_16>满层时，蓄力攻击会直接对敌人引发</><Eminendanis_BoldTypeface_16>暗噬</><Default_BoldTypeface_16>。</>", "English": "<Default_BoldTypeface_16>When </><Skill_BoldTypeface_16>Charge skill</><Default_BoldTypeface_16> is full, the charge attack will directly trigger </><Eminendanis_BoldTypeface_16>Dark Devour</><Default_BoldTypeface_16> on the enemies.</>"}, {"——————————————OtherGodsRelic": "—————————————————————————————————————————————————攻击力阿祖克————————————————————————————————————————————————————————————"}, {"——————————————OtherGodsRelic": "—————————————————————————————————————————————————攻击力阿祖克————————————————————————————————————————————————————————————"}, {"key": "ColdOfIlm_14", "Chinese": "水晶鞋", "English": "Glass Slipper"}, {"key": "Azouk_3", "Chinese": "死亡的背刺", "English": "Death's Backstab"}, {"key": "Azouk_4", "Chinese": "残忍的积累", "English": "Cruel Accumulation"}, {"key": "BurnOfIgni_19", "Chinese": "与火焰的交易", "English": "The trade with Flame"}, {"key": "ElementalExchange2", "Chinese": "元素互换", "English": "Element exchange"}, {"key": "ElementalExchange4", "Chinese": "元素互换", "English": "Element exchange"}, {"key": "Ardipeng_3", "Chinese": "苦痛终将治愈", "English": "Pain Will be Heal"}, {"key": "Ardipeng_6", "Chinese": "治愈之触", "English": "Healing Touch"}, {"key": "Ardipeng_7", "Chinese": "痛苦即是欢愉", "English": "Pain is Joy"}, {"key": "Ardipeng_8", "Chinese": "痛苦即是欢愉", "English": "Pain is Joy"}, {"key": "Lunara_ChargeSpeed1", "Chinese": "强蓄力", "English": "Charge acceleration"}, {"key": "Lunara_ChargeSpeed2", "Chinese": "强蓄力", "English": "Charge acceleration"}, {"key": "Lunara_DodgeSpeed1", "Chinese": "强力闪避", "English": "Dodge acceleration"}, {"key": "Lunara_DodgeSpeed2", "Chinese": "强力闪避", "English": "Dodge acceleration"}, {"key": "LuanaCriticalChanceForHP1", "Chinese": "收获时的喜悦", "English": "Joy at Harvest"}, {"key": "LuanaCriticalChanceForHP3", "Chinese": "收获时的喜悦", "English": "Joy at Harvest"}, {"key": "LuanaCriticalDamageForHP1", "Chinese": "收获时的庆典", "English": "Celebration at Harvest"}, {"key": "LuanaCriticalDamageForHP3", "Chinese": "收获时的庆典", "English": "Celebration at Harvest"}, {"key": "LuanaGodLife4", "Chinese": "收割时的馈赠", "English": "Harvest Gifts"}, {"key": "ColdOfIlm_14_Desc", "Chinese": "<Default_BoldTypeface_16>对处于特殊状态（</><Igni_BoldTypeface_16>燃烧</><Default_BoldTypeface_16>，</><Ilm_BoldTypeface_16>冰霜</><Default_BoldTypeface_16>，</><Poltick_BoldTypeface_16>电击</><Default_BoldTypeface_16>，</><Zantia_BoldTypeface_16>风蚀</><Default_BoldTypeface_16>，</><Azem_BoldTypeface_16>耀光</><Default_BoldTypeface_16>，</><Eminendanis_BoldTypeface_16>侵蚀</><Default_BoldTypeface_16>）的敌人造成的伤害提升15%，可叠加。</>", "English": "<Default_BoldTypeface_16>Damage dealt to enemies with <PERSON><PERSON><PERSON>(</><Igni_BoldTypeface_14>Burning</><Default_BoldTypeface_16>，</><Ilm_BoldTypeface_16>Frost</><Default_BoldTypeface_16>，</><Poltick_BoldTypeface_16>Electrocuted</><Default_BoldTypeface_16>，</><Zantia_BoldTypeface_16>Wind Erosion</><Default_BoldTypeface_16>，</><Azem_BoldTypeface_16>Shine</><Default_BoldTypeface_16>，</><Eminendanis_BoldTypeface_16>Erosed</><Default_BoldTypeface_16>) increase 15%, can be stacked.</>"}, {"key": "BurnOfIgni_19_Desc", "Chinese": "<Default_BoldTypeface_16>元素伤害增强40%，暴击伤害减少20%，可叠加。</>", "English": "<Default_BoldTypeface_16>Elemental Damage increase 40%, but critical damage decrease 20%, can be stacked.</>"}, {"key": "ElementalExchange2_Desc", "Chinese": "<Default_BoldTypeface_16>一级元素伤害增加30%，二级元素伤害减少10%。</>", "English": "<Default_BoldTypeface_16>Basic Elemental Damage increase 30%, but Advanced Elemental damage decrease 10%, can be stacked.</>"}, {"key": "ElementalExchange4_Desc", "Chinese": "<Default_BoldTypeface_16>二级元素伤害增加30%，一级元素伤害减少10%。</>", "English": "<Default_BoldTypeface_16>Advanced Elemental Damage increase 30%, but Basic Elemental damage decrease 10%, can be stacked.</>"}, {"key": "PhyscialDamage2_Desc", "Chinese": "<Default_BoldTypeface_16>物理伤害增强40%，暴击伤害减少20%，可叠加。</>", "English": "<Default_BoldTypeface_16>Elemental Damage increase 40%, but critical damage decrease 20%, can be stacked.</>"}, {"key": "Ardipeng_3_Desc", "Chinese": "<Default_BoldTypeface_16>法器CD的时候，暴击率提升10%。</>", "English": "<Default_BoldTypeface_16>When Artifact is in CD, Critical rate increase by 10%.</>"}, {"key": "Ardipeng_6_Desc", "Chinese": "<Default_BoldTypeface_16>法器CD的时候，暴击伤害提升20%。</>", "English": "<Default_BoldTypeface_16>When Artifact is in CD, Critical Damage increase by 20%</>"}, {"key": "Ardipeng_7_Desc", "Chinese": "<Default_BoldTypeface_16>敌人没有失衡值的时候，暴击率提升30%。</>", "English": "<Default_BoldTypeface_16>When enemy has no Break value, Critical rate increase by 30%.</>"}, {"key": "Ardipeng_8_Desc", "Chinese": "<Default_BoldTypeface_16>敌人没有失衡值的时候，暴击率提升20%。</>", "English": "<Default_BoldTypeface_16>When enemy has no Break value, Critical rate increase by 20%.</>"}, {"key": "Lunara_ChargeSpeed1_Desc", "Chinese": "<Default_BoldTypeface_16>蓄力满后3s内物理伤害提升50%,同种效果至多叠加100%</>", "English": "<Default_BoldTypeface_16>Physical Damage up 50% in 3s After Charge,same effect stack in 100% at most.</>"}, {"key": "Lunara_ChargeSpeed2_Desc", "Chinese": "<Default_BoldTypeface_16>蓄力满后3s内物理伤害提升30%,同种效果至多叠加100%</>", "English": "<Default_BoldTypeface_16>Physical Damage up 30% in 3s After Charge,same effect stack in 100% at most.</>"}, {"key": "Lunara_DodgeSpeed1_Desc", "Chinese": "<Default_BoldTypeface_16>完美闪避后3s内物理伤害提升50%,同种效果至多叠加100%</>", "English": "<Default_BoldTypeface_16>Physical Damage up 50% in 3s After Charge,same effect stack in 100% at most.</>"}, {"key": "Lunara_DodgeSpeed2_Desc", "Chinese": "<Default_BoldTypeface_16>完美闪避后3s内物理伤害提升30%,同种效果至多叠加100%</>", "English": "<Default_BoldTypeface_16>Physical Damage up 30% in 3s After Charge,same effect stack in 100% at most.</>"}, {"key": "LuanaCriticalChanceForHP1_Desc", "Chinese": "<Default_BoldTypeface_16>最大生命值提升50%，暴击率降低15%，可叠加。</>", "English": "<Default_BoldTypeface_16>Max HP increase 50%, Critical rate decrease 15%, can be stacked..</>"}, {"key": "LuanaCriticalChanceForHP3_Desc", "Chinese": "<Default_BoldTypeface_16>最大生命值提升20%，暴击率降低5%，可叠加。</>", "English": "<Default_BoldTypeface_16>Max HP increase 20%, Critical rate decrease 5%, can be stacked..</>"}, {"key": "LuanaCriticalDamageForHP1_Desc", "Chinese": "<Default_BoldTypeface_16>最大生命值提升50%，暴击伤害降低40%，可叠加。</>", "English": "<Default_BoldTypeface_16>Max HP increase 50%, Critical damage decrease 30%, can be stacked..</>"}, {"key": "LuanaCriticalDamageForHP3_Desc", "Chinese": "<Default_BoldTypeface_16>最大生命值提升20%，暴击伤害降低20%，可叠加。</>", "English": "<Default_BoldTypeface_16>Max HP increase 20%, Critical damage decrease 10%, can be stacked..</>"}, {"key": "LuanaGodLife4_Desc", "Chinese": "<Default_BoldTypeface_16>最大生命提升50%，受击伤害提升50%。</>", "English": "<Default_BoldTypeface_16>Your Max HP increase by 50%, Damage taken increase by 50%.</>"}, {"——————————————Artifact": "—————————————————————————————————————————————————法器————————————————————————————————————————————————————————————"}, {"——————————————Artifact": "—————————————————————————————————————————————————法器————————————————————————————————————————————————————————————"}]}