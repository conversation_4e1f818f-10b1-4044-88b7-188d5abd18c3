{"GameTexts": [{"说明": "这其实不太需要说明……一个key对应一句话或者一个单词"}, {"Key": "RequireReachLevel", "Chinese": "需要达到等级", "English": "You need to be graded"}, {"Key": "InEquip", "Chinese": "装备中", "English": "Equipped"}, {"Key": "UnEquipped", "Chinese": "未装备", "English": "Not Equipped"}, {"Key": "Shopping_Buy", "Chinese": "购买 ", "English": "Buy"}, {"Key": "Shopping_CantAffort", "Chinese": "无法购买", "English": "Cannot afford"}, {"Key": "WeaponType_Unarmed", "Chinese": "徒手", "English": "UnEquipped"}, {"Key": "WeaponType_BigSword", "Chinese": "大剑", "English": "GreatSword"}, {"Key": "WeaponType_TwinSword", "Chinese": "双剑", "English": "TwinSword"}, {"Key": "WeaponType_SwordShield", "Chinese": "剑盾", "English": "SwordShield"}, {"Key": "WeaponType_Lance", "Chinese": "长枪", "English": "Spear"}, {"Key": "ArmorPart_Helmet", "Chinese": "头盔", "English": "<PERSON><PERSON><PERSON>"}, {"Key": "ArmorPart_Body", "Chinese": "上装", "English": "Armor"}, {"Key": "ArmorPart_Pant", "Chinese": "下装", "English": "Boots"}, {"Key": "ArmorPart_Hand", "Chinese": "手套", "English": "Gloves"}, {"Key": "ArmorPart_Amulet", "Chinese": "项链", "English": "Necklace"}, {"Key": "ArmorPart_Ring", "Chinese": "戒指", "English": "Ring"}, {"Key": "Terrain_Dirt", "Chinese": "泥地", "English": "Buy"}, {"Key": "Terrain_Grass", "Chinese": "草地", "English": "Buy"}, {"Key": "Terrain_Water", "Chinese": "水中", "English": "Buy"}, {"Key": "Terrain_Wood", "Chinese": "木质地板", "English": "Buy"}, {"Key": "Terrain_Icy", "Chinese": "冰面", "English": "Buy"}, {"Key": "Terrain_Rock", "Chinese": "岩石", "English": "Buy"}, {"Key": "Terrain_Magma", "Chinese": "熔岩", "English": "Buy"}, {"Key": "Weather_Sunny", "Chinese": "晴天", "English": "<PERSON>"}, {"Key": "Weather_Rainy", "Chinese": "雨天", "English": "Rainy"}, {"Key": "Weather_Snowy", "Chinese": "雪天", "English": "Snowy"}, {"Key": "Weather_Volcano", "Chinese": "火山灰弥漫", "English": "Buy"}, {"Key": "Elemental_Occur_OnHit", "Chinese": "迸发式元素", "English": "Buy"}, {"Key": "Elemental_Occur_OnUse", "Chinese": "涌现式元素", "English": "Buy"}, {"Key": "Elemental_Occur_Passive", "Chinese": "被动生效元素", "English": "Buy"}, {"Key": "Elemental_Occur_Action", "Chinese": "元素独有技能", "English": "Buy"}, {"Key": "Text_Continue", "Chinese": "继续", "English": "Continue"}, {"Key": "Text_ReturnTool", "Chinese": "交还铁匠的工具", "English": "Return Blacksmith's Tools"}, {"Key": "Elevator_Fixed", "Chinese": "升降梯已修好", "English": "Lift has been repaired"}, {"Key": "Get_Elevator_Stick", "Chinese": "获得升降梯把手", "English": "Get lift handle"}, {"Key": "Get_Vendor_Goods", "Chinese": "获得旅行商人的物品", "English": "Get items of the Traveling Merchant"}, {"Key": "Get_Blacksmith_Tool", "Chinese": "获得铁匠的工具", "English": "Get Blacksmith's Tools"}, {"Key": "Change_Skill", "Chinese": "切换技能", "English": "Change Skills"}, {"Key": "Change_Class", "Chinese": "切换职业", "English": "Change Classes"}, {"Key": "Interact_Dialog", "Chinese": "对话", "English": "Talk"}, {"Key": "Interact_Shop", "Chinese": "商店", "English": "Shop"}, {"Key": "Interact_Take", "Chinese": "获取", "English": "Pick"}, {"Key": "Interact_UseLift", "Chinese": "使用升降梯", "English": "UseLift"}, {"Key": "Interact_Use", "Chinese": "使用", "English": "Use"}, {"Key": "Interact_Check", "Chinese": "调查", "English": "Check"}, {"___________________________________Setting__": "__设置______________________________________________________"}, {"Key": "Chinese", "Chinese": "中文", "English": "Chinese"}, {"Key": "English", "Chinese": "英语", "English": "English"}, {"________________________________________UI__": "__通用______________________________________________________"}, {"Key": "Selected", "Chinese": "已选择", "English": "Selected"}, {"Key": "Equipped", "Chinese": "已装备", "English": "Equipped"}, {"Key": "SelectItem", "Chinese": "选择道具", "English": "SelectItem"}, {"Key": "SkillA", "Chinese": "技能A", "English": "Skill A"}, {"Key": "SkillB", "Chinese": "技能B", "English": "Skill B"}, {"Key": "SkillC", "Chinese": "技能C", "English": "Skill C"}, {"Key": "Interaction", "Chinese": "交互", "English": "Interact"}, {"Key": "LightAttack", "Chinese": "轻攻击", "English": "Attack"}, {"Key": "Roll", "Chinese": "翻滚", "English": "Roll"}, {"Key": "Jump", "Chinese": "跳跃", "English": "Jump"}, {"Key": "<PERSON><PERSON>terWeapon", "Chinese": "收起武器", "English": "<PERSON><PERSON>terWeapon"}, {"Key": "UseItems", "Chinese": "使用道具", "English": "UseItem"}, {"Key": "Aim", "Chinese": "瞄准", "English": "Aim"}, {"Key": "<PERSON><PERSON>", "Chinese": "菜单", "English": "<PERSON><PERSON>"}, {"Key": "Have", "Chinese": "持有", "English": "Have"}, {"Key": "NewGameAlerts", "Chinese": "开始新的游戏并重置游戏进度", "English": "Ready to create character and start game?"}, {"Key": "force", "Chinese": "势力", "English": "Faction"}, {"Key": "Air", "Chinese": "空中", "English": "Air"}, {"Key": "Ground", "Chinese": "地面", "English": "Ground"}, {"________________________________________UI__": "__Boss名字______________________________________________________"}, {"Key": "Ogre", "Chinese": "巨魔", "English": "Ogre"}, {"________________________________________UI__": "__任务______________________________________________________"}, {"Key": "AcceptanceTask", "Chinese": "接受任务", "English": "Accept Quest"}, {"Key": "CompleteTask", "Chinese": "完成任务", "English": "Quest Complete"}, {"_____________________________________11.29__": "__新任务______________________________________________________"}, {"Key": "RodianMainLine01", "Chinese": "驱赶洛迪安城镇中的啮齿魔", "English": "Evict Rodent Demons from Town Rodian ", "任务标题": "任务阶段 : RodianStory01"}, {"Key": "KillRats", "Chinese": "跟随副官一起消灭啮齿魔", "English": "Follow the Adjutant to exterminate the Rodent Demons"}, {"Key": "TalkWithCaptainIn01", "Chinese": "与指挥官对话", "English": "Talk with Commander"}, {"Key": "TalkWithCaptainIn01Again", "Chinese": "再次与指挥官对话", "English": "Talk with Commander again"}, {"Key": "RodianMainLine02", "Chinese": "与护卫队一同击退矿洞口的啮齿魔", "English": "Repulse the Rodent Demons near the mine entrance with the guards", "任务标题": "任务阶段 : RodianStory02"}, {"Key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Chinese": "破坏矿洞口的障碍物", "English": "Destroy the obstacles at the mine entrance"}, {"Key": "SaveGuardInElevatorRoom", "Chinese": "救援护卫小队并且引爆炸药", "English": "Rescue the guards and ignite the explosives"}, {"Key": "TalkWithGogaros", "Chinese": "与冈加洛斯对话", "English": "Talk with <PERSON><PERSON><PERSON>"}, {"Key": "BlowUpThePassage", "Chinese": "炸毁啮齿魔出现的洞口", "English": "Bomb the entrance where the Rodent Demons appears"}, {"Key": "TalkWithCaptainIn02", "Chinese": "回到城镇向指挥官报告情况", "English": "Return to the town and report to Commander"}, {"Key": "TalkWithCaptainIn02_2", "Chinese": "向指挥官报告情况", "English": "Report to Commander"}, {"Key": "RodianMainLine03", "Chinese": "大战前的准备", "English": "Preparation before the big battle", "任务标题": "任务阶段 : RodianStory03"}, {"Key": "TalkWithCaptainIn03", "Chinese": "与指挥官对话", "English": "Talk with the Commander"}, {"Key": "WeakenRats", "Chinese": "削弱啮齿魔的势力,破坏建筑", "English": "Weaken Rodent Demons' forces"}, {"Key": "TakWithCaptainIn03Again", "Chinese": "回到城镇向指挥官报告", "English": "Return to the town and report to Commander"}, {"Key": "RodianMainLine04", "Chinese": "决一死战", "English": "The Final Battle", "任务标题": "任务阶段 : RodianStory04"}, {"Key": "FindCaptainInMine", "Chinese": "与矿洞前线基地里的指挥官对话", "English": "Talk with Commander at the mine's frontline base"}, {"Key": "FightWithRats", "Chinese": "进攻啮齿魔基地", "English": "Attack Rodent Demons' base"}, {"Key": "KillOgre", "Chinese": "杀死巨魔", "English": "Kill the Ogre"}, {"Key": "TalkWithCaptainInMineAgain", "Chinese": "与指挥官对话", "English": "Talk with Commander"}, {"Key": "RodianMainLine05", "Chinese": "调查隐藏在深处中的黑暗", "English": "Investigate the darkness hidden in the depths", "任务标题": "任务阶段 : RodianStory05"}, {"Key": "TalkWithCaptainForIn05", "Chinese": "回到城镇向指挥官打听情况", "English": "Return to the town and find out more about the situation from Commander"}, {"Key": "FindTheExpedition", "Chinese": "寻找探险队的踪迹", "English": "Search for the traces of the expedition "}, {"Key": "FindIceDevil", "Chinese": "调查矿洞深处中的黑暗", "English": "Investigate the darkness in the depths of the mine"}, {"Key": "ChangeClassOrSkill", "Chinese": "更换职业或技能", "English": "Change occupation or skills"}, {"Key": "HelpB<PERSON>cksmith", "Chinese": "帮助铁匠", "English": "Help the blacksmith "}, {"Key": "LearnChangeClass", "Chinese": "尝试更换职业或技能", "English": "Try to change occupation or skills"}, {"Key": "FindABlacksmithTool", "Chinese": "寻找铁匠工具", "English": "Search for blacksmith's tools"}, {"Key": "ExchangeBlacksmithTools", "Chinese": "交还铁匠工具", "English": "Give back balcksmith's tools"}, {"________________________________________UI__": "__系统设置______________________________________________________"}, {"Key": "MainVolume", "Chinese": "主音量", "English": "Main volume"}, {"Key": "Music", "Chinese": "音乐", "English": "Music volume"}, {"Key": "SoundEffect", "Chinese": "音效", "English": "Sound effect volume"}, {"Key": "Voice", "Chinese": "语音", "English": "Voice volume"}, {"Key": "Language", "Chinese": "语言", "English": "Language"}, {"Key": "KeyPromptType", "Chinese": "手柄提示类型", "English": "Gamepad icon type"}, {"Key": "QuitGame", "Chinese": "退出游戏", "English": "QUIT GAME"}, {"Key": "BackToMenu", "Chinese": "返回主菜单", "English": "Main Menu"}, {"Key": "Chinese", "Chinese": "中文", "English": "Chinese"}, {"Key": "English", "Chinese": "英文", "English": "English"}, {"Key": "Open", "Chinese": "开", "English": "Open"}, {"Key": "Close", "Chinese": "关", "English": "Close"}, {"Key": "Dark", "Chinese": "黑底", "English": "Dark"}, {"Key": "Light", "Chinese": "白底", "English": "Light"}, {"Key": "OutLine", "Chinese": "线框", "English": "OutLine"}, {"Key": "CameraAimAssist", "Chinese": "玩家视角辅助瞄准", "English": "Camera Aim <PERSON>"}, {"Key": "UISound", "Chinese": "界面音效", "English": "UI Sound"}, {"________________________________________UI__": "__地图名字______________________________________________________"}, {"Key": "TestBattle", "Chinese": "测试战斗场景", "English": "English"}, {"Key": "TestCountry", "Chinese": "落山只因的乡下", "English": "English"}, {"Key": "TestDungeon", "Chinese": "测试地牢", "English": "English"}, {"Key": "<PERSON><PERSON><PERSON>", "Chinese": "测试啥场景", "English": "English"}, {"Key": "TestDungeonRoom01", "Chinese": "测试地牢房间01", "English": "English"}, {"Key": "TestDungeonRoom02", "Chinese": "测试地牢房间02", "English": "English"}, {"Key": "TestDungeonRoom03", "Chinese": "测试地牢房间03", "English": "English"}, {"Key": "TestDungeonRoom04", "Chinese": "测试地牢房间04", "English": "English"}, {"Key": "TestDungeonRoom05", "Chinese": "测试地牢房间05", "English": "English"}, {"Key": "TestDungeonRoom06", "Chinese": "测试地牢房间06", "English": "English"}, {"Key": "TestDungeonRoom07", "Chinese": "测试地牢房间07", "English": "English"}, {"Key": "TestDungeonEntrance", "Chinese": "测试地牢入口", "English": "English"}, {"Key": "Village_Main", "Chinese": "洛笛安镇", "English": "Rodian Village"}, {"________________________________________UI__": "__属性名字______________________________________________________"}, {"Key": "HP", "Chinese": "生命值", "English": "HP"}, {"Key": "ATK", "Chinese": "攻击力", "English": "ATK"}, {"Key": "DEF", "Chinese": "防御力", "English": "DEF"}, {"________________________________________UI__": "__开始菜单名字__________________________________________________"}, {"Key": "NewGame", "Chinese": "新的游戏", "English": "NEWGAME"}, {"Key": "ContinueGame", "Chinese": "继续游戏", "English": "CONTINUE"}, {"________________________________________UI__": "__暂停菜单名字__________________________________________________"}, {"Key": "Equipment", "Chinese": "装备变更", "English": "EQUIPMENT"}, {"Key": "ItemShortcutBar", "Chinese": "快捷道具", "English": "INVENTORY"}, {"Key": "PowerMap", "Chinese": "势力进度", "English": "FACTION PROGRESS"}, {"Key": "SystemSettings", "Chinese": "系统设置", "English": "OPTIONS"}, {"Key": "BackGameTitle", "Chinese": "返回主菜单", "English": "MAIN MENU"}, {"Key": "Map", "Chinese": "地图", "English": "MAP"}, {"________________________________________UI__": "__道具名字______________________________________________________"}, {"Key": "HealingPotion", "Chinese": "回复药", "English": "Potion"}, {"Key": "FireBallScroll", "Chinese": "火球卷轴", "English": "Pyroblast Scroll"}, {"Key": "<PERSON>ch", "Chinese": "火把", "English": "<PERSON>ch"}, {"Key": "Cheese", "Chinese": "奶酪", "English": "Cheese"}, {"Key": "PyroblastScroll", "Chinese": "炎爆术卷轴", "English": "Pyroblast Scroll"}, {"Key": "BlacksmithTool", "Chinese": "铁匠的工具", "English": "Blacksmith's Tool"}, {"Key": "DwarfSpirit", "Chinese": "矮人烈酒", "English": "Dwarven Spirit"}, {"________________________________________UI__": "__道具详情______________________________________________________"}, {"Key": "HealingPotionExplain", "Chinese": "回复一定的的生命值", "English": "Restore a determined amount of HP."}, {"Key": "FireBallScrollExplain", "Chinese": "能造成火焰伤害的魔法卷轴", "English": "A magic scroll that deals fire damage."}, {"Key": "TorchExplain", "Chinese": "具有照明效果的火把", "English": "Torch that provides light."}, {"Key": "CheeseExplain", "Chinese": "黏糊糊的奶酪，似乎是啮齿类动物喜爱的食物", "English": "Gooey cheese, rodents seem to love it."}, {"________________________________________UI__": "__地图名字______________________________________________________"}, {"Key": "TestDungeon", "Chinese": "科斯特洞穴", "English": "Costle Cave"}, {"________________________________________UI__": "__货币详情______________________________________________________"}, {"Key": "Gold", "Chinese": "金币", "English": "Coin"}, {"________________________________________UI__": "__新手引导______________________________________________________"}, {"Key": "<PERSON><PERSON>_Weapon", "Chinese": "按↓拔出武器战斗", "English": "Press ↓ to draw your weapon and fight."}, {"Key": "Newbie_Block", "Chinese": "敲碎障碍物进入矿洞", "English": "Break the obstacles to enter the mine."}, {"Key": "<PERSON><PERSON>_Torch", "Chinese": "按↓收起武器可以拾取火把", "English": "Press ↓ to put away your weapon to pick up the torch."}, {"Key": "Newbie_Enterance", "Chinese": "从这里进入矿洞对抗啮齿魔", "English": "Enter the mine from here to fight the Ratmen."}, {"Key": "<PERSON><PERSON>_Attack", "Chinese": "按△口和R1发动攻击", "English": "Press △ and R1 to attack."}, {"Key": "<PERSON><PERSON>_<PERSON>", "Chinese": "利用○翻滚来躲避或用R2格挡攻击", "English": "Use ○ to dodge, or block attacks with R2."}, {"Key": "Newbie_ChangeClassAndSkill", "Chinese": "在这里切换职业和技能", "English": "Change classess and skills here."}, {"Key": "NewbieRetrospect", "Chinese": "新手回顾", "English": "TOTURIALSREVIEW"}, {"Key": "<PERSON>bie_DrawWeapon", "Chinese": "装备武器", "English": "Equipment"}, {"Key": "<PERSON><PERSON>_Attack", "Chinese": "攻击与技能", "English": "Attack and Skills"}, {"Key": "Newbie_DamageAvoidance", "Chinese": "防御与闪避", "English": "Defend and Dodge"}, {"Key": "Newbie_ChangeClass", "Chinese": "职业和技能设置", "English": "Class and Skills"}, {"Key": "<PERSON><PERSON>_BreakDoodad", "Chinese": "可破环场景物件", "English": "Destructible Items"}, {"Key": "Newbie_GatherKeyItem", "Chinese": "关键物品收集", "English": "Important Items Collection"}, {"Key": "Newbie_UseItem", "Chinese": "道具使用", "English": "Props Usage"}, {"Key": "Newbie_ChangeEquipment", "Chinese": "武器和装备", "English": "Weapons and Equipment"}, {"________________________________________UI__": "__职业名字______________________________________________________"}, {"Key": "Swordsman", "Chinese": "剑士", "English": "Swordsman"}, {"Key": "Warrior", "Chinese": "勇士", "English": "Warrior"}, {"Key": "<PERSON><PERSON><PERSON>", "Chinese": "枪兵", "English": "<PERSON><PERSON><PERSON>"}, {"Key": "BladeDancer", "Chinese": "剑舞者", "English": "Blade Dancer"}, {"________________________________________UI__": "__职业故事或说明_________________________________________________"}, {"Key": "SwordsmanExplain", "Chinese": "剑士擅长使用剑盾协同作战，在防御敌人攻击的同时寻找时机给予致命打击。是近战职业中最基础的初始职业，所有高难度的招式都源于战士的职业技能。除了举盾外，战士也具备一定的滞空手段和陆空双突进能力。", "English": "These Swordsmen are good at using both swords and shields together, always looking for the opportunity to deliver a fatal blow while defending against enemy attacks. It's the most basic initial classes in the melee path, and all difficult moves are derived from these core skills."}, {"Key": "WarriorExplain", "Chinese": "勇士喜欢用大剑或巨剑作为武器打击敌人。勇士的每一招都充满了力量感，使用蓄力攻击更能将勇士的力量优势发挥到极致。除此之外，勇士还能运用武器的重量优势防御反击对手，让对手进入硬直，再将其挑空对其进一步输出控制。。", "English": "A warrior's moves are powerful, using charged attacks to use their strength to their advantage in battle. Warriors can also use their power to travel large distances in seconds, providing them a distinct battlefield advantage."}, {"Key": "SpearmanExplain", "Chinese": "枪兵的作战方式比起其它近战职业更注重垂直方向的打击，操作上也更具灵活性。枪兵擅长将敌人代入自己的「空中领域」，在空中不断打出高额伤害。每一次挑空敌人后都会为自己积攒战意，随着战意的累积造成的垂直伤害也就越高。", "English": "Unlike other melee classes, the spearman's combat style focuses more on vertical strikes and Airborne fighting style. Spearmen are good at pulling enemies into the air where they perform best, continuously dealing high damage."}, {"Key": "BladeDancerExplain", "Chinese": "剑舞者是战士系职业中十分注重连击也是作战风格最迅捷的一种。剑舞者的招数非常多变，在命中敌人后可以将普通招式派生成强化攻击从而打出多种多样的华丽招式。在闪避能力上也更灵活，运用得当的话即使被包围也能轻松应对。", "English": "The Sword Dancer attaches great importance to combos, creating the fastest combat style in the game. Upon hitting enemies, the Sword Dancer's regular moves can be chained in many different ways to create intricate sword spinning combos."}, {"________________________________________UI__": "__Warrior技能名字________________________________________________"}, {"Key": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Chinese": "巨刃突刺", "English": "Great Blade Thrust"}, {"Key": "Warrior_DashSlashAttack1", "Chinese": "突袭迅斩", "English": "Raid Slash"}, {"Key": "Warrior_AirDoubleStrike0", "Chinese": "空中突袭迅斩", "English": "Air Raid Slash"}, {"Key": "<PERSON><PERSON><PERSON>", "Chinese": "剑意反击", "English": "Great Blade <PERSON>"}, {"Key": "Warrior_AirRush_DemonSlayer", "Chinese": "猎魔突袭斩", "English": "Demon Slayer's Slash"}, {"Key": "Warrior_Air_FallSlash", "Chinese": "冲地斩", "English": "Ground Smash"}, {"Key": "Warrior_ChargeAttack", "Chinese": "蓄力斩", "English": "Charge Slash"}, {"Key": "Warrior_ChargeJumpAttack", "Chinese": "崩裂蓄力斩", "English": "Destruction Slash"}, {"Key": "Warrior_AirChargeAttack", "Chinese": "空中蓄力斩", "English": "Air Charge Slash"}, {"____________________": "____________________LinkAction____________________"}, {"Key": "Warrior_StingUpperSlash", "Chinese": "巨刃破空斩", "English": "Great Blade Slash"}, {"Key": "Warrior_StingComboSlash", "Chinese": "巨刃剑风斩", "English": "Great Blade Wind Slash"}, {"Key": "Warrior_DashSlashAttack2", "Chinese": "突袭横扫斩", "English": "Sweep Slash"}, {"Key": "Warrior_AirDoubleStrike1", "Chinese": "空中突袭横扫斩", "English": "Air Sweep Slash"}, {"Key": "Warrior_CounterDashAttack", "Chinese": "反击迅斩", "English": "Counter Strike"}, {"Key": "Warrior_CounterDashAttack_JustBlock", "Chinese": "反击横扫斩", "English": "Counter Sweep Slash"}, {"________________________________________UI__": "__BladeDancer技能名字_____________________________________________"}, {"Key": "BladeDancer_DashAttack", "Chinese": "迅捷斩", "English": "Swift Slash"}, {"Key": "BladeDancer_AirTwiceComboAttack", "Chinese": "片刃双击", "English": "Twice Slash"}, {"Key": "BladeDancer_RiseComboSlash", "Chinese": "连击升空斩", "English": "Ascension Combo Slash"}, {"Key": "BladeDancer_RiseSlash", "Chinese": "升空斩", "English": "Ascension Slash"}, {"Key": "BladeDancer_AirDashAttack", "Chinese": "空中迅捷斩", "English": "Air Swift Slash"}, {"Key": "BladeDancer_AirDashComboAttack", "Chinese": "刃风迅捷斩", "English": "<PERSON><PERSON> Blade"}, {"Key": "BladeDancer_SwrodDanceComboAttack", "Chinese": "锋刃剑舞", "English": "the Edge of Blade Dance"}, {"Key": "BladeDancer_AirSwrodDanceComboAttack_Fall", "Chinese": "环剑大风车", "English": "Spinning Slash"}, {"Key": "BladeDancer_AirSwrodDanceComboAttack_Dash", "Chinese": "回旋空乱舞", "English": "Hurricane Dance"}, {"____________________": "____________________LinkAction____________________"}, {"Key": "BladeDancer_RiseComboSlash_AJ", "Chinese": "连击升空斩", "English": "Ascension Combo Slash(JA)"}, {"Key": "BladeDancer_RiseSlash_AJ", "Chinese": "升空快斩", "English": "Launch Slash(JA)"}, {"Key": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "Chinese": "迅捷突斩", "English": "Swift Slash(JA)"}, {"Key": "BladeDancer_DashAttackLeft_AJ", "Chinese": "迅捷左旋斩", "English": "Swift Left Slash(JA)"}, {"Key": "BladeDancer_DashAttackRight_AJ", "Chinese": "迅捷右旋斩", "English": "Swift Right Slash(JA)"}, {"Key": "BladeDancer_SwrodDanceDashComboAttack_AJ", "Chinese": "剑舞突袭", "English": "Sword Dance Raid(JA)"}, {"________________________________________UI__": "__<PERSON><PERSON><PERSON>技能名字_________________________________________________"}, {"Key": "Spearman_SweapAttack1", "Chinese": "回旋乱舞 I", "English": "Sweap Dance I"}, {"Key": "Spearman_AirDownSpikeAttack1", "Chinese": "下落突刺", "English": "Down Thrust"}, {"Key": "S<PERSON><PERSON>_DashSpike", "Chinese": "突进穿刺", "English": "Impaling Thrust"}, {"Key": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown", "Chinese": "俯冲突刺", "English": "Diving Thorns"}, {"Key": "S<PERSON><PERSON>_DashSweapSlash", "Chinese": "突进横扫", "English": "Dash Sweap"}, {"Key": "S<PERSON><PERSON>_BackJumpSweapSlash", "Chinese": "后跳横扫", "English": "Sweeping Back"}, {"Key": "Spearman_AirDashSweapSlash", "Chinese": "空中突进横扫", "English": "Air Dash Sweap"}, {"____________________": "____________________LinkAction____________________"}, {"Key": "Spearman_SweapAttack2", "Chinese": "回旋乱舞 II", "English": "Sweap Dance II"}, {"Key": "Spearman_SweapAttack3", "Chinese": "回旋乱舞 III", "English": "Sweap Dance III"}, {"Key": "Spearman_SweapAttack4", "Chinese": "回旋乱舞 IV", "English": "Sweap Dance IV"}, {"Key": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "Chinese": "枪刃三连击", "English": "Spearblade Triple Strike"}, {"Key": "<PERSON><PERSON><PERSON>_RiseSlash_Combo", "Chinese": "枪刃破空击", "English": "Spearblade Air Strike"}, {"Key": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Chinese": "飞空击", "English": "Air Strike"}, {"Key": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "Chinese": "升空击", "English": "Launch Strike"}, {"Key": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Chinese": "跃身斩", "English": "Leaping Slash"}, {"________________________________________UI__": "__Swordsman技能名字________________________________________________"}, {"Key": "Swordsman_RiseSlash", "Chinese": "飞升斩", "English": "Ascension Slash"}, {"Key": "Swordsman_RiseComboSlash", "Chinese": "裂空斩", "English": "Splitting Slash"}, {"Key": "Swordsman_AirDownSlashAttack1", "Chinese": "下冲斩", "English": "Downward Slash"}, {"Key": "Swordsman_DashSlash", "Chinese": "突进斩", "English": "Breakthrough Slash"}, {"Key": "Swordsman_AirDashSting", "Chinese": "冲锋突刺", "English": "Assault Burst"}, {"Key": "Swordsman_Defense", "Chinese": "盾牌防御", "English": "Shield Defense"}, {"Key": "Swordsman_AirDownShieldSmash", "Chinese": "盾坠击", "English": "Shield Fall Strike"}, {"____________________": "____________________LinkAction____________________"}, {"Key": "Swordsman_DashSlash2", "Chinese": "突进扫斩", "English": "Breakout Slash"}, {"Key": "Swordsman_Defense_Attack1", "Chinese": "盾牌连击 I", "English": "Shield Smash I"}, {"Key": "Swordsman_Defense_Attack1_2", "Chinese": "盾牌连击 II", "English": "Shield Smash II"}, {"Key": "Swordsman_Defense_Attack2", "Chinese": "冲击盾", "English": "Shield Crash"}, {"Key": "Swordsman_Defense_Attack3", "Chinese": "勇气冲锋", "English": "Courageous Charge"}, {"Key": "Swordsman_Defense_JustBlock", "Chinese": "震盾击", "English": "Shock Shield Strike"}, {"Key": "Swordsman_UnName", "Chinese": "震地盾击", "English": "Ground Shield Slam"}, {"________________________________________UI__": "__Warrior技能描述________________________________________________"}, {"Key": "<PERSON>_Sting<PERSON>ash<PERSON><PERSON>ck_Desc", "Chinese": "提起大剑向前中范围冲锋并造成多端伤害。", "English": "Raise your sword to charge forward and deal damage to mid-range enemies."}, {"Key": "Warrior_DashSlashAttack1_Desc", "Chinese": "向前方快速位移一小段距离并造成范围伤害。", "English": "Quickly dash forward a short distance and deal ranged damage."}, {"Key": "Warrior_AirDoubleStrike0_Desc", "Chinese": "滞空时使用此技能对敌人造成最多二段的伤害，第二段攻击能将小体型目标挑入空中随后自身落地。", "English": "Use this skill while airborne to deal up to two strikes to enemies; the second strike can drag small targets into the air."}, {"Key": "<PERSON>_<PERSON>_<PERSON>", "Chinese": "敌人攻击命中的瞬间按下防御，可无视其攻击并对其造成大量伤害。", "English": "Press Defend the moment an enemy attack hits to deflect it and deal damage back to them.(Just Block:The moment you be hitted,Use this skill）"}, {"Key": "Warrior_AirRush_DemonSlayer_Desc", "Chinese": "滞空时使用此技能向上跳跃一小段距离后，奋力向前冲刺，落地的同时利用强力的斩击对对手造成猛烈的一击。", "English": "Use this skill while airborne to jump up a short distance and then dash forward, landing a powerful attack on any nearby enemies.（JustBlock）"}, {"Key": "Warrior_Air_FallSlash_Desc", "Chinese": "滞空时使用此技能，向下冲击的同时对地面敌人造成范围伤害。", "English": "Use this skill while airborne to deal ranged damage to grounded enemies."}, {"Key": "Warrior_ChargeAttack_Desc", "Chinese": "抬起大剑蓄力，最多可达3段蓄力，最后一段蓄力斩先向前横扫压制对手，随后再向前方奋力一劈。", "English": "Raise your sword to charge-up your attack up to 3 times, with the final charge-up slashing forward and dealing significant damage to enemies."}, {"Key": "Warrior_ChargeJumpAttack_Desc", "Chinese": "抬起大剑蓄力，最多可达3段蓄力，最后一段蓄力斩向前方跃起后向下重劈，造成范围伤害。", "English": "Raise your sword to charge-up up to 3 times, with the final charge-up causing you to leap forward and slash downward heavily, dealing ranged damage to any nearby enemies."}, {"Key": "Warrior_AirChargeAttack_Desc", "Chinese": "滞空时使用此技能。对地面敌人造成范围伤害并落地。", "English": "Use this skill to ground yourself and deal ranged damage to nearby enemies."}, {"____________________": "____________________LinkAction____________________"}, {"Key": "Warrior_StingUpperSlash_Desc", "Chinese": "进行“巨刃突刺”并且命中时，将目标挑入空中。", "English": "When “Great Blade Thrust” hits an enemy, send enemies along the way flying."}, {"Key": "Warrior_StingComboSlash_Desc", "Chinese": "进行“巨刃突刺”并且命中时，对沿途敌人造成多段伤害并击飞敌人。", "English": "When “Great Blade Thrust” hits an enemy, deal damage to enemies along the way and send them flying."}, {"Key": "Warrior_DashSlashAttack2_Desc", "Chinese": "进行“突袭斩”时,向前方突进的同时，横扫周围的敌人，并将其击飞。", "English": "When You are using Skill “Raid Slash”, move forwards while sending enemies around you flying."}, {"Key": "Warrior_AirDoubleStrike1_Desc", "Chinese": "滞空时使用此技能对敌人造成最多二段的伤害，第二段攻击能将小体型目标挑入空中随后自身落地。", "English": "Use this skill while airborne to deal up to two strikes to enemies; the second strike can drag small targets into the air."}, {"Key": "Warrior_CounterDashAttack_Desc", "Chinese": "“剑意反击”成功时，向前方快速位移一小段距离并造成范围伤害。", "English": "When You successfully using Skill“Great Blade Parry”，Quickly dash forward a short distance and deal ranged damage."}, {"Key": "Warrior_CounterDashAttack_JustBlock_Desc", "Chinese": "“剑意反击”成功时，向前方突进并且横扫周围的敌人，将其击飞。", "English": "When You successfully using Skill“Great Blade Parry”，Sweap your greatsword and blow up your enemy"}, {"________________________________________UI__": "__BladeDancer技能描述_____________________________________________"}, {"Key": "BladeDancer_DashAttack_Desc", "Chinese": "向前突进的同时向前方挥砍双剑，并对沿途敌人造成伤害。", "English": "Swing forward with both swords and deal damage to enemies along the way."}, {"Key": "BladeDancer_AirTwiceComboAttack_Desc", "Chinese": "滞空时以迅捷的动作发动两段斩击。", "English": "Launch a swift two-stage attack while airborne."}, {"Key": "BladeDancer_RiseComboSlash_Desc", "Chinese": "使用二连斩击，后跳的同时挑飞身前敌人。", "English": "Using a two-strike attack, simultaneously leap backwards and pick up enemies in front of you."}, {"Key": "BladeDancer_RiseSlash_Desc", "Chinese": "快速的二连斩击，跃起的同时挑飞身前敌人。", "English": "A quick two-strike attack, simultaneously dashing and attacking enemies in front of you."}, {"Key": "BladeDancer_AirDashAttack_Desc", "Chinese": "滞空时，向前突进并对沿途敌人造成伤害。", "English": "Dive forward and deal damage to any passing enemies while airborne."}, {"Key": "BladeDancer_AirDashComboAttack_Desc", "Chinese": "滞空时，向后转身并且挥出迅捷的一剑，再向前突进并对沿途敌人造成伤害。", "English": "Turn back to slash, then burst forward and deal damage to passing enemies while airborne."}, {"Key": "BladeDancer_SwrodDanceComboAttack_Desc", "Chinese": "使用双剑带动身体旋转而使出的剑舞动作", "English": "Sword dance by using both swords to spin your body."}, {"Key": "BladeDancer_AirSwrodDanceComboAttack_Fall_Desc", "Chinese": "滞空时，旋转自身的同时带动双剑挥砍，形成多连段的伤害直到落地。", "English": "Spin and swing your sword to deal damage while airborne until you land."}, {"Key": "BladeDancer_AirSwrodDanceComboAttack_Dash_Desc", "Chinese": "滞空时，旋转自身的同时带动双剑挥砍，向前位移的同时形成多连段的伤害。", "English": "Spin and swing your sword while airborne to move forward and deal damage."}, {"____________________": "____________________LinkAction____________________"}, {"Key": "BladeDancer_RiseComboSlash_AJ_Desc", "Chinese": "使用二连斩击，后跳的同时挑飞身前敌人。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "Using a two-strike attack, simultaneously leap backwards and pick up enemies in front of you. (<PERSON><PERSON><PERSON><PERSON>：The moment you hit an enemy,Use this skill)"}, {"Key": "BladeDancer_RiseSlash_AJ_Desc", "Chinese": "快速的二连斩击，跃起的同时挑飞身前敌人。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "A quick two-strike attack, simultaneously jumping and picking up enemies in front of you. (<PERSON><PERSON><PERSON><PERSON>：The moment you hit an enemy,Use this skill)"}, {"Key": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_AJ_Desc", "Chinese": "向前突进的同时向前方挥砍双剑，并对沿途敌人造成伤害。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "Swing forward with both swords and deal damage to enemies along the way. (<PERSON><PERSON><PERSON><PERSON>：The moment you hit an enemy,Use this skill)"}, {"Key": "BladeDancer_DashAttackLeft_AJ_Desc", "Chinese": "向前突进的同时向左侧边面挥砍双剑，并对沿途敌人造成伤害。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "Swing both swords to the left while moving forward, dealing damage to enemies along the way. (<PERSON><PERSON><PERSON><PERSON>：The moment you hit an enemy,Use this skill)"}, {"Key": "BladeDancer_DashAttackRight_AJ_Desc", "Chinese": "向前突进的同时向右侧边面挥砍双剑，并对沿途敌人造成伤害。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "Swing both swords to the right while moving forward, dealing damage to enemies along the way. (<PERSON><PERSON><PERSON><PERSON>：The moment you hit an enemy,Use this skill)"}, {"Key": "BladeDancer_SwrodDanceDashComboAttack_AJ_Desc", "Chinese": "使用双剑带动身体旋转，边向前移动，边挥砍出缭乱的剑舞。（JustAttack:命中敌人的瞬间使用此技能。）", "English": "Use your swords to spin your body while moving forward, slashing nearby enemies in a dazzling sword dance. (JustAttack：The moment you hit an enemy,Use this skill)"}, {"________________________________________UI__": "__<PERSON><PERSON><PERSON>技能描述_________________________________________________"}, {"Key": "<PERSON><PERSON><PERSON>_SweapAttack1_Desc", "Chinese": "向身边敌人发动范围攻击，最多可以打出四段连击。 I", "English": "Launch a ranged attack at enemies around you; launch up to four combos."}, {"Key": "<PERSON><PERSON><PERSON>_AirDownSpikeAttack1_Desc", "Chinese": "滞空时使用此技能对下方发动突刺攻击，造成小范围伤害。", "English": "Use this skill while airborne to launch a burst attack, dealing damage to enemies below."}, {"Key": "<PERSON><PERSON><PERSON>_DashSpike_Desc", "Chinese": "向前方用力发动穿刺攻击。", "English": "Launch a forward piercing attack."}, {"Key": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown_Desc", "Chinese": "滞空时使用此技能向斜前方俯冲攻击。", "English": "Use this skill while airborne to dive and attack diagonally forward."}, {"Key": "<PERSON><PERSON><PERSON>_DashSweapSlash_Desc", "Chinese": "向前方突进一段距离并打出范围攻击。", "English": "Dash forward a short distance and land a ranged attack."}, {"Key": "<PERSON><PERSON><PERSON>_BackJumpSweapSlash_Desc", "Chinese": "向后方跳跃并对前方小范围内敌人造成范围伤害。", "English": "Jump backward and deal ranged damage to enemies in a small area in front of you."}, {"Key": "<PERSON><PERSON><PERSON>_AirDashSweapSlash_Desc", "Chinese": "滞空时向前方突进一段距离并打出范围攻击。", "English": "Dash forward a short distance and launch an ranged attack while airborne."}, {"____________________": "____________________LinkAction____________________"}, {"Key": "<PERSON><PERSON><PERSON>_SweapAttack2_Desc", "Chinese": "向身边敌人发动范围攻击，最多可以打出四段连击。", "English": "Launch a ranged attack at enemies around you; launch up to four combos."}, {"Key": "<PERSON><PERSON><PERSON>_SweapAttack3_Desc", "Chinese": "向身边敌人发动范围攻击，最多可以打出四段连击。", "English": "Launch a ranged attack at enemies around you; launch up to four combos."}, {"Key": "<PERSON><PERSON><PERSON>_SweapAttack4_Desc", "Chinese": "向身边敌人发动范围攻击，最多可以打出四段连击。", "English": "Launch a ranged attack at enemies around you; launch up to four combos."}, {"Key": "<PERSON><PERSON><PERSON>_DashSpike_Combo_Desc", "Chinese": "“突进穿刺”命中敌人时，挥舞长枪发动三段攻击。", "English": "When “Impaling Thrust” hits an enemy, swing your spear to launch a three-step attack."}, {"Key": "<PERSON><PERSON><PERSON>_RiseSlash_Combo_Desc", "Chinese": "“突进穿刺”命中敌人时，上挑长枪并跃入空中。", "English": "When “Impaling Thrust” hits an enemy, pick up your spear and leap into the air."}, {"Key": "<PERSON><PERSON><PERSON>_DashSpike_HitJump_Desc", "Chinese": "命中时可跃入空中，旋转自身的同时挥舞长枪造成伤害", "English": "Leap through the air and spin while swinging your spear to deal damage while airborne."}, {"Key": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2_Desc", "Chinese": "命中时可跃入空中，旋转自身的同时挥舞长枪造成伤害", "English": "Leap through the air and spin while swinging your spear to deal damage while airborne."}, {"Key": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash_Desc", "Chinese": "命中时向下劈砍并跃入空中", "English": "Slash downward on hit and leap into the air"}, {"________________________________________UI__": "__Swordsman技能描述________________________________________________"}, {"Key": "Swordsman_RiseSlash_Desc", "Chinese": "向上挥的同时跃向空中，可将部分敌人挑入空中同时自身进入滞空。", "English": "Jumping while slashing upwards picks up enemies and drags them into the air."}, {"Key": "Swordsman_RiseComboSlash_Desc", "Chinese": "先在地面挥砍一次，向上挑的同时向上跳跃，可将部分敌人挑入空中同时自身进入滞空。", "English": "Slashing once while grounded, then uppercutting drags enemies into the air for a short time."}, {"Key": "Swordsman_AirDownSlashAttack1_Desc", "Chinese": "滞空时向下方劈，造成伤害同时落地", "English": "Slash downward while airborne to deal damage and ground yourself."}, {"Key": "Swordsman_DashSlash_Desc", "Chinese": "向前冲锋一小段距离并进行横向的范围挥砍。", "English": "Charges forward a short distance and slashes horizontally."}, {"Key": "Swordsman_AirDashSting_Desc", "Chinese": "滞空时向前冲锋一小段距离并对地面敌人造成范围伤害并落地。", "English": "Charge forward a short distance while airborne and deal ranged damage to grounded enemies."}, {"Key": "Swordsman_Defense_Desc", "Chinese": "持盾进入防御姿态，能防御来自身前范围的攻击。", "English": "Holding the shield in a defensive stance defends against frontal attacks.（Just Block:The moment you be hitted,Use this skill）"}, {"Key": "Swordsman_AirDownShieldSmash_Desc", "Chinese": "滞空时，使用盾向下用力砸去，对地面小范围内的敌人造成范围伤害直到落地。", "English": "Use shield while airborne to smash downwards and deal ranged damage to a small area of grounded enemies."}, {"____________________": "____________________LinkAction____________________"}, {"Key": "Swordsman_DashSlash2_Desc", "Chinese": "进行突进斩时，再次使用，可再向前冲锋一小段距离并造成多端伤害。", "English": "When performing a dash, using it again charges forward a short distance and deals damage."}, {"Key": "Swordsman_Defense_Attack1_Desc", "Chinese": "防御姿态下，使用盾牌对身前敌人进行打击", "English": "In defensive stance, use the shield to strike enemies in front of you"}, {"Key": "Swordsman_Defense_Attack1_2_Desc", "Chinese": "进行盾牌连击Ⅰ时，在第一次盾击动画后的一定时间内再次按键造成追击。", "English": "When performing Shield Combo I, press again within a certain time after the first shield attack animation to deal a combo strike."}, {"Key": "Swordsman_Defense_Attack2_Desc", "Chinese": "防御姿态下，向前冲锋一小段距离并用盾牌对身前的敌人进行打击。", "English": "In defensive stance, charge forward a short distance and strike enemies in front of you with your shield."}, {"Key": "Swordsman_Defense_Attack3_Desc", "Chinese": "防御姿态下，向前冲锋一段距离同时打击敌人，松开键位会追加一次盾牌打击或剑刺击。", "English": "In defensive stance, charging forward for a short distance and Release button will add a shield strike or sword stinger."}, {"Key": "Swordsman_Defense_JustBlock_Desc", "Chinese": "在敌人攻击即将命中自身的瞬间举盾，可招架敌人的攻击并用盾牌对其造成多段伤害并击飞小体型目标。", "English": "Parry enemy attacks by raising your shield as they hit you, dealing damage to the target."}, {"________________________________________UI_Button__": "__按键名字___________________________________________________"}, {"Key": "Left", "Chinese": "左", "English": "Left"}, {"Key": "Right", "Chinese": "右", "English": "Right"}, {"Key": "Up", "Chinese": "上", "English": "Up"}, {"Key": "Down", "Chinese": "下", "English": "Down"}, {"Key": "UD", "Chinese": "上下", "English": "Up and Down"}, {"Key": "LR", "Chinese": "左右", "English": "Left and Right"}, {"Key": "LU", "Chinese": "左上", "English": "Upper Left"}, {"Key": "LD", "Chinese": "左下", "English": "Lower Left"}, {"Key": "LR", "Chinese": "左右", "English": "Left and Right"}, {"Key": "RU", "Chinese": "右上", "English": "Upper Right"}, {"Key": "RD", "Chinese": "右下", "English": "Lower Right"}, {"Key": "Cancel", "Chinese": "取消", "English": "Cancel"}, {"Key": "Back", "Chinese": "返回", "English": "Back"}, {"Key": "Confirm", "Chinese": "确定", "English": "Yes"}, {"Key": "Previous", "Chinese": "上一页", "English": "Previous page"}, {"Key": "Next", "Chinese": "下一页", "English": "Next page"}, {"Key": "UpAndDown", "Chinese": "上下选择", "English": "Up/Down"}, {"Key": "LeftAndRight", "Chinese": "左右选择", "English": "Left/Right"}, {"Key": "LeftAndRight_Change", "Chinese": "左右切换", "English": "Left/Right"}, {"Key": "UI_Selected", "Chinese": "选择", "English": "Select"}, {"Key": "UI_Change", "Chinese": "切换", "English": "Change"}, {"Key": "UI_Closs", "Chinese": "关闭", "English": "<PERSON><PERSON>"}, {"________________________________________UI_Weapon__": "__武器名字___________________________________________________"}, {"Key": "TestSpear", "Chinese": "觉醒者长枪", "English": "<PERSON><PERSON><PERSON><PERSON>"}, {"Key": "Iron_Spear", "Chinese": "铁质长枪", "English": "Iron Spear"}, {"Key": "Harpoon", "Chinese": "鱼叉枪", "English": "Harpoon Spear"}, {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Chinese": "仲裁长柄斧", "English": "Lawbringer‘s <PERSON><PERSON><PERSON>"}, {"Key": "<PERSON><PERSON>_<PERSON>", "Chinese": "精灵长枪", "English": "<PERSON><PERSON>"}, {"Key": "Golden_Trident", "Chinese": "潮汐刺枪", "English": "<PERSON><PERSON> Spear"}, {"Key": "Golden<PERSON><PERSON>_Spear", "Chinese": "戈鲁多拜神枪", "English": "<PERSON><PERSON><PERSON><PERSON> Divine Spear"}, {"Key": "<PERSON>_Halberd", "Chinese": "托丹禁卫军戟", "English": "Thodan Praetorian Halberd"}, {"Key": "DarkSpear", "Chinese": "黑尖枪", "English": "Black Tipped Spear"}, {"Key": "BigSword", "Chinese": "觉醒者大剑", "English": "Awakener Greatsword"}, {"Key": "Iron_GreatSword", "Chinese": "铁制双手剑", "English": "Iron Two-handed Sword"}, {"Key": "OldFashionedNoble_GreatSword", "Chinese": "旧式贵族大剑", "English": "Noble Greatsword"}, {"Key": "Elven_GreatSword", "Chinese": "精灵大剑", "English": "<PERSON>ven GreatSword"}, {"Key": "DarkKnight_GreatSword", "Chinese": "黑骑士大剑", "English": "Black Knight Greatsword"}, {"Key": "KingsKnight_GreatSword", "Chinese": "王之骑士大剑", "English": "King's Knight Greatsword"}, {"Key": "Legolas_Claymore", "Chinese": "莱格里斯骑士大剑", "English": "Lygriss Knight Greatsword"}, {"Key": "TestSword", "Chinese": "觉醒者之剑", "English": "Sword of the Awakener"}, {"Key": "Iron_Sword", "Chinese": "铁质直剑", "English": "Iron Straightsword"}, {"Key": "Goblin_<PERSON>ber", "Chinese": "哥布林钝刃", "English": "Goblin Blunt Blade"}, {"Key": "<PERSON><PERSON><PERSON>", "Chinese": "蜂刃", "English": "Bee Blade"}, {"Key": "Ceremony_Saber", "Chinese": "礼仪用剑", "English": "Ceremonial Sword"}, {"Key": "Skeleton_Sword", "Chinese": "骷髅直剑", "English": "Skeleton Straightsword"}, {"Key": "Golden_Sword", "Chinese": "黄金长剑", "English": "Golden Longsword"}, {"Key": "Legolas_Sword", "Chinese": "莱格里斯骑士剑", "English": "Lygriss Knight Sword"}, {"Key": "TestShield", "Chinese": "觉醒者之盾", "English": "Shield of the Awakener"}, {"Key": "Wooden_Shield", "Chinese": "木制筝形盾", "English": "Wooden Shield"}, {"Key": "Skeleton_Buckler", "Chinese": "骷髅圆盾", "English": "<PERSON>"}, {"Key": "Elven_Shield", "Chinese": "精灵筝形盾", "English": "Elven Shield"}, {"Key": "Golden_Shield", "Chinese": "戈鲁多风筝盾", "English": "Gruuldor Shield"}, {"Key": "Golden_LargeShield", "Chinese": "戈鲁多大盾", "English": "Gruuldor GreatShield"}, {"Key": "HolyTree_Shield", "Chinese": "圣树铁盾", "English": "Holy Tree Iron Shield"}, {"________________________________________UI_WeaponExplain__": "__武器描述_____________________________________________"}, {"Key": "TestSpearExplain", "Chinese": "觉醒者长枪描述（待填）", "English": "English"}, {"Key": "Iron_SpearExplain", "Chinese": "看上去十分朴实长枪，枪头由纯铁打造。枪杆轻盈，可以轻松刺穿常规的物体。", "English": "A simple spear made from pure iron. The blade is lightweight and can easily pierce through most regular objects."}, {"Key": "HarpoonExplain", "Chinese": "原本是为了捕鱼而专门设计的叉枪，靠自重就能贯穿部分生物的躯干。回乡的骑士为了应付日益泛滥的魔怪将这样的渔叉加以改造制成了现在的模样。", "English": "A piercing gun originally designed for fishing, it can penetrate the torso of some creatures by its own weight alone. Returning knights  remodeled their harpoons into its present form in order to cope with an increasing number of monsters."}, {"Key": "Soldier_HalberdExplain", "Chinese": "受命于国王而四处游历的审判官护卫们所用的武器，他们用手里的长柄斧执行仲裁的结果，彰显王权。", "English": "A weapon used by inquisitors who traveled under the command of the king, laying down the law with brutal force to display the king's power."}, {"Key": "<PERSON><PERSON>_<PERSON>pearExplain", "Chinese": "第二纪元的精灵们所使用的长枪，轻盈又致命，能够破除魔法。枪杆上的刻纹写满了对矮人族的不屑。", "English": "A spear used by the elves of the Second Age. Light and deadly, this weapon is capable of breaking through magic. The engraving on the spear speaks poorly of the dwarves."}, {"Key": "Golden_TridentExplain", "Chinese": "据说是仿造文献中的远古的武器而制作的刺枪，在过去被一些特定神明的信徒所当作圣物崇拜。他们相信这个世界的背后还隐藏着不为人知的真相，只有信奉古神才能逃过终焉之日。", "English": "It's said that the spear was made as a replica of an ancient weapon, and worshipped as a sacred object by believers of ancient gods. They believe that there is an unknown truth behind this world, and only by believing in the ancient gods can they escape the rapture."}, {"Key": "GoldenCross_SpearExplain", "Chinese": "这是一柄镶有黄金的仪式用长枪。因是仪式用品所以其锋利和坚固度远不如市面上其它品类，但在圣职人员手中却能发挥意想不到的潜力。据说坦尔的游民对黄金有非常高的崇拜，他们会将黄金镶嵌在宗教用品上以彰显对商神曼尼的崇拜。", "English": "A ceremonial weapon with a gold inlay. Because of it's ritualistic purpose  it is reasonably blunt, however in the hands of a cleric it can have unexpected potential. It is said that the nomads of Tanr worshiped gold to the extent of inlaying it into religious objects to show their worship to the god of commerce."}, {"Key": "Golden_HalberdExplain", "Chinese": "外形模仿了战争神托丹画像中所持握的长戟，通常有禁军骑士装备。禁军骑士们持握着这样的武器，诛杀一切对神明不敬的生物。", "English": "The halberd is modeled after one held in the portrait of <PERSON><PERSON>, the god of war, and is usually equipped by Forbidden Knights. These knights use such weapons to kill any creature that dares disrespect the gods."}, {"Key": "DarkSpearExplain", "Chinese": "传说中第一纪元的邪龙陨落之时，由自身脊骨所化之物。在坠龙谷被巨人所获并锻造成武器。随着时代变迁流转于各个种族的名匠之手。这柄武器的持有者很可能会被其中蕴含的邪能所侵蚀，唯有高位的觉醒者才能抵御这样的诱惑。", "English": "Legend has it that when the evil dragon of the First Age fell, this was made from its backbone. Forged into a weapon by a giant in the Valley of the Fallen Dragon, it has passed through the hands of famous craftsmen of various races. Only a high awakener can resist the temptation of the evil energy contained within this weapon."}, {"Key": "BigSwordExplain", "Chinese": "觉醒者大剑描述（待填）", "English": "English"}, {"Key": "Iron_GreatSwordExplain", "Chinese": "看上去十分朴实的剑，由纯铁打造。平衡感优秀，是随处可见的武器。", "English": "A simple sword, made of pure iron. This weapon can be found everywhere due to its simple yet reliable nature."}, {"Key": "OldFashionedNoble_GreatSwordExplain", "Chinese": "曾经的骑士们受封时由先民之王所赐予的战争用剑。比起当下高贵所好佩剑之复杂造型，那时贵族更注重佩剑在战场上的表现。每一名贵族都以用受赠这武器杀敌为荣。", "English": "A war sword given to knights by the King of the First People when they were ordained. Noblemen once paid more attention to the performance of the sword on the battlefield than their counterparts do nowadays. Every nobleman was proud to kill the enemy with the weapon he was given."}, {"Key": "Elven_GreatSwordExplain", "Chinese": "第二纪元的精灵们所使用的大剑，看似厚重实则轻盈，能够造成些许魔法伤害。整只剑都蕴含着当时精灵工匠们的智慧和技艺。", "English": "A greatsword used by the elves of the Second Era, the blade is lightweight and can cause magic damage. The sword contains the wisdom and skill of elven craftsmen of the time."}, {"Key": "DarkKnight_GreatSwordExplain", "Chinese": "漆黑之人所用的武器，黑骑士就如这种武器的造型一样给人带来不安。没人知道他们到底为谁或是为什么信念服务。至少在你获得这把武器之前，没有生还者有这个机会。", "English": "The Black Knights are as unsettling as the weapons they use. No one knows exactly who they serve or what beliefs they follow. Until you hold this weapon, no one will live long enough to find out either."}, {"Key": "KingsKnight_GreatSwordExplain", "Chinese": "被王所认可之人所有的大剑。其没有附加任何的额外力量却是骑士精神的最高体现。", "English": "A greatsword owned by a man praised by the king. It has no extra powers attached to it, only the highest expression of chivalry."}, {"Key": "Legolas_ClaymoreExplain", "Chinese": "莱格里斯远征骑士们所使用的大剑，其剑锋造型独特如同一张身份证明。据悉先民之王为了抵御恶魔侵袭曾派出了一支由不同地区不同种族的勇士组成的骑士团。骁勇善战的他们将祈福传递至国境中的每一处。然而就是这样一支劲旅还是迷失在了与恶魔的斗争中。", "English": "A greatsword used by the knights of Lygriss, the blade of which is uniquely shaped as a certificate of identity. It is said that the King of the First Men sent an order of knights composed of brave warriors from different regions and races to defend against demons, however such a strong army still lost against evil."}, {"Key": "TestSwordExplain", "Chinese": "觉醒者之剑描述（待填）", "English": "English"}, {"Key": "Iron_SwordExplain", "Chinese": "看上去十分朴实的直剑，由纯铁打造。持握非常舒适，能带给使用者一种安全感。", "English": "A simple straight sword, made of pure iron. It is comfortable to hold and gives the user a sense of security."}, {"Key": "Goblin_SaberExplain", "Chinese": "哥布林族所使用的兵刃，造型独特。锋利度也许不如人类打造的武器，但是其特殊造型和铁锈能造成意想不到的伤害。", "English": "Blades used by the kobolds are unique in shape. They may not be as sharp as human-made weapons, but its special shape and embedded rust can cause unexpected damage."}, {"Key": "Bee_StingExplain", "Chinese": "带有一定曲度的兵刃，据说由第二纪元的精灵族所常用，现在受人类贵族追捧。因为富有流线感的造型，蜂刃切割时的手感十分顺滑，没有一丝拖泥带水。", "English": "A blade with a unique curvature, said to be used by the elves of the Second Age, and now sought after by human nobles. Because of its streamlined shape, the <PERSON>'s blade cuts smoothly and cleanly."}, {"Key": "Ceremony_SaberExplain", "Chinese": "由人类贵族所佩戴的仪式用剑。剑刃锋利，手感轻盈。但对付盔甲略显颓势，在一些不着甲的决斗比武中常能看到它的身姿。", "English": "A ceremonial sword worn by human nobles, the blade is sharp and feels light in the hand. It is weak against armor, and can often be seen in armorless duels."}, {"Key": "Skeleton_SwordExplain", "Chinese": "充满了锈迹与斑驳剑刃，锻造工艺明显不属于这个时代。这柄武器就如其使用者本身，充满怨念。其剑锋上的缺牙也证明此武器久经沙场。", "English": "Mottled with rust, the forging process of this blade clearly predates this era. This weapon, like its user, is full of resentment. The missing teeth on its blade suggest that this weapon has been well-used."}, {"Key": "Golden_SwordExplain", "Chinese": "这是一柄镶有黄金的仪式用长剑。因是仪式用品所以其锋利和坚固度远不如市面上其它品类，但在信仰深厚之人手中却能发挥意想不到的潜力。据说坦尔的游民对黄金有非常高的崇拜，他们会将黄金镶嵌在宗教用品上以彰显对商神曼尼的崇拜。", "English": "A ceremonial longsword inlaid with gold. Because of its ritualistic nature it's reasonably blunt, however it has unexpected potential in the hands of those with deep beliefs.  It is said that the nomads of Tanr worshiped gold to the extent of inlaying it into religious objects to show their worship to the god of commerce."}, {"Key": "Legolas_SwordExplain", "Chinese": "莱格里斯远征骑士们所使用的长剑，其剑锋造型独特如同一张身份证明。据悉先民之王为了抵御恶魔侵袭曾派出了一支由不同地区不同种族的勇士组成的骑士团。骁勇善战的他们将祈福传递至国境中的每一处。然而就是这样一支劲旅还是迷失在了与恶魔的斗争中。", "English": "A longsword used by the knights of the Lygriss, it has a unique blade shape to easily reveal their identity. It is said that the King of the First Men sent an order of knights composed of brave warriors from different regions and races to defend against demons, however such a strong army still lost against evil."}, {"Key": "TestShieldExplain", "Chinese": "觉醒者之盾描述（待填）", "English": "English"}, {"Key": "Wooden_ShieldExplain", "Chinese": "看上去十分朴实的木遁，由木材打造。虽然结构简单但性能优秀。", "English": "Built from wood, its design is plain and unassuming. Despite its simple structure, the overall performance is excellent."}, {"Key": "Skeleton_BucklerExplain", "Chinese": "充满了锈迹的铁盾，锻造工艺明显不属于这个时代。这柄武器就如其使用者本身，充满怨念。其表面凹陷也证明此盾牌饱经风霜。", "English": "A rusty iron shield, the forging process clearly predates this era. This weapon, like its user, is full of resentment. Its many surface dents also prove that this shield has been well used."}, {"Key": "Elven_ShieldExplain", "Chinese": "第二纪元的精灵们所使用的盾牌，看似厚重实则轻便可靠，对魔法有着特殊的耐性。盾牌似乎还用了矮人的锻造技艺，似乎能够侧面证实精灵和矮人在第二纪元也有过一段时间的和平往来。", "English": "The shields used by the elves of the Second Age appear heavy, but they are light and reliable with a special resistance to magic. The shields also seem to use dwarven forging techniques, which confirms that elves and dwarves once had a peaceful relationship in the Second Age."}, {"Key": "Golden_ShieldExplain", "Chinese": "坦鲁的游民所使用的盾牌。其表面用黄金点缀，有较高的防御性，但会限制使用者的灵活性。盾牌上的金纹能够储存部分神力。据说坦尔的游民对黄金有非常高的崇拜，他们会将黄金镶嵌在宗教用品上以彰显对商神曼尼的崇拜。", "English": "A shield used by the nomads of Tanr. Its surface is embellished with gold, which is highly defensive but can limit the user's dexterity. The shield's gold pattern is capable of storing divine power.  It is said that the nomads of Tanr worshiped gold to the extent of inlaying it into religious objects to show their worship to the god of commerce."}, {"Key": "Golden_LargeShieldExplain", "Chinese": "坦鲁的游民所使用的盾牌。其表面用黄金点缀，能够对敌人进行圣属性的打击。据说坦尔的游民对黄金有非常高的崇拜，他们会将黄金镶嵌在宗教用品上以彰显对商神曼尼的崇拜。", "English": "A shield used by the nomads of Tanr. Its surface is embellished with gold and is capable of a holy attribute -blows to the enemy.  It is said that the nomads of Tanr worshiped gold to the extent of inlaying it into religious objects to show their worship to the god of commerce."}, {"Key": "HolyTree_ShieldExplain", "Chinese": "莱格里斯的远征骑士们所使用的盾牌，拥有被光明神祝福过的力量，能缓慢恢复佩戴者的生命力。据悉先民之王为了抵御恶魔侵袭曾派出了一支由不同地区不同种族的勇士组成的骑士团。骁勇善战的他们将祈福传递至国境中的每一处。然而就是这样一支劲旅还是迷失在了与恶魔的斗争中。", "English": "A shield used by the expeditionary knights of Lygriss. It has been blessed by the God of Light and can slowly restore the health of its wearer. It is said that the King of the First Men sent an order of knights composed of brave warriors from different regions and races to defend against demons, however such a strong army still lost against evil."}, {"________________________________________DefeatedUI__": "__胜利失败的UI___________________________________________________"}, {"Key": "GameOver_WinTitle", "Chinese": "目标达成", "English": "Mission Clear"}, {"Key": "GameOver_WinSub", "Chinese": "探险结束", "English": "End of Expedition"}, {"Key": "GameOver_LoseSub", "Chinese": "战败", "English": "Defeat"}, {"Key": "GameOver_WinButton", "Chinese": "返回城市", "English": "Return to Town"}, {"Key": "GameOver_LoseButton", "Chinese": "返回城市", "English": "Return to Town"}, {"________________________________________UI_EquipmentType__": "__装备类型名字______________________________________________"}, {"Key": "MainWeapon", "Chinese": "主武器", "English": "MainWeapon"}, {"Key": "ViceWeapon", "Chinese": "副武器", "English": "SubWeapon"}, {"Key": "Head<PERSON><PERSON><PERSON>", "Chinese": "头盔", "English": "<PERSON><PERSON><PERSON>"}, {"Key": "BodyArmor", "Chinese": "胸甲", "English": "Breastplate"}, {"Key": "HandArmor", "Chinese": "护手", "English": "Gauntlets"}, {"Key": "FootArmor", "Chinese": "护腿", "English": "<PERSON>ts"}, {"________________________________________MessageDialog__": "_对话框默认内容________________________________________________"}, {"Key": "MessageDialog_Yes", "Chinese": "确定", "English": "Yes"}, {"Key": "MessageDialog_No", "Chinese": "不", "English": "No"}, {"Key": "MessageDialog_Cancel", "Chinese": "取消", "English": "Cancel"}, {"Key": "PressAnyKeyToContinue", "Chinese": "按任意键继续", "English": "Press any key to continue"}, {"________________________________________UI_Equipment__": "__装备名字______________________________________________________"}, {"Key": "Test_Warrior_Male", "Chinese": "测试头盔", "English": "English"}, {"Key": "<PERSON><PERSON>", "Chinese": "羽毛头盔", "English": "English"}, {"Key": "Armor_Coats_Green", "Chinese": "易爆大氅", "English": "English"}, {"Key": "TestBra", "Chinese": "易爆外衣", "English": "English"}, {"Key": "WarriorGlove", "Chinese": "易爆腕轮", "English": "English"}, {"Key": "WarriorBoot", "Chinese": "易爆皮裤", "English": "English"}, {"Key": "Warrior<PERSON>_Helmet", "Chinese": "流浪骑士的羽饰头盔", "English": "Wandering Knight's Feather Helmet"}, {"Key": "Warrior01_Armor", "Chinese": "流浪骑士的胸铠", "English": "Wandering Knight's Breastplate"}, {"Key": "Warrior01_Glove", "Chinese": "北境骑兵手甲", "English": "Northern Cavalry Gauntlets"}, {"Key": "Warrior<PERSON>_Boot", "Chinese": "北境骑兵重靴", "English": "Northern Cavalry Heavy Boots"}, {"Key": "Warrior02_Helmet", "Chinese": "神殿精灵卫士的镶金重盔", "English": "Temple Elf Guard's Golden Helmet"}, {"Key": "Warrior02_Armor", "Chinese": "神殿精灵卫士的镶金胸铠", "English": "Temple Elf Guard's Golden Breastplate"}, {"Key": "Warrior02_Glove", "Chinese": "流浪骑士的臂铠", "English": "Wandering Knight's Gauntlets"}, {"Key": "Warrior02_Boot", "Chinese": "流浪骑士的腿铠", "English": "Wandering Knight's Legplates"}, {"Key": "Warrior03_Helmet", "Chinese": "原初头盔", "English": "Original Helmet"}, {"Key": "Warrior03_Armor", "Chinese": "冰魄收割者的爪痕", "English": "Ice Reaper's <PERSON><PERSON>"}, {"Key": "Warrior03_<PERSON>love", "Chinese": "原初护手", "English": "Original Gauntlets"}, {"Key": "Warrior03_Boot", "Chinese": "原初长靴", "English": "Original Boots"}, {"________________________________________UI_EquipmentExplain__": "__装备描述_______________________________________________"}, {"Key": "Test_Warrior_MaleExplain", "Chinese": "测试头盔描述（待填）", "English": "English"}, {"Key": "FeatherExplain", "Chinese": "羽毛头盔描述（待填）", "English": "English"}, {"Key": "Armor_Coats_GreenExplain", "Chinese": "易爆大氅描述（待填）", "English": "English"}, {"Key": "TestBraExplain", "Chinese": "易爆外衣描述（待填）", "English": "English"}, {"Key": "WarriorGloveExplain", "Chinese": "易爆腕轮描述（待填）", "English": "English"}, {"Key": "WarriorBootExplain", "Chinese": "易爆皮裤描述（待填）", "English": "English"}, {"Key": "Warrior01_HelmetExplain", "Chinese": "异乡的旅者所装备的无名头盔。斑驳的锈迹与可怖的伤痕也难以掩盖其精良的制作工艺。一些掌握古代技艺的工匠可以将其修缮如初。无名的甲胄恢复往昔荣光的同时，也将找回其本来的名字。", "English": "Nameless helmet worn by travelers from a foreign land. Mottled rust and deep scratches do little to conceal its fine craftsmanship. Perhaps a craftsman with the right skills could restore it to its original state. If the nameless armor is returned to its former glory, it may even remember its original name...."}, {"Key": "Warrior01_ArmorExplain", "Chinese": "异乡的旅者所装备的无名铠甲。斑驳的锈迹与可怖的伤痕也难以掩盖其精良的制作工艺。一些掌握古代技艺的工匠可以将其修缮如初。无名的甲胄恢复往昔荣光的同时，也将找回其本来的名字。", "English": "Nameless armor worn by travelers from a foreign land. Mottled rust and deep scratches do little to conceal its fine craftsmanship. Perhaps a craftsman with the right skills could restore it to its original state. If the nameless armor is returned to its former glory, it may even remember its original name...."}, {"Key": "Warrior01_GloveExplain", "Chinese": "北境骑士赠予旅行者的装甲，用以替换损坏的流浪骑士臂铠。盔甲上装饰的花纹彰显了其原主人不俗的身份。保暖的同时，具有不错的防护能力。在北境是一个不错的选择。", "English": "Armor given to the <PERSON><PERSON> by the Northern Cavalry to replace their damaged Wan<PERSON> Knight's Gauntlets. The armor's intricate design gives a good insight into its original owner. It proves not only warmth, but also good protection, an essential combination this far north."}, {"Key": "Warrior01_BootExplain", "Chinese": "北境骑士赠予旅行者的装甲，用以替换损坏的流浪骑士腿铠。盔甲上装饰的花纹彰显了其原主人不俗的身份。保暖的同时，具有不错的防护能力。在北境是一个不错的选择。", "English": "Armor given to the <PERSON><PERSON> by the Northern Cavalry to replace their damaged Wandering Knight's Boots. The armor's intricate design gives a good insight into its original owner. It proves not only warmth, but also good protection, an essential combination this far north."}, {"Key": "Warrior02_HelmetExplain", "Chinese": "神殿精灵卫士的镶金重盔", "English": ""}, {"Key": "Warrior02_ArmorExplain", "Chinese": "神殿精灵卫士的镶金胸铠", "English": ""}, {"Key": "Warrior02_GloveExplain", "Chinese": "受到某种神秘力量的影响，本应由铁匠修复才能装备的臂铠，出现在了地下迷宫的宝箱里。冒险者的旅途总是充满惊喜。", "English": "Consumed by a mysterious power, the gauntlets somehow appeared in a treasure chest in the underground labyrinth. An adventurer's journey is always full of surprises."}, {"Key": "Warrior02_BootExplain", "Chinese": "受到某种神秘力量的影响，本应由铁匠修复才能装备的腿铠，出现在了地下迷宫的宝箱里。冒险者的旅途总是充满惊喜。", "English": "Consumed by a mysterious power, the gauntlets somehow appeared in a treasure chest in the underground labyrinth. An adventurer's journey is always full of surprises."}, {"Key": "Warrior03_HelmetExplain", "Chinese": "原初头盔", "English": "English"}, {"Key": "Warrior03_ArmorExplain", "Chinese": "极寒位面的恶魔工匠以冰冷的邪火锻造的铠甲，如卡斯隆冰川一样坚固。闭上双眼感受到的不是甄郁与芬芳，而是饱受寒霜烈焰烧灼的灵魂所发出的痛苦哀嚎与绝望呐喊。", "English": "Armor forged in icefire by demon craftsmen of the Arctic Plane, as strong as the Kathlon Glacier. Close your eyes and feel not the lush and fragrant, but the wails and cries of souls who have been burned by frost and flame."}, {"Key": "Warrior03_GloveExplain", "Chinese": "原初护手", "English": "English"}, {"Key": "Warrior03_BootExplain", "Chinese": "原初长靴", "English": "English"}, {"________________________________________创建角色UI__": "________________________________________________________"}, {"Key": "CreateCha_Title", "Chinese": "创建新角色", "English": "Player Edit"}, {"Key": "CreateCha_Name", "Chinese": "姓名", "English": "Name"}, {"Key": "CreateCha_Race", "Chinese": "种族", "English": "Race"}, {"Key": "CreateCha_Gender", "Chinese": "性别", "English": "Gender"}, {"Key": "CreateCha_Class", "Chinese": "职业", "English": "Class"}, {"Key": "CreateCha_Voice", "Chinese": "声音", "English": "Voice"}, {"Key": "CreateCha_Appearance", "Chinese": "外貌", "English": "Appearance"}, {"Key": "CreateCha_Element", "Chinese": "元素", "English": "Element"}, {"Key": "CreateCha_Confirm", "Chinese": "确定", "English": "Confirm"}, {"Key": "CreateCha_RaceTitle", "Chinese": "种族", "English": "Race"}, {"Key": "CreateCha_Human", "Chinese": "人类", "English": "Human"}, {"Key": "CreateCha_Elf", "Chinese": "精灵", "English": "<PERSON><PERSON>"}, {"Key": "CreateCha_Dwarf", "Chinese": "矮人", "English": "<PERSON><PERSON><PERSON>"}, {"Key": "CreateCha_Male", "Chinese": "男", "English": "Male"}, {"Key": "CreateCha_Female", "Chinese": "女", "English": "Female"}, {"Key": "CreateCha_Warrior", "Chinese": "战士", "English": "Fighter"}, {"Key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chinese": "游侠", "English": "<PERSON>"}, {"Key": "CreateCha_Wizard", "Chinese": "法师", "English": "Mage"}, {"Key": "CreateCha_ConfirmDialog", "Chinese": "确定建立内容\n并开始游戏吗？", "English": "Ready to create character and start game?"}, {"________________________________________UI__": "__商店道具______________________________________________________"}, {"Key": "Name_TradingGoods1_0", "Chinese": "回复药", "English": "Potion"}, {"Key": "Name_TradingGoods1_1", "Chinese": "回复药(10)", "English": "Potion(10)"}, {"Key": "Name_TradingGoods1_2", "Chinese": "火球术卷轴", "English": "Fireball <PERSON>"}, {"Key": "Name_TradingGoods1_3", "Chinese": "火球术卷轴(3)", "English": "<PERSON><PERSON>(3)"}, {"Key": "Desc_TradingGoods1_0", "Chinese": "恢复30%生命值的药水。", "English": "Regenerates 30% health."}, {"Key": "Desc_TradingGoods1_1", "Chinese": "10瓶装，恢复30%生命值的药水。", "English": "A dozen potions."}, {"Key": "Desc_TradingGoods1_2", "Chinese": "可以释放火球魔法", "English": "Ability to cast fireball magic."}, {"________________________________________对话__": "__旅行商人______________________________________________________"}, {"Key": "VagabondageVendor_Name", "Chinese": "休·阿克曼", "English": "<PERSON>"}, {"Key": "VagabondageVendor_Speech2b1", "Chinese": "嘿朋友，听说是你协助了骑士们解放了洛迪安。", "English": "Hey friend, I heard you helped the knights to liberate <PERSON><PERSON>."}, {"Key": "VagabondageVendor_Speech2b2", "Chinese": "算是吧。", "English": "Sort of. "}, {"Key": "VagabondageVendor_Speech2b3", "Chinese": "来来来，我们这些老百姓都十分感谢你。这是送你的礼物。", "English": "Come on over! All of us, the townfolks, are grateful for what you did. Here's a present to you. "}, {"Key": "VagabondageVendor_Speech2b4", "Chinese": "有什么其它需要或是想喝点饮料尽管来找我，我这有洛笛安的特产，最可口的的沙士水。", "English": "If you're looking for something to drink, or any other things, just come to me. I have all the specialties of Rodian, and the most delicious Saci Water."}, {"________________________________________对话__": "__守卫2______________________________________________________"}, {"Key": "GuardGogaros_Name", "Chinese": "护卫冈加洛斯", "English": "Guard <PERSON><PERSON><PERSON>"}, {"________________________________________护卫冈加洛斯__": "__主线任务2b______________________________________________________"}, {"Key": "GuardGogaros_Speech2b1", "Chinese": "欧特兰德洛笛安分部开拓兵团骑士，冈加洛斯向你致敬。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2b2", "Chinese": "你好。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2b3", "Chinese": "多亏了你的协助，现在我们不用再担心这些鼠人从坑道里钻出来了。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2b4", "Chinese": "我们马上会在坑道内设立据点以方便之后的清剿行动。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2b5", "Chinese": "之前我们的侦察兵大致描述了坑道深处的样子。里面就像是一个地下世界，非常复杂。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2b6", "Chinese": "感谢你冒险者，如今总算可以让那些流离失所的百姓回家了。", "English": "英文翻译"}, {"________________________________________护卫冈加洛斯__": "__主线任务2c______________________________________________________"}, {"Key": "GuardGogaros_Speech2c1", "Chinese": "你不仅勇猛还富有智慧，洛迪安的人民和远征的士兵们都会感谢你的冒险者。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2c2", "Chinese": "如此一来，短期内就不会在遇到更多啮齿魔了。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2c3", "Chinese": "我们回去向指挥官禀告捷讯吧。", "English": "英文翻译"}, {"________________________________________护卫冈加洛斯__": "__主线任务2d______________________________________________________"}, {"Key": "GuardGogaros_Speech2d1", "Chinese": "像您这样训练有素又神勇无比的冒险者应该不常见吧，我以前和冒险者打过交道，他们没有一个有像您这样的身手。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech2d2", "Chinese": "我相信无论是国王陛下还是指挥官阁下都会很愿意支付您的佣金。", "English": "英文翻译"}, {"________________________________________护卫冈加洛斯__": "__主线任务4a______________________________________________________"}, {"Key": "GuardGogaros_Speech4a1", "Chinese": "我们也不明白为什么这些行事混乱的啮齿魔会团结在一起，它们竟然还能施展如此复杂的防御法术。", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech4a2", "Chinese": "你也认为这背后有别的什么东西在干涉对吗？只不过我一直受的教导就是在有充分的证据前不要妄下结论...", "English": "英文翻译"}, {"Key": "GuardGogaros_Speech4a3", "Chinese": "马上要开始进攻了，冒险者大人。听说指挥官要派你去破坏那座会放魔法的图腾。我们的希望就寄托在您身上了。愿您雷霆加身，烈阳永照。", "English": "英文翻译"}, {"________________________________________对话__": "__守卫1______________________________________________________"}, {"Key": "Guardian1_Name", "Chinese": "护卫莎娜", "English": "<PERSON>"}, {"________________________________________11.23__": "__旧对话______________________________________________________"}, {"Key": "Guardian1_Speech0", "Chinese": "你好，冒险者，我叫莎娜，是指挥官大人的贴身护卫。", "English": "Greetings adventurer, I am <PERSON><PERSON>, the captain's personal bodyguard."}, {"Key": "Guardian1_Speech1", "Chinese": "没有指挥官大人的指令，任何人都不得通过这座桥。", "English": "No one is allowed to cross this bridge without the permission of the captain."}, {"Key": "Guardian1_Speech2", "Chinese": "谨遵指挥官大人的命令。", "English": "Obey the commands of the captain."}, {"Key": "Guardian1_Speech3", "Chinese": "指挥官大人太辛苦了，他明明不用陪我们在这儿站岗……", "English": "The captain doesn't need to accompany us to stand guard here..."}, {"Key": "Guardian1_Speech4", "Chinese": "打气精神来！莎娜，现在可不能懈怠。", "English": "Cheer up, <PERSON><PERSON>! At least you can't slack off now."}, {"Key": "Guardian1_Speech5", "Chinese": "冈加洛斯那家伙没在打瞌睡吧……现在正是关键的时候。", "English": "That <PERSON><PERSON><PERSON> guy isn't dozing off, is he? ...... Now's the moment of truth."}, {"Key": "Guardian1_Speech6", "Chinese": "怪物们士气大减，或许我应该向指挥官大人提议乘胜追击？不……还是算了。", "English": "The monsters are demoralized, perhaps I should encourage the captain to finish them once and for all... no... forget it."}, {"Key": "Guardian1_Speech7", "Chinese": "最终，我们夺回了矿洞。", "English": "Eventually, we recaptured the mine."}, {"Key": "Guardian1_Speech8", "Chinese": "听说矿坑废弃前是人类的领地……但那时我还不是贴身护卫，过去……究竟发生了什么呢？", "English": "I heard that the mine belonged to humans before it was abandoned ...... but I wasn't a guard back then, so I wonder what truly happened."}, {"Key": "Guardian1_Speech9", "Chinese": "矿洞里到处都是啮齿魔，真是糟糕。但好在我们兵力充足，况且还有指挥官大人。", "English": "The mine was full of ratmen, it was terrible. But the good thing is that we have enough troops, and most importantly, the captain."}, {"Key": "Guardian1_Speech10", "Chinese": "能为指挥官大人效力是我的荣幸。", "English": "It is my honor to serve the captain."}, {"Key": "Guardian1_Speech11", "Chinese": "矿洞中的啮齿魔消停了不少，指挥官大人说是我们努力战斗的结果，但是我们仅仅是在外面巡逻……不，指挥官大人这么做一定有他的深意。", "English": "The ratmen in the mine have reduced in number. The captain said it's the result of our hard work, but we're only patrolling outside... No, the captain must have his reasons for saying so."}, {"Key": "Guardian1_Speech12", "Chinese": "冒险者，听说你最近在矿洞清理了不少啮齿魔，我开始对你有些刮目相看了。", "English": "Adventurer, I hear you've cleared out a lot of ratmen from the mines recently, and I must admit I'm a little impressed."}, {"Key": "Guardian1_Speech13", "Chinese": "啮齿魔已经被完全消灭了！感谢你，冒险者，这是人类的胜利。", "English": "The ratmen have been completely wiped out! Thank you, adventurer, this is a victory  for humanity."}, {"Key": "Guardian1_Speech14", "Chinese": "兽人、啮齿魔、巨魔、宝物……为什么一个废弃的矿洞会聚集这么多怪物和事情……不，我不该想这么多，指挥官大人说得对，我只要做好自己的工作就够了，就像我平时做的一样。", "English": "Orcs, ratmen, trolls, treasure ...... Why would an abandoned mine have so many monsters inside it...... No, I shouldn't think about it, the Captain is right, it's enough for me to just focus on my job, like I always do."}, {"Key": "Guardian1_Speech15", "Chinese": "前线巡逻的队员传来消息，矿洞里的兽人消失了不少，谢谢你，冒险者。指挥官大人一定很认可你的所作所为。", "English": "The front patrol sent word that many of the Orcs in the mines have disappeared, thank you, adventurer. The captain must surely approve of what you've done.说"}, {"Key": "Guardian1_Speech16", "Chinese": "“你的每一份努力在国王眼中无所遁形”，指挥官大人平时是这么鼓励我的。", "English": "'Every effort you make has nothing to hide in the eyes of the king'… that's what the captain says to encourage me."}, {"Key": "Guardian1_Speech17", "Chinese": "不，这并不是我们做的……这是你一个人的功劳。", "English": "No, it's not something we did ...... it's something you did alone."}, {"Key": "Guardian1_Speech18", "Chinese": "冈加洛斯昨天想冲进洞穴帮助你，我拦住了他……抱歉，我也觉得让你一个人对付这么多敌人很危险，但指令是指令……", "English": "<PERSON><PERSON><PERSON> tried to rush into the cave to help you yesterday and I stopped him ...... I'm sorry, I knew it was dangerous to leave you alone against so many enemies, but orders are orders......"}, {"Key": "Guardian1_Speech19", "Chinese": "矿洞里的兽人都被消灭得差不多了，谢谢你，冒险者，请接收来自我个人的谢意。", "English": "The Orcs in the mines are practically wiped out, all thanks to you, adventurer. Please accept my deepest gratitude."}, {"________________________________________对话__": "__指挥官______________________________________________________"}, {"Key": "Captain<PERSON>mith_Name", "Chinese": "指挥官德拉万", "English": "Commander <PERSON><PERSON>"}, {"________________________________________指挥官__": "__主线任务1b______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech1b1", "Chinese": "真不愧是飞龙杀手，三两下就消灭了这些雷霆蛋儿的鼠人。", "English": "No wonder you're called the Wyvern Slayer. So quick in killing these damned rats."}, {"Key": "Captain<PERSON><PERSON>_Speech1b2", "Chinese": "为什么单独找我？", "English": "Why did you come to me?"}, {"Key": "Captain<PERSON><PERSON>_Speech1b3", "Chinese": "我听闻过你在罗斯兰的事迹冒险者，或者我应该直接称你为觉醒者。", "English": "Adventurer, I heard about your stories in RossLand.Or shall I call you the Awakener."}, {"Key": "Captain<PERSON><PERSON>_Speech1b4", "Chinese": "...", "English": "..."}, {"Key": "Captain<PERSON><PERSON>_Speech1b5", "Chinese": "别紧张，我们都有一位共同的朋友。愿十二神和先民护佑你。", "English": "<PERSON>lax, we all have a mutal friend. May the twelve Gods and the ancestors protect you."}, {"Key": "Captain<PERSON><PERSON>_Speech1b6", "Chinese": "我知道你为什么要想办法来洛迪安，就顺便帮了你一把。", "English": "I know why you're trying to find a way to come to <PERSON><PERSON>. So I thought to help you out there. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b7", "Chinese": "我猜作为回报我要帮你干些什么。", "English": "I guess there's something you need me to do in return. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b8", "Chinese": "作为回报，你协助我们一起消灭坑道里的所有鼠人，我就把遗迹的情报告诉你。", "English": "In return, you need to help us kill all the ratmem in the tunnel, and I'll tell you about the ruins. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b9", "Chinese": "我想了解有关埃布尔矮人地下遗迹的事情", "English": "I want to understand about the Dwarven Underground Ruins of the Eber."}, {"Key": "Captain<PERSON><PERSON>_Speech1b10", "Chinese": "没问题，不过这需要你先去坑道里援助我的伙伴冈加洛斯和他的手下。", "English": "That's not an issue, but first, you need to go to the tunnel and help out my friend <PERSON><PERSON><PERSON> and his men."}, {"Key": "Captain<PERSON><PERSON>_Speech1b11", "Chinese": "为什么在这里奋战？", "English": "Why are you fighting here?"}, {"Key": "Captain<PERSON><PERSON>_Speech1b12", "Chinese": "冈加洛斯为了掩护其他士兵带着爆炸物，堵上那些操蛋的鼠人挖的近路，才把一部分鼠人引诱出来。", "English": "<PERSON><PERSON><PERSON> lured some of the ratmen out to cover for the other men while they took the explosives with them to block the shortcut those damned ratmen dug. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b13", "Chinese": "也不知道这帮家伙什么时候变得这么聪明了，令人生畏。", "English": "No one knew when these things became so smart, shocking. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b14", "Chinese": "这也是我指名道姓要找你来的原因，等你援助我的手下回来后便会明白了。", "English": "And that's why I specifically need you here. You'll understand everything once you helped my men getting back. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b15", "Chinese": "交给我吧", "English": "Leave that with me. "}, {"Key": "Captain<PERSON><PERSON>_Speech1b16", "Chinese": "好，那就赶紧帮助我的部下吧。", "English": "Great, then hurry and go help my men. "}, {"________________________________________指挥官__": "__主线任务2b______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech2b1", "Chinese": "辛苦了觉...我是说冒险者大人，现在我就让骑士团和村民们进驻这里了。", "English": "Thanks for everything Awa... I meant Adventurer. I'll let the knights and the villagers know about settling here. "}, {"________________________________________指挥官__": "__主线任务2c______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech2c1", "Chinese": "多亏有你和冈加洛斯炸掉的通道，为我们争取了大量的时间，那些流离失所的居民都陆陆续续回来了。", "English": "All thanks to you and <PERSON><PERSON><PERSON> bombing the tunnel, buying us a great deal of time. Now the residents are gradually returning. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c2", "Chinese": "放心吧，给阁下的奖赏并不会少的。", "English": "Don't worry about it, I won't miss out on any rewards."}, {"Key": "Captain<PERSON><PERSON>_Speech2c3", "Chinese": "该跟我说说遗迹的事情了", "English": "Now it's time to talk to me about the ruins"}, {"Key": "Captain<PERSON><PERSON>_Speech2c4", "Chinese": "你想知道遗迹的事，这要从上个纪元说起了。", "English": "Wanna know about the ruins? We need to start from the last century then. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c5", "Chinese": "那时候的洛迪安叫做洛衫安贡达，是最繁盛的矮人国度之一。精灵和矮人还在友好往来的时候，这里也是两族人聚集最多的城市。", "English": "Back then, Rodian was still called Rosan-Gundar, one of the most prosperous Dwarven kingdoms. It was also the city where the elves and the dwarves would gather when they still had a good relationship with each other. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c6", "Chinese": "当时的矮人便在整个路卡蒂亚大陆下建造了许多遗迹。", "English": "Dwarves back then built many ruins across the entire Lucadia."}, {"Key": "Captain<PERSON><PERSON>_Speech2c7", "Chinese": "目前结合种种迹象以及情报来看，洛笛安的矿场内肯定别有洞天。", "English": "Based on all the signs and information, it must be another world in the Rodian mine. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c8", "Chinese": "你要找的遗迹也正在其深处，而恰巧这一路上都是鼠人。俗话说得好，有恶魔的地方就有圣遗物。", "English": "The ruins you are looking for would be in the depths of it, and the ratmem just happen to block the road to it. Well, there's a saying that where they are demons, there must be an holy relic. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c9", "Chinese": "你放心吧，圣遗物对我等凡躯无用。我的志向就是为王国完全收复洛迪安，让这里恢复洛衫安贡达时期的繁荣。", "English": "Rest assured, holy relics have no use to us mortals. My goal is to recapture <PERSON><PERSON> for the kingdom and recover it back to the flourishing times of Rosan-Gundar."}, {"Key": "Captain<PERSON><PERSON>_Speech2c10", "Chinese": "为什么我有听到有关恶魔的传闻", "English": "Why am I hearing rumors about demons."}, {"Key": "Captain<PERSON><PERSON>_Speech2c11", "Chinese": "你没听错，就是恶魔。恶魔出现后，洛衫安贡达也是首批遭受打击的。也有传闻说洛衫安贡达是从内部被瓦解的。", "English": "That's right, demons. The <PERSON><PERSON><PERSON><PERSON><PERSON> was the first to hit by demons. But there were also rumors saying <PERSON><PERSON><PERSON><PERSON><PERSON> collapsed from the inside. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c12", "Chinese": "你难道不觉得这里的啮齿魔也太有纪律了吗？它们就好似一夜之间有了智慧一样。你看之前的战斗那些耗子知道要阻止冈加洛斯他们引爆炸药。", "English": "Don't you think the Rodent Demons here are way too disciplined? It's like they gained intelligence overnight. In the previous battle, those ratmen knew they should stop <PERSON><PERSON><PERSON> from setting off the explosives. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c13", "Chinese": "你知道的还挺多。", "English": "You seems to know a lot. "}, {"Key": "Captain<PERSON><PERSON>_Speech2c14", "Chinese": "祝你的理想早日实现。", "English": "I hope you can achieve your goals one day."}, {"Key": "Captain<PERSON><PERSON>_Speech2c15", "Chinese": "要实现这些离不开我们的紧密合作你说是吗。", "English": "Well I can't achieve it without our close collaboration, right?"}, {"Key": "Captain<PERSON><PERSON>_Speech2c16", "Chinese": "帮助你们有什么好处？", "English": "If I help you, what's in it for me?"}, {"Key": "Captain<PERSON><PERSON>_Speech2c17a", "Chinese": "你协助我们的行动，我代表国王给予你在洛迪安自由探索的权限。", "English": "Help us out and I can represent the King to grant you the right to explore <PERSON><PERSON> freely."}, {"Key": "Captain<PERSON><PERSON>_Speech2c17b", "Chinese": "得到的任何战利品都不需要赋税，划算吧。", "English": "No tax on any booty, isn't that a bargain?"}, {"Key": "Captain<PERSON><PERSON>_Speech2c18", "Chinese": "你们不是要设立据点吗，还需要我做什么？", "English": "Aren't you guys setting up the stronghold, is there anything else you need me to do?"}, {"Key": "Captain<PERSON><PERSON>_Speech2c19", "Chinese": "新的委托很快就会下达。在此之前，你可以在村里逛逛。没准有人会需要你的帮助的。比如那个矮人老铁匠。", "English": "New orders will be here soon. But before then, you can explore the village. Somebody else may need your help. Like that old dwarf blacksmith."}, {"Key": "Captain<PERSON><PERSON>_Speech2c20", "Chinese": "好，那我先四处走走", "English": "Ok, I'll have a walk around the place."}, {"________________________________________指挥官__": "__主线任务3a______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech3a1", "Chinese": "和村民们相处的怎么样？", "English": "How are you getting on with the villagers?"}, {"Key": "Captain<PERSON><PERSON>_Speech3a2", "Chinese": "至少我在他们眼里不是灭鼠专家。", "English": "At least they don't see me as some kind of rat exterminator. "}, {"Key": "Captain<PERSON><PERSON>_Speech3a3", "Chinese": "如果你要前往矿坑调查的话，可以顺便帮我个忙。", "English": "If you're heading to investigate the pit, there's something you can help me out with on the way. "}, {"Key": "Captain<PERSON><PERSON>_Speech3a4", "Chinese": "我的人手已经做好拿下坑道的准备，我们需要你为这次围剿行动先行一步削弱洞里那些风操的啮齿魔的战力。", "English": "My men are ready to take down the pit. We need you to weaken Rodent Demons' forces in the mine in preparation for the siege. "}, {"Key": "Captain<PERSON><PERSON>_Speech3a5", "Chinese": "那些啮齿魔以团体分散在各处，守在它们自己的营地边上。你只要去摧毁那些营地就行了。", "English": "Those Rodent Demons guard their camps in groups, but the groups are spread out. All you need to do is to destroy the camps."}, {"Key": "Captain<PERSON><PERSON>_Speech3a6", "Chinese": "交给我吧。", "English": "Leave that with me. "}, {"________________________________________指挥官__": "__主线任务3b______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech3b1", "Chinese": "冒险者，如果可以的话，还请你先一步将坑道中的离群啮齿魔给清理掉。", "English": "Adventurer, if you can, please clear those lone Rodent Demons in the tunnel."}, {"________________________________________指挥官__": "__主线任务3c______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech3c1", "Chinese": "任务完成的怎么样？", "English": "How the quest going?"}, {"Key": "Captain<PERSON><PERSON>_Speech3c2", "Chinese": "我摧毁了大部分他们的营地。", "English": "I destroyed most of their camps."}, {"Key": "Captain<PERSON><PERSON>_Speech3c3", "Chinese": "很好。有调查出什么吗？", "English": "Good. Did you find out anything?"}, {"Key": "Captain<PERSON><PERSON>_Speech3c4", "Chinese": "还没有，但我能感到更深处散发着混沌的魔力。", "English": "Not yet, but I can feel this chaotic mana coming from deeper inside. "}, {"Key": "Captain<PERSON><PERSON>_Speech3c5", "Chinese": "矿洞深处吗？那正好。我已经做了周密的作战计划准备一举歼灭残余的所有啮齿魔。你要是准备好了，就从进矿道后正对的升降梯下来，在坑道内与我们汇合吧。", "English": "The depths of the mine? Convenient. I've already made a thorough plan to exterminate the rest of the Rodent Demons. If you're ready, take the lift that's right opposite the tunnel and meet us in the pit. "}, {"________________________________________指挥官__": "__主线任务4a______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech4a1", "Chinese": "你好，冒险者大人。", "English": "Hello, Adventurer. "}, {"Key": "Captain<PERSON><PERSON>_Speech4a2", "Chinese": "遇到麻烦了？", "English": "Are you in trouble?"}, {"Key": "Captain<PERSON><PERSON>_Speech4a3", "Chinese": "如你所见，那些雷霆蛋儿的鼠人不知道从哪学的防御魔法。我们的炮弹都被屏障给弹开了。", "English": "As you can see, we don't know where those damned ratmen learned defense magic from. The barrier bounced off our bombs."}, {"Key": "Captain<PERSON><PERSON>_Speech4a4", "Chinese": "也许我能从边上的小路绕进去。", "English": "Maybe I can take a detour to get it. "}, {"Key": "Captain<PERSON><PERSON>_Speech4a5", "Chinese": "也只能麻烦你从边上的小路找机会进入敌人的营寨内部，破坏那座会放防御魔法的图腾。", "English": "Please, I think that's the only way for us to find a way into the fort and detroy that totem which is releasing all the defensive magic. "}, {"________________________________________指挥官__": "__主线任务4c______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech4c1", "Chinese": "进攻！宰了这些肮脏的东西！", "English": "Attack! Kill these dirty scums!"}, {"________________________________________指挥官__": "__主线任务4d______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech4d1", "Chinese": "十二神在上，没想到里面还有只巨魔！我会向国王陛下汇报你的勇猛，冒险者……我们很快会派增援占领并重建这个地方。", "English": "Good lords, I wasn't expecting an Ogre in here! I'll report to the King on your bravery, Adventurer... We'll send backups to take over and rebuild this place."}, {"Key": "Captain<PERSON><PERSON>_Speech4d2", "Chinese": "恢复成洛衫安贡达的样子？", "English": "And recover it to how <PERSON><PERSON><PERSON><PERSON><PERSON> was like?"}, {"Key": "Captain<PERSON><PERSON>_Speech4d3", "Chinese": "恢复成洛衫安贡达时期的繁荣。", "English": "Exactly, recovering it back to the flourishing times of Rosan-Gundar."}, {"________________________________________指挥官__": "__主线任务5a______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech5a1", "Chinese": "探索队在坑道里找到了一条通往外部的通道，也许是通往那山顶或是哪个山腰的吧。", "English": "The expedition found a tunnel leading to the outside, maybe that leads to the moutainside or the mountaintop. "}, {"Key": "Captain<PERSON><PERSON>_Speech5a2", "Chinese": "可惜他们刚探出头就受到了兽人的袭击，唯一幸存的家伙说还看到了一些牲畜的尸体，它们的伤口上覆盖着一层由魔法形成的冰霜。", "English": "Unfortuanately they were attacked by the Orcs as soon as they left the tunnel. The lucky guy who survived said they saw some animal corpses with frost formed from magic on their wounds."}, {"Key": "Captain<PERSON><PERSON>_Speech5a3", "Chinese": "据我的推测，你要调查的圣遗物或许就在那里，不过，那里似乎也盘踞着一些不太妙的东西。总之，愿月光赐福你，冒险者。", "English": "My guessing is that, the holy relic you're looking for could be there. But some not so good things seems to be around there too. But anyway, may the moonlight bless you, Adventurer."}, {"________________________________________指挥官__": "__主线任务5b______________________________________________________"}, {"Key": "Captain<PERSON><PERSON>_Speech5b1", "Chinese": "调查员们的尸体上附着一层魔法所致的霜？你所言当真吗冒险者？", "English": "The corpses of the investigators are covered by frost formed from magic? Are you sure about what you just said <PERSON><PERSON>?"}, {"Key": "Captain<PERSON><PERSON>_Speech5b2", "Chinese": "没想到这洛迪安周围尽是敌人，也尽是立功的机会…", "English": "I wasn't expecting so many enemies around <PERSON><PERSON>, but this could be my chance to make some contributions..."}, {"Key": "Captain<PERSON><PERSON>_Speech5b3", "Chinese": "咳咳，我刚刚说了什么吗？", "English": "Cough cough... Did I say something?"}, {"Key": "Captain<PERSON><PERSON>_Speech5b4", "Chinese": "感谢你带来的情报冒险者，这件事上也许我们能达成新的合作，你觉得呢？", "English": "Thank you for the information, <PERSON>r. Maybe we can collaborate again on this too, what do you think?"}, {"________________________________________对话__": "__铁匠______________________________________________________"}, {"Key": "MineTeamBlacksmith_Name", "Chinese": "派崔克·石锤", "English": "<PERSON>"}, {"________________________________________铁匠__": "__铁匠的烦恼a______________________________________________________"}, {"Key": "MineTeamBlacksmith_Speecha1", "Chinese": "什么人？原来是你啊。", "English": "Who is it? Oh, it's you."}, {"Key": "MineTeamBlacksmith_Speecha2", "Chinese": "你好。听指挥官说你有麻烦。", "English": "Hello. The Commander told me you could need some help."}, {"Key": "MineTeamBlacksmith_Speecha3", "Chinese": "我从那些士兵们那儿听说了你的战绩，没准你能帮我个小忙。", "English": "The soldiers told me about what you did, and I thought maybe you can help me out a little."}, {"Key": "MineTeamBlacksmith_Speecha4", "Chinese": "确定是小忙吗？...", "English": "Just a little?"}, {"Key": "MineTeamBlacksmith_Speecha5", "Chinese": "你要是能把我的锻造工具从那矿坑里找回来，我就能用它们来强化和制作更多的武器装备，而且会给你打折的，你看怎么样？", "English": "If you can retrieve the tools I use for forging from the pit, I'll be able to use them to enhance and make more weapons and equipments. AND I'll give a discount on them, how does that sound?"}, {"Key": "MineTeamBlacksmith_Speecha6", "Chinese": "我想想...", "English": "Let me think about it"}, {"Key": "MineTeamBlacksmith_Speecha7", "Chinese": "噢！伟大的冒险者，你就帮帮我这个孤零零的矮人老铁匠吧！", "English": "Oh please! The great Adventurer, please help out a lonely old blacksmith like me!"}, {"Key": "MineTeamBlacksmith_Speecha8", "Chinese": "好吧...我知道了...", "English": "Fine, I'll do it"}, {"________________________________________铁匠__": "__铁匠的烦恼d______________________________________________________"}, {"Key": "MineTeamBlacksmith_Speechd1", "Chinese": "谢谢你勇敢的冒险者。嗷！我可太想念你们了我的老朋友们！", "English": "Thank you my brave Adventurer. Oh! How I have missed you my old friends!"}, {"Key": "MineTeamBlacksmith_Speechd2", "Chinese": "如此一来老夫也不必闲着了。看看有什么能为你锻造的吧!", "English": "Now I don't need to sit around and stay idle. Let me see if there anything I can make for you"}, {"Key": "MineTeamBlacksmith_Selectiond1", "Chinese": "打造武器", "English": "Make weapon"}, {"Key": "MineTeamBlacksmith_Selectiond2", "Chinese": "贩卖装备", "English": "Sell equipment"}, {"Key": "MineTeamBlacksmith_Selectiond3", "Chinese": "对话", "English": "Talk"}, {"Key": "MineTeamBlacksmith_Selectiond4", "Chinese": "结束对话", "English": "Finish the talk"}, {"________________________________________对话__": "__幸存士兵______________________________________________________"}, {"________________________________________引导士兵__": "__主线任务1b______________________________________________________"}, {"Key": "SurviverGuard_Name", "Chinese": "幸存的士兵", "English": "GuideGuard*（未命名）"}, {"Key": "GuideGuard_Speech1b1", "Chinese": "你…你是伟大的波尔提克派来的勇者吗？", "English": "英文翻译"}, {"Key": "GuideGuard_Speech1b2", "Chinese": "啊，抱歉!十分感谢你的救命之恩!我从未见过身手如此强悍的战士，是我失礼了。", "English": "英文翻译"}, {"Key": "GuideGuard_Speech1b3", "Chinese": "也许…也许我还没从恶战中缓过来…呵呵…我身上都是血…都是血…", "English": "英文翻译"}, {"________________________________________对话__": "__巡逻士兵1______________________________________________________"}, {"________________________________________引导士兵__": "__主线任务4a______________________________________________________"}, {"Key": "PatrolGuard1_Name", "Chinese": "巡逻的士兵", "English": "PatrolGuard*（未命名）"}, {"Key": "PatrolGuard1_Speech4a1", "Chinese": "据我爷爷的爷爷说，这里曾经是先民的国土。", "English": "英文翻译"}, {"Key": "PatrolGuard1_Speech4a2", "Chinese": "那时候精灵和矮人和平共存，一切都朝着最美好的愿景发展着。", "English": "英文翻译"}, {"Key": "PatrolGuard1_Speech4a3", "Chinese": "直到哀殇之地的出现，恶魔的到来让星球陷入了混沌，持续至今。", "English": "英文翻译"}, {"Key": "PatrolGuard1_Speech4a4", "Chinese": "我猜那个矿坑的深层就是矮人们挖掘的，里面也许还留下了不少那个时代的好东西吧。", "English": "英文翻译"}, {"________________________________________对话__": "__巡逻士兵2______________________________________________________"}, {"________________________________________引导士兵__": "__主线任务4a______________________________________________________"}, {"Key": "PatrolGuard2_Name", "Chinese": "巡逻的士兵", "English": "PatrolGuard*（未命名）"}, {"Key": "PatrolGuard2_Speech4a1", "Chinese": "听说你也会加入骑士团围剿鼠人巢穴的行动？", "English": "英文翻译"}, {"Key": "PatrolGuard2_Speech4a2", "Chinese": "之前的作战我们都被你的身手和战绩锁鼓舞了，这次作战有你在的话我们这些士兵也会更加勇敢。", "English": "英文翻译"}, {"Key": "PatrolGuard2_Speech4a3", "Chinese": "愿雷霆之威加护于你，冒险者。", "English": "英文翻译"}, {"________________________________________对话__": "__巡逻士兵3______________________________________________________"}, {"________________________________________引导士兵__": "__主线任务4a______________________________________________________"}, {"Key": "PatrolGuard3_Name", "Chinese": "巡逻的士兵", "English": "PatrolGuard*（未命名）"}, {"Key": "PatrolGuard3_Speech4a1", "Chinese": "你好冒险者，回到洛迪安的村民们都很感激你的帮助。", "English": "英文翻译"}, {"Key": "PatrolGuard3_Speech4a2", "Chinese": "这些鼠人从坑道理涌出来前，他们和他们的祖先世代都生活在这里供奉着塔泽海姆。", "English": "英文翻译"}, {"Key": "PatrolGuard3_Speech4a3", "Chinese": "那时王国各处的金属也大多都源自洛迪安。", "English": "英文翻译"}, {"Key": "PatrolGuard3_Speech4a4", "Chinese": "你看，我这身装备就是用这里开采出的金属锻造的。", "English": "英文翻译"}, {"Key": "PatrolGuard3_Speech4a5", "Chinese": "无论如何我们都要收复洛迪安!", "English": "英文翻译"}, {"________________________________________对话__": "__场景可调查物品交互信息______________________________________________________"}, {"________________________________________场景可调查物品交互信息__": "__主线任务1a______________________________________________________"}, {"Key": "EnvironmentItemInfo_DeadWereRat", "Chinese": "啮齿魔的尸体", "English": "DeadWereRat"}, {"Key": "DeadWereRat_Info0", "Chinese": "喜欢扎堆生活在黑暗中的魔物，难得看到它们如此有规模的来到地表。个头还比书里记载的大很多。", "English": "英文翻译"}, {"Key": "EnvironmentItemInfo_WereRatWeapon", "Chinese": "啮齿魔的武器", "English": "DeadWereRat"}, {"Key": "WereRatWeapon_Info0", "Chinese": "这种造型的武器，看来有一些年头了。没准都不是这个纪元的。", "English": "英文翻译"}, {"________________________________________场景可调查物品交互信息__": "__主线任务5a______________________________________________________"}, {"Key": "EnvironmentItemInfo_DeadPeople", "Chinese": "队员的尸体", "English": "DeadWereRat"}, {"Key": "DeadPeople_Info0", "Chinese": "伤口上与那些牲畜一样，覆盖着一层由魔法形成的冰霜。", "English": "英文翻译"}, {"________________________________________装备词条(假的)__": "__装备词条(假的)______________________________________________________"}, {"Key": "Wererat_Killer_3", "Chinese": "对啮齿魔伤害增加3%", "English": "Damage dealt to the Rodent Demons increase 3%"}, {"Key": "<PERSON>rat_Killer_5", "Chinese": "对啮齿魔伤害增加5%", "English": "Damage dealt to the Rodent Demons increase 5%"}, {"Key": "Orc_Killer_3", "Chinese": "对兽人伤害增加3%", "English": "Damage dealt to the Orc increase 3%"}, {"Key": "Orc_Killer_5", "Chinese": "对兽人伤害增加5%", "English": "Damage dealt to the Orc increase 5%"}, {"Key": "BigMonster_Killer_3", "Chinese": "对大型魔物伤害增加3%", "English": "Damage dealt to the Monster increase 3%"}, {"Key": "BigMonster_Killer_5", "Chinese": "对大型魔物伤害增加5%", "English": "Damage dealt to the Monster increase 5%"}, {"Key": "Defender_5", "Chinese": "防御力增加5%", "English": "Defense increase 5%"}, {"Key": "Runner_5", "Chinese": "精力消耗减少5%", "English": "Energy consumption decrease 5%"}, {"Key": "Mage_5", "Chinese": "魔力消耗减少5%", "English": "Mana consumption decrease 5%"}, {"Key": "Lucky_7", "Chinese": "幸运+7", "English": "Luck +7"}, {"Key": "Agile_7", "Chinese": "敏捷+7", "English": "Agility +7"}, {"Key": "Resilience_7", "Chinese": "韧性+7", "English": "Tenacity +7"}, {"Key": "Energy_7", "Chinese": "精力+7", "English": "Energy +7"}, {"Key": "FireResistance_50", "Chinese": "火焰抗性+50", "English": "Fire Resistance +50"}, {"Key": "IceResistance_50", "Chinese": "冰冻抗性+50", "English": "Freeze Resistance +50"}, {"Key": "ToxicResistance_50", "Chinese": "毒抗性+50", "English": "Poison Resistance +50"}, {"Key": "LuoXianG_Killer_1", "Chinese": "对伟大的罗贤刚同志伤害增加100%(TEST)", "English": "英文翻译"}, {"________________________________________背景字幕__": "__主线任务1a______________________________________________________"}, {"Key": "AdjutantGuard_Name", "Chinese": "副官", "English": "Adjutant "}, {"Key": "Player_Name", "Chinese": "玩家", "English": "Player"}, {"Key": "BackGroundText_MainQuest1a1", "Chinese": "冒险者，再往前，就是洛迪安了。", "English": "Adventurer, a little further, and we'll arrive at Rodian."}, {"Key": "BackGroundText_MainQuest1a2", "Chinese": "听上去，战斗已经打响了", "English": "It sounds like the battle had started already."}, {"Key": "BackGroundText_MainQuest1a3", "Chinese": "我们得抓紧了冒险者。", "English": "We have to hurry."}, {"Key": "BackGroundText_MainQuest1a4", "Chinese": "送这些该死的耗子们下九狱吧！", "English": "Let's send these goddamn rats into hell!"}, {"Key": "BackGroundText_MainQuest1a5", "Chinese": "为了洛迪安，为了欧特兰德！", "English": "For Rod<PERSON>, for Oatland!"}, {"Key": "BackGroundText_MainQuest1a6", "Chinese": "冒险者，你要当心这些魔物，它们向后拉开距离很可能是要偷袭了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1a7", "Chinese": "你可以尝试将它们挑飞在空中逐个击破。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1a8", "Chinese": "沙滩上的鼠人都被消灭了，不知道指挥官他们那边战况如何。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1a9", "Chinese": "走吧...", "English": "英文翻译"}, {"________________________________________背景字幕__": "__主线任务1b______________________________________________________"}, {"Key": "BackGroundText_MainQuest1b1", "Chinese": "保持队形！", "English": "Maintain the formation!"}, {"Key": "BackGroundText_MainQuest1b2", "Chinese": "铭记你们的训练士兵们！", "English": "Remember the training you did, soldiers!"}, {"Key": "BackGroundText_MainQuest1b3", "Chinese": "送那些低能的耗子下九狱！", "English": "And send those imbeciles into hell!"}, {"Key": "BackGroundText_MainQuest1b4", "Chinese": "达里安，这位就是最近在都城大名鼎鼎的飞龙杀手吗？", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1b5", "Chinese": "没想到我已经有“名字”了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1b6", "Chinese": "正好让我的伙计们看看你的本事。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1b7", "Chinese": "连飞龙都能一个人对付，这些啮齿魔相信也不在话下。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest1b8", "Chinese": "我之前可没听说啮齿魔会舞刀弄枪...用的还是上个纪元的家伙...", "English": "英文翻译"}, {"________________________________________背景字幕__": "__主线任务2a______________________________________________________"}, {"Key": "ManVoice_Name1", "Chinese": "机灵的欧特兰德士兵", "English": "Clever Oatland Soldier"}, {"Key": "ManVoice_Name2", "Chinese": "冷静的的欧特兰德士兵", "English": "Calm Oatland Soldier"}, {"Key": "ManVoice_Name3", "Chinese": "胆怯的欧特兰德士兵", "English": "Frightened Oatland Solider"}, {"Key": "BackGroundText_MainQuest2a1", "Chinese": "这些脏东西，源源不断的爬出来，简直没完没了。", "English": "I've had enough of these dirtbags. There's so many of them it seem like it's never ending."}, {"Key": "BackGroundText_MainQuest2a2", "Chinese": "得想办法把引线装上炸掉洞孔，等它们全爬上来就来不及了。", "English": "We need to think of a way to put the fuse in place and bomb the entrance. Otherwise it'll be too late once they're all out."}, {"Key": "BackGroundText_MainQuest2a3", "Chinese": "可，可这些鼠人似乎知道我们的意图。", "English": "Bu-but they seem like they know what we're up to."}, {"Key": "BackGroundText_MainQuest2a4", "Chinese": "是指挥官说的帮手来了，兄弟们坚持住！", "English": "It's the helper Commander told us about, hang in there guys!"}, {"Key": "BackGroundText_MainQuest2a5", "Chinese": "鼠人交给我们，你先想办法炸掉我们布置的炸药桶吧。", "English": "Leave the ratmem with us, why don't you go and find a way to bomb the explosives we set up."}, {"________________________________________背景字幕__": "__主线任务3b______________________________________________________"}, {"Key": "BackGroundText_MainQuest3b1", "Chinese": "这下面还真够大的...我得记好路标，不然一会迷路就麻烦了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b2", "Chinese": "有意思，鼠人居然已经发展出自己的文明了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b3", "Chinese": "图腾崇拜，这图腾上绘的标志好像在哪里见过...鼠人背后一定还有别的智慧生物引导...", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b4", "Chinese": "这就是铁匠要找的工具，看起来十分古老。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b5", "Chinese": "好了，这个据点清理完毕。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b6", "Chinese": "再来多少次结果都一样。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest3b7", "Chinese": "据点都清理完了，该回地表了。", "English": "英文翻译"}, {"________________________________________背景字幕__": "__主线任务4c______________________________________________________"}, {"Key": "EveryOne", "Chinese": "众士兵", "English": "Soldiers"}, {"Key": "BackGroundText_MainQuest4c1", "Chinese": "这里，冒险者，走这条路！", "English": "Over here <PERSON><PERSON>, go this way!"}, {"Key": "BackGroundText_MainQuest4c2", "Chinese": "祝你好运朋友。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c3", "Chinese": "所以鼠人到底为什么要在自家城寨门口修塔楼呢...", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c4", "Chinese": "总算是清理干净了，这里有条路或许可以尝试一下。...", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c5", "Chinese": "这条路也许能直接进入城寨内。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c6", "Chinese": "看起来只要解决这些鼠人萨满就可以破坏图腾的法术了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c7", "Chinese": "防护罩失效了，就是现在！", "English": "Their shield lost its effect! Now!"}, {"Key": "BackGroundText_MainQuest4c8", "Chinese": "大门被炸开了！", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c9", "Chinese": "我们成功了。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest4c10", "Chinese": "干得漂亮！全军听令，进攻！", "English": "Nice work! All listen up, ATTACK!"}, {"Key": "BackGroundText_MainQuest4c11", "Chinese": "胜利属于我们！胜利属于欧特兰德！", "English": "The victory belongs to us! The victory belongs to Oatland!"}, {"________________________________________背景字幕__": "__主线任务5b______________________________________________________"}, {"Key": "DeadAnimal", "Chinese": "冰原狼的尸体", "English": "<PERSON><PERSON><PERSON><PERSON>'s corpse"}, {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Chinese": "哥布林的尸体", "English": "Goblin's corpse"}, {"Key": "BackGroundText_MainQuest5b1", "Chinese": "明明通向室外，空气却要比矿坑里更稀薄。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest5b2", "Chinese": "瘴气？看来这里的灵泉涌动很不正常。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest5b3", "Chinese": "(冰原狼的尸体...从伤口的形状来看，不像是那帮鼠人能造成的。）", "English": "(<PERSON><PERSON><PERSON><PERSON>'s corpse... From the shape of the wound, it doesn't look like it's caused by those ratmen.)"}, {"Key": "BackGroundText_MainQuest5b4", "Chinese": "(是更加锐利的武器...更凶悍的生物)", "English": "(It's caused by sharper weapons... Something a lot fiercer)"}, {"Key": "BackGroundText_MainQuest5b5", "Chinese": "（一只哥布林的尸体？冰锥直接刺进了内脏...）", "English": "(A goblin's corpse? The icicle pierced right through the organs...)"}, {"Key": "BackGroundText_MainQuest5b6", "Chinese": "（尸体内部还留有混沌的力量...）", "English": "(Chaotic powers still remain inside the corpse...)"}, {"Key": "BackGroundText_MainQuest5b7", "Chinese": "（或许是我要找的家伙。）", "English": "(Something I'm looking for may be right ahead)"}, {"Key": "BackGroundText_MainQuest5b8", "Chinese": "兽人？我怎么一点也不意外呢。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest5b9", "Chinese": "那些鼠人的图腾文化原来是跟你们学的。", "English": "英文翻译"}, {"Key": "BackGroundText_MainQuest5b10", "Chinese": "你们背后的主子又是什么东西呢？", "English": "英文翻译"}, {"________________________________________背景字幕__": "__主线任务5c恶魔出场______________________________________________________"}, {"Key": "<PERSON><PERSON>emon", "Chinese": "冰之恶魔", "English": "Demon of Ice"}, {"Key": "BackGroundText_MainQuest5c1", "Chinese": "我能闻得到你...觉醒者...", "English": "I can smell you... The Awakener..."}, {"________________________________________气泡__": "__气泡内文字1b______________________________________________________"}, {"Key": "EveryOne", "Chinese": "众士兵", "English": "Soldiers"}, {"Key": "BubbleText_MainQuest1b1", "Chinese": "去死吧魔物！", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest1b2", "Chinese": "你们的脏抓子休想碰到我！", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest1b3", "Chinese": "还敢在那活蹦乱跳？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest1b4", "Chinese": "去九狱见你的鼠子鼠孙吧，贱种。", "English": "英文翻译"}, {"________________________________________气泡__": "__气泡内文字2a______________________________________________________"}, {"Key": "BubbleText_MainQuest2a1", "Chinese": "十二神啊，谁去把那些炸药引爆了。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2a2", "Chinese": "雷霆蛋儿，没完没了。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2a3", "Chinese": "救救我，我还想回老家结婚。", "English": "英文翻译"}, {"________________________________________气泡__": "__炸掉洞穴并清理完鼠人和刚加洛斯对话 2b_______________________________________"}, {"Key": "BubbleText_MainQuest2b1", "Chinese": "你…你是伟大的波尔提克派来的勇者吗？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b2", "Chinese": "啊，抱歉!十分感谢你的救命之恩!", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b3", "Chinese": "我从未见过身手如此强悍的战士，是我失礼了。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b4", "Chinese": "也许…也许我还没从恶战中缓过来…", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b5", "Chinese": "呵呵…我身上都是血…都是血…", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b6", "Chinese": "据我爷爷的爷爷说这里曾经是先民的国土。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b7", "Chinese": "那时候精灵和矮人和平共存，一切都朝着最美好的愿景发展着直到哀殇之地的出现，恶魔的到来让陆地陷入了混沌，持续至今。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest2b8", "Chinese": "我猜那个矿坑的深层就是矮人们挖掘的，里面也许还留下了不少那个时代的好东西吧。", "English": "英文翻译"}, {"________________________________________气泡__": "__4a______________________________________"}, {"Key": "Soldier1", "Chinese": "士兵1", "English": "英文翻译"}, {"Key": "Soldier2", "Chinese": "士兵2", "English": "英文翻译"}, {"Key": "Soldier3", "Chinese": "士兵3", "English": "英文翻译"}, {"Key": "Soldier4", "Chinese": "士兵4", "English": "英文翻译"}, {"Key": "Soldier5", "Chinese": "士兵5", "English": "英文翻译"}, {"Key": "Soldier6", "Chinese": "士兵6", "English": "英文翻译"}, {"Key": "Soldier7", "Chinese": "士兵7", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a1", "Chinese": "这地方可真阴冷...", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a2", "Chinese": "不然呢，你以为这是跳蚤街的妓院给你找温暖的吗？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a3", "Chinese": "我刚刚在上面找了个神婆算了一卦。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a4", "Chinese": "结果怎么样？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a5", "Chinese": "第一次翻出来七个，第二次翻出来十一个。神婆说了众神这次与我们同在。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a6", "Chinese": "是吗？那一会我去看看她能翻出什么新花样。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a7", "Chinese": "你好，无名的冒险者。刚才在沙滩上你的表现我们都看到了，如果这里有美酒的话我和兄弟们一定会敬你一杯。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a8", "Chinese": "谢谢你在矿坑里救了我，要是没有你我可能再也没法回去见我的爱人了。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a9", "Chinese": "都说觉醒者不一定能成为勇士，不过在我看来你是堂堂正正的英雄。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a10", "Chinese": "听说了吗，那些鼠人不仅会魔法还会用那种很复杂的防御术式。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a11", "Chinese": "是啊，学院里不是说啮齿魔这类的生物大多都智力低下么，难道它们是在暗域待的太久变异了？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a12", "Chinese": "我听我的祖母说恶魔在干涉着整个世界，你说这些鼠人背后会不会是一只恶魔呢？", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a13", "Chinese": "放屁吧，恶魔都绝迹多久了。你奶奶还说你生儿子没屁眼儿呢。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a14", "Chinese": "奶奶个腿儿，你想皮痒了是吧！", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a15", "Chinese": "雷霆的蛋...", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a16", "Chinese": "我才不想去推炮车呢，谁推谁被射成渣。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a17", "Chinese": "是啊，到时候你奶奶都认不出你来了。", "English": "英文翻译"}, {"Key": "BubbleText_MainQuest4a18", "Chinese": "风操的，下次洗澡小心你的菊花！", "English": "英文翻译"}, {"________________________________________Loading Tips__": "__加载时的小提示______________________________________"}, {"key": "LoadingTipsTitle1", "Chinese": "欧特兰德王国 · 其一", "English": "The Kingdom of Oatland • I"}, {"key": "LoadingTipsTitle2", "Chinese": "欧特兰德王国 · 其二", "English": "The Kingdom of Oatland • II"}, {"key": "LoadingTipsTitle3", "Chinese": "欧特兰德王国 · 其三", "English": "The Kingdom of Oatland • III"}, {"key": "LoadingTipsTitle4", "Chinese": "密室", "English": "Secret Chamber"}, {"key": "LoadingTipsTitle5", "Chinese": "龙脊骑士团 · 其一", "English": "The Knights of Dragon Spine • I"}, {"key": "LoadingTipsTitle6", "Chinese": "龙脊骑士团 · 其二", "English": "The Knights of Dragon Spine • II"}, {"key": "LoadingTipsTitle7", "Chinese": "洛迪安", "English": "<PERSON><PERSON>"}, {"key": "LoadingTipsTitle8", "Chinese": "魔晶簇", "English": "Magical Crystal Cluster"}, {"key": "LoadingTipsTitle9", "Chinese": "啮齿魔", "English": "<PERSON><PERSON>"}, {"key": "LoadingTipsTitle10", "Chinese": "兽奴", "English": "Beast Slave"}, {"key": "LoadingTipsTitle11", "Chinese": "攻击派生", "English": "Attack derivations"}, {"key": "LoadingTipsContent1", "Chinese": "欧特兰德王国在卢卡迪亚以西的平原和丘陵地带接壤之处、是这片大陆最主要的国家之一。", "English": "The Oatland Kingdom is between the plains and the hills East of Lucadia. It's one of the leading countries on this land."}, {"key": "LoadingTipsContent2", "Chinese": "欧特兰德王国分成中心、内部和外部三个地区。越接近中心则越繁荣。", "English": "The Oatland Kingdom is split into three areas, the outer, inner, and center. The closer to the center, the more prosperous it is."}, {"key": "LoadingTipsContent3", "Chinese": "欧特兰德王国盛产棉花和小麦，捕鱼业也发展的十分繁荣。欧特兰德王国和隆德塞恩大公国也有着十分密切的贸易。", "English": "The Oatland Kingdom is known for its cotton and wheat, fishing is also a thriving industry here. The Oatland Kingdom has a close trading relationship with the Dukedom of Londsain."}, {"key": "LoadingTipsContent4", "Chinese": "在地图中充满了隐藏点空间可供探索，进入这些场所的方式各不相同。", "English": "The map is filled with many hidden spaces you can explore, and the ways to enter these places varies."}, {"key": "LoadingTipsContent5", "Chinese": "骑士团受欧特兰德国王旨意前往洛迪安收复被魔物侵占的洛迪安镇。", "English": "The knights are ordered by the King to recapture Rodian, which is the town that was invaded and occupied by the demons."}, {"key": "LoadingTipsContent6", "Chinese": "骑士团团长汉斯·休奈特的家族曾是猎龙氏族，故此骑士团也被称为龙脊骑士团。", "English": "The principal knight, <PERSON>, and their family used to be dragon hunters, hence why the knights are also known as The Knights of Dragon Spine."}, {"key": "LoadingTipsContent7", "Chinese": "洛迪安镇在沦陷前是个隶属于欧特兰德王国的矿业小镇。据说在上一个纪元这里属于矮人，并且是与精灵族贸易的先头站。", "English": "Rodian belonged to the Oatland before it was occupied, they were known as the town of mining. <PERSON><PERSON><PERSON> said this place belonged to the dwarves in the last century and used to be at the front for trading with the elves."}, {"key": "LoadingTipsContent8", "Chinese": "魔晶簇是一种常在地下所见的矿石，魔力泉涌越剧烈的地方魔晶簇的质量也越优质。", "English": "Magical Crystal Cluster is an ore you'd frequently find underground. The best quality magical crystal clusters are found where mana gushed the most violently. "}, {"key": "LoadingTipsContent9", "Chinese": "啮齿魔又称鼠人。它们是常年栖息在幽暗域的魔物很少会将自己暴露在阳光下。", "English": "Rodent Demons are also known as ratman. They are demons living in the Dusk Region and rarely expose themselves to the sunlight."}, {"key": "LoadingTipsContent10", "Chinese": "兽奴，一种体型庞大且头脑愚蠢的巨魔劣种。笨重的身躯能够轻易摧毁巨石。", "English": "Beast Slave, a huge, stupid, and inferior breed to the ogres. They're bulky and can easily destroy giant rock."}, {"key": "LoadingTipsContent11", "Chinese": "连续按下普通攻击可以使出连续的攻击动作。当按下第一次普攻后停顿一下再按下之后的普攻则能使出连续的派生攻击。", "English": "Use consecutive attacks by pressing the attack button continuously. Press the attack button once and pause before you press it again to use consecutive derivative attacks."}, {"________________________________________Roguelike_RoomSelection__": "_______<PERSON><PERSON>房间选择_________________________________"}, {"_____________________________Roguelike_RoomType__": "_____<PERSON><PERSON>房间类型_______________________"}, {"key": "RoomTypeNormal", "Chinese": "正常", "English": "Normal"}, {"key": "RoomTypeElite", "Chinese": "精英", "English": "Elite"}, {"key": "RoomTypeBoss", "Chinese": "Boss", "English": "Boss"}, {"key": "RoomTypeStore", "Chinese": "商店", "English": "Store"}, {"key": "RoomTypeUpgrade", "Chinese": "升级", "English": "Upgrade"}, {"key": "RoomTypeChallenge", "Chinese": "挑战", "English": "Challenge"}, {"_____________________________Roguelike_RoomReward__": "_____<PERSON><PERSON>房间奖励_______________________"}, {"key": "RoomRewardRelic", "Chinese": "圣遗物", "English": "圣遗物"}, {"key": "RoomRewardItem", "Chinese": "主动道具", "English": "主动道具"}, {"key": "RoomRewardTalentpoint", "Chinese": "天赋点", "English": "天赋点"}, {"key": "RoomRewardKey", "Chinese": "钥匙", "English": "钥匙"}, {"key": "RoomRewardChallenge", "Chinese": "挑战", "English": "挑战"}, {"key": "RoomRewardBoss", "Chinese": "Boss", "English": "Boss"}, {"key": "RoomRewardShop", "Chinese": "商店", "English": "商店"}, {"key": "RoomRewardUpgrade", "Chinese": "升级祭坛", "English": "升级祭坛"}, {"_____________________________Roguelike_AwakeSkillName__": "_____Roguelike觉醒技能名字_______________________"}, {"key": "Earthquake", "Chinese": "地震波", "English": "英文翻译"}, {"key": "BloodlyThirsty", "Chinese": "嗜血成性", "English": "英文翻译"}, {"_____________________________Roguelike_AwakeSkillDesc__": "_____Roguelike觉醒技能描述_______________________"}, {"key": "Earthquake_Desc", "Chinese": "崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩崩", "English": "英文翻译"}, {"key": "BloodlyThirsty_Desc", "Chinese": "桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀桀", "English": "英文翻译"}, {"_____________________________Roguelike_RelicName__": "_____<PERSON><PERSON>圣遗物名字_______________________"}, {"key": "BurnOfIgni", "Chinese": "燃烧吧", "English": "英文翻译"}, {"key": "ActtackBullet", "Chinese": "炸弹", "English": "英文翻译"}, {"_____________________________Roguelike_RelicDesc__": "_____<PERSON><PERSON>圣遗物描述_______________________"}, {"key": "BurnOfIgni_Desc", "Chinese": "燃烧吧 —— 天照！！！（测试用，待填）", "English": "英文翻译"}, {"key": "ActtackBullet_Desc", "Chinese": "炸弹   —— 艺术就是派大星 ！！！（测试用，待填）", "English": "英文翻译"}, {"_____________________________Roguelike_RelicDesc__": "_____<PERSON><PERSON>主动道具名字_______________________"}, {"key": "FireBallScroll_Rogue", "Chinese": "火焰卷轴", "English": "英文翻译"}, {"key": "Cannon_Rogue", "Chinese": "大炮", "English": "英文翻译"}, {"key": "HealingPotion_Rogue", "Chinese": "回复药", "English": "英文翻译"}, {"_____________________________Roguelike_RelicDesc__": "_____<PERSON><PERSON>主动道具描述_______________________"}, {"key": "FireBallScroll_Rogue_Explain", "Chinese": "能造成火焰伤害的魔法卷轴", "English": "英文翻译"}, {"key": "Cannon_Rogue_Explain", "Chinese": "自动攻击敌人的炮台", "English": "英文翻译"}, {"key": "HealingPotion_Rogue_Explain", "Chinese": "回复一定的的生命值", "English": "英文翻译"}]}