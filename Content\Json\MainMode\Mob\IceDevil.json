{"Mob": [{"Id": "IceDevil", "Name": "冰恶魔", "Tag": ["IceDevil"], "BpPath": "Core/Characters/IceDevil/IceDevil_New", "AI": ["DoChangeState", "DoRageStateAction", "DoUltAttack", "AttackFreezingEnemy", "AttackAroundEnemies", "AttackBunchEnemies", "AttackNearTwoViewedEnemies", "AttackFarTwoViewedEnemies", "AttackHigherEnemy", "AttackNearOneViewedEnemy", "AttackMiddleDisOneViewedEnemy", "AttackFarOneViewedEnemy", "TiredAction", "AttackRandomTarget", "AttackIceSceneItem", "MoveToFarOneViewedEnemy"], "AIOrder": [{"OrderId": "MoveToLoc", "bReversedCheckTag": true, "CheckTag": "", "AIScriptIdList": ["AIOrderMoveToLocation"]}, {"OrderId": "MoveToTarget", "bReversedCheckTag": false, "CheckTag": "Attack", "AIScriptIdList": ["AIOrderMoveToEnemy"]}, {"OrderId": "Auto", "bReversedCheckTag": true, "CheckTag": "", "AIScriptIdList": []}], "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -200}], "OnBeKilled": ["MobBeKilled.IceDevilBeKilled()"], "ExpGiven": 800, "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 3000, "SightZRadius": 500, "SightHalfAngleDregee": 60, "LoseSightRadius": 4000, "LoseSightHalfAngleDregee": 65, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 30, "Attack": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [{"Id": "AddRageWhenBeHurt", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 1.0}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 0.35}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Arm", "Meat": {"Physical": 0.45}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Leg", "Meat": {"Physical": 0.3}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "脑袋上的角", "Id": "Horn", "Meat": {"Physical": 1.0}, "Breakable": {"Physical": 1.0}, "Part": "Horn", "Durability": [50, 50, 100, 100, 120, 130, 150, 150, 150, 200, 200, 250, 250, 350, 350, 350, 400, 400, 500, 500, 500, 500], "CanBeDestroy": true, "Type": "Horn", "HideSightPart": [], "OnPartBroken": ["IceDevilAIScript.HornBroken()"]}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/Reaper/Move"]}, "InitAction": true}, {"说明": "临时", "Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Hurt/Hurt_LargeBreak"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Monster/Reaper/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Monster/Reaper/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Monster/Reaper/Hurt/Hurt_Back"]}, "InitAction": true}, {"notes": "一阶段跳跃回旋斩", "Id": "S1_Attack_L1", "Cmds": ["Action0"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_L1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_L1"]}, "InitAction": true}, {"notes": "一阶段前踏回旋斩", "Id": "S1_Attack_M1", "Cmds": ["Action1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_M1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_M1"]}, "InitAction": true}, {"notes": "一阶段召唤冰柱", "Id": "S1_Attack_M2", "Cmds": ["Action2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_M2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_M2"]}, "InitAction": true}, {"notes": "一阶段前踏两连回旋斩", "Id": "S1_Attack_S1_1", "Cmds": ["Action3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S1_1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S1_1"]}, "InitAction": true}, {"notes": "一阶段前踏横斩", "Id": "S1_Attack_S1_2", "Cmds": ["Action4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S1_2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S1_2"]}, "InitAction": true}, {"notes": "一阶段上挑斩", "Id": "S1_Attack_S2", "Cmds": ["Action5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S2"]}, "InitAction": true}, {"notes": "一阶段前拉重斩", "Id": "S1_Attack_S3", "Cmds": ["Action6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S3", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S3"]}, "InitAction": true}, {"notes": "一阶段反向回旋斩", "Id": "S1_Attack_S4", "Cmds": ["Action7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S4", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S4"]}, "InitAction": true}, {"notes": "一阶段后跳回旋斩", "Id": "S1_Attack_S5", "Cmds": ["Action8"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_S5", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_S5"]}, "InitAction": true}, {"notes": "一阶段投掷冰镰刀", "Id": "S1_Attack_XL1", "Cmds": ["Action9"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_NormalAttack_XL1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S1_RageAttack_XL1"]}, "InitAction": true}, {"notes": "二阶段前冲交叉斩", "Id": "S2_Attack_L1", "Cmds": ["Action10"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_L1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_L1"]}, "InitAction": true}, {"notes": "二阶段亢奋状态长距离前冲交叉斩", "Id": "S2_Attack_L1_XL", "Cmds": ["Action11"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_L1_XL"]}, "InitAction": true}, {"notes": "二阶段前踏横斩", "Id": "S2_Attack_M1", "Cmds": ["Action12"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_M1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_M1"]}, "InitAction": true}, {"notes": "二阶段前踏回旋斩", "Id": "S2_Attack_S1_1", "Cmds": ["Action13"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_S1_1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_S1_1"]}, "InitAction": true}, {"notes": "二阶段前踏反向回旋斩", "Id": "S2_Attack_S1_2", "Cmds": ["Action14"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_S1_2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_S1_2"]}, "InitAction": true}, {"notes": "二阶段前踏交叉斩", "Id": "S2_Attack_S1_3", "Cmds": ["Action15"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_S1_3", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_S1_3"]}, "InitAction": true}, {"notes": "二阶段前踏上撩斩", "Id": "S2_Attack_S2", "Cmds": ["Action16"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_S2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_S2"]}, "InitAction": true}, {"notes": "二阶段亢奋状态前踏二连斩", "Id": "S2_Attack_S3", "Cmds": ["Action17"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_S3"]}, "InitAction": true}, {"notes": "二阶段投掷冰镰刀", "Id": "S2_Attack_XL1", "Cmds": ["Action18"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NomalAttack_XL1", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_XL1"]}, "InitAction": true}, {"notes": "二阶段亢奋状态冰镰刀挥砍", "Id": "S2_Attack_XL2", "Cmds": ["Action19"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_XL2"]}, "InitAction": true}, {"notes": "空中下劈", "Id": "Attack_DownDash", "Cmds": ["Action20"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/A_AttackDownDash"]}, "InitAction": true}, {"notes": "空中攻击1", "Id": "AirAttack_S1_1", "Cmds": ["Action21"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/AirlAttackS1_1"]}, "InitAction": true}, {"notes": "空中攻击2", "Id": "AirAttack_S1_2", "Cmds": ["Action22"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/AirlAttackS1_2"]}, "InitAction": true}, {"notes": "空中攻击3", "Id": "AirAttack_S1_3", "Cmds": ["Action23"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/AirlAttackS1_3"]}, "InitAction": true}, {"notes": "召唤冰墙", "Id": "Ult", "Cmds": ["Action24"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/Reaper/Ult"]}, "InitAction": true}, {"notes": "进入亢奋状态", "Id": "RageState", "Cmds": ["Action25"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/Reaper/RageState"]}, "InitAction": true}, {"notes": "进入二阶段", "Id": "ChangeState", "Cmds": ["Action26"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/ChangeStage"]}, "InitAction": true}, {"notes": "向前走路", "Id": "Walk_Forward", "Cmds": ["Action27"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Walk_Forward"]}, "InitAction": true}, {"notes": "向后走路", "Id": "Walk_Back", "Cmds": ["Action28"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/Reaper/Walk_Back"]}, "InitAction": true}, {"notes": "向前回避浮空", "Id": "Dodge_Forward", "Cmds": ["Action29"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Forward"]}, "InitAction": true}, {"notes": "向后回避浮空", "Id": "Dodge_Back", "Cmds": ["Action30"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Back"]}, "InitAction": true}, {"notes": "向前回避跳步", "Id": "DashStep_Forward", "Cmds": ["Action31"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/DashStep_Forward"]}, "InitAction": true}, {"notes": "向后回避跳步", "Id": "DashStep_Back", "Cmds": ["Action32"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/DashStep_Back"]}, "InitAction": true}, {"notes": "向左回避跳步", "Id": "DashStep_Left", "Cmds": ["Action33"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/DashStep_Left"]}, "InitAction": true}, {"notes": "向右回避跳步", "Id": "DashStep_Right", "Cmds": ["Action34"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/DashStep_Right"]}, "InitAction": true}, {"notes": "向前回避传送", "Id": "Dodge_Teleport_Forward", "Cmds": ["Action35"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Forward"]}, "InitAction": true}, {"notes": "向后回避传送", "Id": "Dodge_Teleport_Back", "Cmds": ["Action36"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Back"]}, "InitAction": true}, {"notes": "向左回避传送", "Id": "Dodge_Teleport_Left", "Cmds": ["Action37"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Left"]}, "InitAction": true}, {"notes": "向右回避传送", "Id": "Dodge_Teleport_Right", "Cmds": ["Action38"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Right"]}, "InitAction": true}, {"notes": "向上回避传送", "Id": "Dodge_Teleport_Up", "Cmds": ["Action39"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Up"]}, "InitAction": true}, {"notes": "二阶段召唤冰柱", "Id": "S2_Attack_M2", "Cmds": ["Action40"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_NormalAttack_M2", "ArtResource/Anim/Montage/Monster/Reaper/Battle/S2_RageAttack_M2"]}, "InitAction": true}, {"notes": "瞬移到场中", "Id": "Dodge_Teleport_Forward_Ult", "Cmds": ["Action41"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Reaper/Dodge/Dodge_Teleport_Forward_Ult"]}, "InitAction": true}]}]}