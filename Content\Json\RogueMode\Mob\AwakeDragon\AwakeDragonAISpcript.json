{"AIScript": [{"说明": "地面近距离攻击", "Id": "AwakeDragon_GroundNormalAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,300,-30,30)", "MobAIScript.CheckSelfOnGround()"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(100,300,NormalAttack_S1)"]}, {"说明": "地面中远距离跳跃攻击", "Id": "AwakeDragon_GroundJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(400,2000,-45,45)", "AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_GroundJumpAttack)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_GroundJumpAttack,5)", "MobAIScript.AITurnToClosetEnemyInRangeDoAction(400,2000,JumpAttack_Ground)"]}, {"说明": "地面特殊攻击", "Id": "AwakeDragon_GroundSkillAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,1000,-90,90)", "AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_GroundSkillAttack)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_GroundSkillAttack,20)", "MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,800,AwakeDragon_GroundFireAttack)"]}, {"说明": "地面远距离特殊攻击", "Id": "AwakeDragon_GroundSkillAttack_2", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(600,1000,-30,30)", "AwakeDragonAIScript.CheckActionCoolDown(Ground_FireAttack)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AddActionCoolDown(Ground_FireAttack,15)", "MobAIScript.AITurnToClosetEnemyInRangeDoAction(800,1200,Ground_FireAttack)"]}, {"说明": "空中喷火攻击", "Id": "AwakeDragon_SkyFireBreath", "Condition": ["MobAIScript.CheckSelfInSky()", "MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,600,-90,90)", "AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_AirFireBreath)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AIDoActionWithCoolDown(AwakeDragon_AirFireBreath,3)"]}, {"说明": "起飞,每次起飞设置下次落地的间隔", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Switch", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,5000,-180,180)", "MobAIScript.CheckSelfOnGround()", "AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_TakeOff)"], "OnReady": [], "Action": ["AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_Landing,10)", "AwakeDragonAIScript.AIDoActionWithCoolDown(TakeOffJump,30)", "AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_<PERSON>,10)"]}, {"说明": "起飞,每次起飞设置下次落地的间隔", "Id": "Awake<PERSON><PERSON><PERSON>_TakeOff", "Condition": ["AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_TakeOff)", "MobAIScript.CheckSelfOnGround()"], "OnReady": [], "Action": ["AwakeDragonAIScript.AIDoActionWithCoolDown(TakeOffJump,30)", "AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_<PERSON>,10)"]}, {"说明": "近地时直接落地", "Id": "AwakeDragon_NearLanding", "Condition": ["MobAIScript.CheckSelfInSky()", "AwakeDragonAIScript.CheckGroundHeight(100)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(LandOnGround)", "AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_<PERSON>,10)"]}, {"说明": "落地,每次落地设置下次起飞的间隔", "Id": "AwakeDragon_Landing", "Condition": ["MobAIScript.CheckSelfInSky()", "AwakeDragonAIScript.CheckActionCoolDown(AwakeDragon_Landing)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(LandOnGround)", "AwakeDragonAIScript.AddActionCoolDown(AwakeDragon_<PERSON>,20)"]}, {"说明": "基础地面战斗套组", "Id": "AwakeDragonBasicBattleOnGround", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,200)", "MobAIScript.CheckSelfOnGround()"], "OnReady": [], "Action": ["AwakeDragonAIScript.AwakeDragonBasicBattle(0,100,200)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "AwakeDragon_TurnToStimulate_OnGround", "Condition": ["OrcAIScript.CheckHasStimulate()", "MobAIScript.CheckHasEnemyInRange(200,50000)"], "OnReady": [], "Action": ["MobAIScript.AITurnToStimulateDoAction()"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "AwakeDragon_TurnToStimulate_Sky", "Condition": ["MobAIScript.CheckHasEnemyInRange(400,50000)", "MobAIScript.CheckSelfInSky()"], "OnReady": [], "Action": ["MobAIScript.AIMoveToClosetEnemy(400,50000)"]}, {"说明": "180度左右转身动作", "Id": "AwakeDragon_Turn", "Condition": ["GoblinAIScript.CheckCanTurn(0,50000,-60,60)"], "OnReady": [], "Action": ["GoblinAIScript.AttackClosetNoViewedEnemy(0,50000,TurnRight_180,TurnLeft_180)"]}]}