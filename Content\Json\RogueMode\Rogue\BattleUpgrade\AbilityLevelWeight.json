{"AbilityLevelWeight": [{"RoomType": "Normal", "MinLevel": 0, "MaxLevel": 10, "Lv1": 10000, "Lv2": 300, "Lv3": 1}, {"RoomType": "Elite", "MinLevel": 0, "MaxLevel": 10, "Lv1": 10000, "Lv2": 800, "Lv3": 1}, {"RoomType": "Challenge", "MinLevel": 0, "MaxLevel": 10, "Lv1": 10000, "Lv2": 3000, "Lv3": 1}, {"RoomType": "Boss", "MinLevel": 0, "MaxLevel": 10, "Lv1": 10000, "Lv2": 3000, "Lv3": 30}, {"RoomType": "Store", "MinLevel": 0, "MaxLevel": 10, "Lv1": 10000, "Lv2": 2500, "Lv3": 1}, {"RoomType": "Normal", "MinLevel": 11, "MaxLevel": 20, "Lv1": 10000, "Lv2": 285, "Lv3": 5}, {"RoomType": "Elite", "MinLevel": 11, "MaxLevel": 20, "Lv1": 10000, "Lv2": 1250, "Lv3": 10}, {"RoomType": "Challenge", "MinLevel": 11, "MaxLevel": 20, "Lv1": 10000, "Lv2": 2100, "Lv3": 60}, {"RoomType": "Boss", "MinLevel": 11, "MaxLevel": 20, "Lv1": 10000, "Lv2": 9000, "Lv3": 160}, {"RoomType": "Store", "MinLevel": 11, "MaxLevel": 20, "Lv1": 10000, "Lv2": 7500, "Lv3": 200}, {"RoomType": "Normal", "MinLevel": 21, "MaxLevel": 31, "Lv1": 10000, "Lv2": 2854, "Lv3": 25}, {"RoomType": "Elite", "MinLevel": 21, "MaxLevel": 31, "Lv1": 10000, "Lv2": 3750, "Lv3": 50}, {"RoomType": "Challenge", "MinLevel": 21, "MaxLevel": 31, "Lv1": 10000, "Lv2": 4200, "Lv3": 120}, {"RoomType": "Boss", "MinLevel": 21, "MaxLevel": 31, "Lv1": 10000, "Lv2": 9900, "Lv3": 1000}, {"RoomType": "Store", "MinLevel": 21, "MaxLevel": 31, "Lv1": 10000, "Lv2": 26525, "Lv3": 5000}]}