{"Dialogs": [{"说明": "Swordsman_Gerasso通关后初次对话", "Id": "Rogue_Gerasso_WeeklyRoundFirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_WeeklyRound1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_WeeklyRound2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_WeeklyRound3"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Gerasso_WeeklyRound_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Gerasso默认开始对话", "Id": "Rogue_Gerasso_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_FirstDialog2"}], "Selections": [{"说明": "Rogue_Gerasso故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueGerassoStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_Gerasso切换职业", "Conditions": ["DialogCondition.CheckCareerUnlock(Swordsman_Gerasso)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionChangeClass", "NextEventFunc": "DialogAction.DirectGoTo(ChangeClass)", "Actions": []}, {"说明": "Rogue_Gerasso结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "DialogAction.DirectGoTo(EndDialog)", "Actions": []}]}, {"说明": "Rogue_Gerasso默认的重复对话", "Id": "DefaultStoryDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_GuaranteeDialog"}], "Selections": []}, {"说明": "Rogue_Gerasso初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_StoryFirstDialog2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_First_StoryDialog,1)"]}, {"说明": "Rogue_Gerasso初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_StoryFirstDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 别的角色死亡后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_AfterDeathDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_AfterDeathDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_AfterDeathDialog3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(G<PERSON><PERSON>_AfterDeath_StoryDialog,1)"]}, {"说明": "Rogue_Gerasso 别的角色死亡后的重复对话", "Id": "AfterDeathDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_AfterDeathDialogRepeat1"}], "Selections": []}, {"说明": "见过沙漠图后的故事对话", "Id": "ArriveDesertLevelDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ArriveDesert1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ArriveDesert2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ArriveDesert3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_ArriveDesertLevel_StoryDialog,1)"]}, {"说明": "Rogue_Gerasso 见过沙漠图后的重复对话", "Id": "ArriveDesertLevelDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ArriveDesertRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 通关后的重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_WeeklyRoundRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_WeeklyRoundRepeat2"}], "Selections": []}, {"说明": "Rogue_Gerasso 拿到1个圣杯碎片的对话", "Id": "FirstGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFirstGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFirstGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel01_Dialog,1)"]}, {"说明": "Rogue_Gerasso 拿到1个圣杯碎片的重复对话", "Id": "FirstGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFirstGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 得知“圣杯誓言石”后的对话", "Id": "GetGrailInfoDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainGrailOathStone1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainGrailOathStone2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GetGrailInfo_Dialog,1)"]}, {"说明": "Rogue_Gerasso 得知“圣杯誓言石”后的重复对话", "Id": "GetGrailInfoDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainGrailOathStoneRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 拿到2个圣杯碎片的对话", "Id": "SecondGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainSecondGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel02_Dialog,1)"]}, {"说明": "Rogue_Gerasso 拿到2个圣杯碎片的重复对话", "Id": "SecondGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 拿到3个圣杯碎片的对话", "Id": "ThirdGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainThirdGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainThirdGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel03_Dialog,1)"]}, {"说明": "Rogue_Gerasso 拿到3个圣杯碎片的重复对话", "Id": "ThirdGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainThirdGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 拿到4个圣杯碎片的对话", "Id": "ForthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainForthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainForthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainForthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel04_Dialog,1)"]}, {"说明": "Rogue_Gerasso 拿到4个圣杯碎片的重复对话", "Id": "ForthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainForthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 拿到5个圣杯碎片的对话", "Id": "FifthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFifthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFifthGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel05_Dialog,1)"]}, {"说明": "Rogue_Gerasso 拿到5个圣杯碎片的重复对话", "Id": "FifthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_ObtainFifthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso 解除圣杯封印的对话", "Id": "UnlockGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_UnlockGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_UnlockGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Gerasso_GrailLevel06_Dialog,1)"]}, {"说明": "Rogue_Gerasso 解除圣杯封印的重复对话", "Id": "UnlockGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_UnlockGrailRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_UnlockGrailRepeat2"}], "Selections": []}, {"说明": "Rogue_Gerasso 击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_KillRealDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_KillRealDeathLord2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_KillRealDeathLord3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(G<PERSON><PERSON>_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Gerasso 击败真·真死骸骑士的重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_KillRealDeathLordRepeat1"}], "Selections": []}, {"说明": "Rogue_Gerasso切换职业", "Id": "ChangeClass", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_SwitchCharacter1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 2}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 2}], "EndClipScript": ["DialogAction.RogueChangePawn(Swordsman_Gerasso)"]}, {"说明": "Rogue_Gerasso结束对话", "Id": "EndDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "Text": "Gerasso_EndDialog"}], "Selections": []}], "EndDialogScript": []}]}