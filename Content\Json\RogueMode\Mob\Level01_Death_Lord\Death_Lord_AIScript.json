{"AIScript": [{"说明": "转阶段", "Id": "Death_Lord_ChangeStage", "Condition": ["MobAIScript.CheckRogueMobHpLessThanBuffStack(Rogue_Boss_CheckSecondStage)", "MobAIScript.CheckHasBuff(Rogue_Boss_FirstStage)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageState)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "Death_Lord_TurnToStimulate", "Condition": ["MobAIScript.CheckPlayerInRange(200,99999)"], "Action": ["MobAIScript.AIMoveToPlayer()"]}]}