{"Buff": [{"说明": "命中敌人后给自己添加憎恨buff,添加的憎恨buff层数等于该buff的层数", "Id": "AddHate", "Tag": [], "Priority": 0, "MaxStack": 100, "OnHit": ["IceDevilBuff.AddHateBuff()"]}, {"说明": "冰恶魔的憎恨buff", "Id": "IceDevilHate", "Tag": [], "Priority": 0, "MaxStack": 200, "OnOccur": ["BulletScript.PrintBuffStack()"]}, {"说明": "给命中目标添加冰霜buff，添加的冰霜buff的层数等于这个buff的层数", "Id": "IceDevilForstAttack", "Tag": [], "Priority": 0, "MaxStack": 100, "OnHit": ["IceDevilBuff.AddFrostBuff()"], "OnOffense": ["IceDevilBuff.HitIceSceneItem()"]}, {"说明": "冰霜buff，层数到100层后会让角色进入冰冻状态", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Tag": [], "Priority": 0, "MaxStack": 100, "OnOccur": ["IceDevilBuff.SetFreezingWhenStackArrive(100, 2)"]}, {"说明": "冰boss进入二阶段的标记buff", "Id": "IceDevilSecondStage", "Tag": [], "Priority": 0, "MaxStack": 1, "OnOccur": []}, {"说明": "保存投掷冰镰刀的目标冰冻场景物件", "Id": "IceSceneItemTarget", "Tag": [], "Priority": 0, "MaxStack": 1, "OnOccur": []}, {"说明": "用来记录扔冰镰刀动作的CD", "Id": "ThrowIceSyctheCD", "Tag": [], "Priority": 0, "MaxStack": 1, "OnOccur": []}]}