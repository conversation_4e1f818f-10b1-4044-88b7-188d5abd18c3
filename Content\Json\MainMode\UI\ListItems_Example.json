{
    "FListItemElementInfo范例":
    [
        {
            "ListStyle":                    "列表风格 [ListLimited] = 有限列表,即列表选择到最后一个元素再往下一个选择时不会选择为第一个,反之亦然。 [ListEndless] = 无限列表与有限列表反之",
            "EntrySpace":                   "<float> 列表元素间隔大小",
            "ListBlackGroundImage":         "列表底版样式图片路径 path",
            "ListPosition":                 "列表在UI上的位置      X=300,Y=0",
            "ListMaxSize":                  "列表的大小            X=700,Y=700",
            "ListComValue":                 "列表偏移值            100.0",

            "Type":                         "当前页面是什么        Menu", 
            "InterValue":                   "列表元素排列曲线峰值间隔     1.5",
            "CurveAngle":                   "列表曲线     90.0为一个完整的正弦曲线",
            "DisplacementValue":            "列表元素偏移值             25.0",
            "CompensationValue":            "列表整体元素偏移值         70.0",
            "ListItems":                    //列表里的元素 是数组可以有多个
            [
                {
                    "ListItemId":           "元素的Id                       Equipment",
                    "IconGroundImage":      "元素图标的背景图片路径          ArtResource/UI/Menu/Menu_ChangeOccupation/Vacancy",
                    "IconImage":            "元素图标图片路径                ArtResource/UI/Menu/EquipmentUI/LevelOne/Helmet",
                    "Name":                 "元素名字                       装备",
                    "MakerImage":           "元素标记图片路径                ",
                    "SelectImage":          "元素选择图片路径                ArtResource/UI/Menu/EquipmentUI/LevelOne/SelectBox",
                    "EntryGroundImage":     "元素内容背景图片路径            ArtResource/UI/Menu/EquipmentUI/LevelOne/TextPlate",
                    "EntrySelectImage":     "元素内容选择图片路径            ArtResource/UI/Menu/EquipmentUI/LevelOne/TextHover",
                    "IsEquipped":           "是否装备中装备或物品相关        false",
                    "EElementState":        "元素的状态                     State_Generally 一般状态   State_Select选中状态",
                    "EEntryStyle":          "元素的样式                     EntryLeft 元素图标在左边   EntryRight 元素图标在右边  EntryCentered 元素只有内容"
                } 
            ]
        }
    ]
}