{"Class": [{"说明": "游侠", "Id": "<PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["_"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Warrior", "AimBlendSpace": ["ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/AimOffset"], "StateActions": {"Ground": {"Armed": "ShortBow_Move", "UnArmed": "ShortBow_Unarmed_Move"}, "Flying": {"Armed": "ShortBow_Move", "UnArmed": "ShortBow_Unarmed_Move"}, "Falling": {"Armed": "ShortBow_Fall", "UnArmed": "ShortBow_Unarmed_Fall"}, "Attached": {"Armed": "ShortBow_Ride", "UnArmed": "ShortBow_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "ShortBow_Hurt", "UnArmed": "ShortBow_Hurt"}, "Blow": {"Armed": "ShortBow_Blow", "UnArmed": "ShortBow_Blow"}, "Frozen": {"Armed": "ShortBow_Frozen", "UnArmed": "ShortBow_Frozen"}, "Bounced": {"Armed": "ShortBow_Bounced", "UnArmed": "ShortBow_Bounced"}, "Dead": {"Armed": "ShortBow_Dead", "UnArmed": "ShortBow_Dead"}, "Landing": {"Armed": "ShortBow_JustFall", "UnArmed": "ShortBow_<PERSON>rmed_JustFall"}, "SecondWind": {"Armed": "ShortBow_SecWind", "UnArmed": "ShortBow_SecWind"}, "GetUp": {"Armed": "ShortBow_RevivedOnSecWind", "UnArmed": "ShortBow_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "ShortBow", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "ChangeToRanger", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________短弓徒手基础动作________________________________"}, {"说明": "短弓徒手走路站立", "Id": "ShortBow_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Unarmed_Jump", "ShortBow_<PERSON>rmed_Dodge", "ShortBow_DrawWeapon", "Unarm_UseItem", "ShortBow_DrawAttack", "Interactive"], "1": ["ShortBow_Unarmed_Jump", "ShortBow_<PERSON>rmed_Dodge", "ShortBow_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/UnarmedMove"]}}, {"说明": "短弓徒手起跳", "Id": "ShortBow_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "ShortBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "短弓徒手翻滚", "Id": "ShortBow_<PERSON>rmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_<PERSON>rmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/Dodge_F"]}, "InitAction": true}, {"说明": "短弓徒手下落", "Id": "ShortBow_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "ShortBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/UnarmedFall"]}, "Priority": 1}, {"说明": "短弓徒手下落着地", "Id": "ShortBow_<PERSON>rmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "ShortBow_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Jump/UnarmedJustFall", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "短弓收刀", "Id": "SheathShortBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Shea<PERSON><PERSON><PERSON><PERSON>n"]}}, {"说明": "短弓拔刀", "Id": "DrawShortBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "ShortBow_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Player/Ranger/Ranger_Male/DrawWeapon"]}}, {"Line": "_______________________________短弓(LevelSquencer)动作________________________________"}, {"说明": "短弓趴地上_LevelSquencer用", "Id": "FallDown_Loop", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_Loop"]}}, {"说明": "短弓趴地上起来_LevelSquencer用", "Id": "FallDown_End", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_End"]}}, {"说明": "短弓_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________短弓(持武器)基础动作________________________________"}, {"Id": "ShortBow_Move", "Cmds": ["ShortBow_Move"], "Tags": [{"Tag": "ShortBow_Move", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["ShortBow_Shoot", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/Move", "ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/Move_AimState", "ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/Move_AimState"]}}, {"Id": "ShortBow_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_HurtCounter"], "1": ["ShortBow_Dodge"], "2": ["ShortBow_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Hurt_Back", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Hurt_Front", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Hurt_Air", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Hurt_Air"]}}, {"Id": "ShortBow_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_QS_B"], "1": ["ShortBow_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Up", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Back", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Front", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Up", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Back", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Blow_Front"]}}, {"Id": "ShortBow_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "ShortBow_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "ShortBow_Jump", "From": 0}, {"Tag": "ShortBow_Dodge", "From": 0}, {"Tag": "ShortBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Ranger/Ranger_Male/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "ShortBow_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": ["ShortBow_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Ranger/Ranger/Fall"]}, "Priority": 1}, {"Id": "ShortBow_JustFall", "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump", "ShortBow_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Jump/JustFall", "ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "ShortBow_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_Dodge", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/Dodge_F"]}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON><PERSON>_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/Step_F"]}, "InitAction": true}, {"Id": "ShortBow_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/Step_F"]}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "ShortBow_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "ShortBow_QS_B", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Dodge", "ShortBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/QuickStanding_B"]}}, {"说明": "受身动作前翻", "Id": "ShortBow_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "ShortBow_QS_F", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Dodge", "ShortBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Dodge/QuickStanding_F"]}}, {"说明": "倒地动作", "Id": "ShortBow_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "ShortBow_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________短弓特殊动作________________________________"}, {"Line": "_______________________________短弓受击(特殊)动作________________________________"}, {"Id": "ShortBow_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Player/Ranger/Ranger_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________短弓基础(附加)动作________________________________"}, {"说明": "瞄准动作", "Id": "ShortBow_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "ShortBow_Aim", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Shoot", "ShortBow_Jump"], "1": ["ShortBow_Dodge"], "2": []}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_NormalAtk_Aim"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_Male/PickUp"]}}, {"Line": "_______________________________短弓命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "ShortBow_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Ranger/Ranger_Male/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "ShortBow_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Ranger/Ranger_Male/OrderBuddy/OrderBuddyMoveToTarget"]}}], "RogueBattleActions": [{"Line": "_______________________________短弓战斗动作_______________________________"}, {"Line": "_______________________________短弓_普攻_地面_______________________________"}, {"说明": "短弓 普通攻击 1", "Id": "Ranger_LAttack01", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_LAttack1", "From": 0}, {"Tag": "ShortBow_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_LAttack2"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"], "3": ["ShortBow_BranchAttack1"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_Slash0"]}, "InitAction": true}, {"说明": "短弓 普通攻击 2", "Id": "Ranger_LAttack02", "Cmds": [], "Tags": [{"Tag": "ShortBow_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_LAttack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_Slash1"]}, "InitAction": true}, {"Id": "Ranger_LAttack03", "Cmds": [], "Tags": [{"Tag": "ShortBow_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_LAttack1"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_Slash2"]}, "InitAction": true}, {"说明": "短弓分支普通攻击1", "Id": "Ranger_BranchAttack1", "Cmds": [], "Tags": [{"Tag": "ShortBow_BranchAttack1", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack2"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SlashB0"]}, "InitAction": true}, {"说明": "短弓分支普通攻击2", "Id": "Ranger_BranchAttack2", "Cmds": [], "Tags": [{"Tag": "ShortBow_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SlashB1"]}, "InitAction": true}, {"说明": "短弓分支普通攻击3", "Id": "Ranger_BranchAttack3", "Cmds": [], "Tags": [{"Tag": "ShortBow_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack4"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SlashB2"]}, "InitAction": true}, {"说明": "短弓分支普通攻击4", "Id": "Ranger_BranchAttack4", "Cmds": [], "Tags": [{"Tag": "ShortBow_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack5"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SlashB3"]}, "InitAction": true}, {"说明": "短弓分支普通攻击5", "Id": "Ranger_BranchAttack5", "Cmds": [], "Tags": [{"Tag": "ShortBow_BranchAttack5", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SlashB4"]}, "InitAction": true}, {"Id": "Ranger_NormalAtk_Shoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_Shoot", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_NormalAtk_Shoot"]}, "InitAction": true}, {"Line": "_______________________________短弓_普攻_空中_______________________________"}, {"Id": "Ranger_AirAttack1", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirAttack1", "From": 0}, {"Tag": "ShortBow_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirAttack2", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/A_Slash0"]}, "InitAction": true}, {"Id": "Ranger_AirAttack2", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirAttack1"], "1": ["_"], "2": ["ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________短弓_技能_地面_动作_______________________________"}, {"Id": "Ranger_BackJumpShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BackJumpShoot"]}, "InitAction": true}, {"Id": "Ranger_BackJumpShootAir", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BackJumpShootAir"]}, "InitAction": true}, {"Id": "Ranger_BackStepShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BackStepShoot"]}, "InitAction": true}, {"Id": "Ranger_BackStepShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BackStepShoot"]}, "InitAction": true}, {"Id": "Ranger_BurstShot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BurstShot"]}, "InitAction": true}, {"Id": "Ranger_BurstShot_Up", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_BurstShot_Up"]}, "InitAction": true}, {"Id": "Ranger_JumpAirShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_JumpAirShoot"]}, "InitAction": true}, {"Id": "Ranger_JumpShootDown", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_JumpShootDown"]}, "InitAction": true}, {"Id": "Ranger_QuickcShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_QuickcShoot"]}, "InitAction": true}, {"Id": "Ranger_QuickcShoot2", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_QuickcShoot2"]}, "InitAction": true}, {"Id": "Ranger_SlideShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_SlideShoot"]}, "InitAction": true}, {"Id": "Ranger_SreadShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_SreadShoot"]}, "InitAction": true}, {"Id": "Ranger_StormBurstShot", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillBow_StormBurstShot"]}, "InitAction": true}, {"Id": "<PERSON><PERSON>BackJ<PERSON>", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillFt_BackJump"]}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>", "Cmds": [], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillFt_GriffinKick"]}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>_BackJump", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Dodge_Step"], "1": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/G_SkillFt_GriffinKick_BackJump"]}, "InitAction": true}, {"Line": "_______________________________短弓_技能_空中_动作_______________________________"}, {"Id": "Ranger_Air_BackJumpAirShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirAttack1", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/A_SkillBow_BackJumpAirShoot"]}, "InitAction": true}, {"Id": "Ranger_Air_JumpAirShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirAttack1", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/A_SkillBow_JumpAirShoot"]}, "InitAction": true}, {"Id": "Ranger_Air_JumpShootDown", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirAttack1", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/A_SkillBow_JumpShootDown"]}, "InitAction": true}, {"Id": "Ranger_Air_SreadShoot", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirAttack1", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Player/Ranger/Ranger_Male/Attack/A_SkillBow_SreadShoot"]}, "InitAction": true}, {"Id": "Ranger_Air_SkillFt_BackJump", "Cmds": [], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirAttack1", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Player/Ranger/Ranger_Male/Attack/A_SkillFt_BackJump"]}, "InitAction": true}, {"Line": "_______________________________短弓_瞄准_动作_______________________________"}], "RogueBattleStyle": ["RangerStyle_1", "RangerStyle_2", "RangerStyle_3"]}], "Buff": [], "Aoe": []}