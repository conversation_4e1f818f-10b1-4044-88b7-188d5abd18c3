{"Buff": [{"Id": "OgreWithPillar", "Tag": ["Ogre", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnChangeAction": [], "OnHit": [], "OnOccur": ["OgreBuff.DeletePillarSceneItem(RockMace_EquipR)"], "OnRemoved": ["OgreBuff.DeleteAttachedPillarAOE()"]}, {"Id": "CanPullUpPillar", "Tag": ["Ogre", "<PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnChangeAction": [], "OnHit": []}, {"Id": "OgreRage", "Tag": ["Ogre"], "Priority": 0, "MaxStack": 1, "OnChangeAction": [], "OnHit": []}, {"Id": "OgreRested", "Tag": ["Ogre"], "Priority": 0, "MaxStack": 4, "OnChangeAction": [], "OnHit": []}, {"Id": "OgreAlwaysRage", "Tag": ["Ogre"], "TickTime": 1, "Priority": 0, "MaxStack": 1, "OnTick": ["OgreBuff.SetExcitedWhenHPLess(0.4)"], "OnChangeAction": [], "OnHit": []}, {"Id": "AccumulateDamageAndHurt", "Tag": ["Ogre"], "Priority": 0, "MaxStack": 999999, "OnBeHurt": ["BuffUtils.AccumulateDamageAndHurt(500,HurtFromRock)"]}]}