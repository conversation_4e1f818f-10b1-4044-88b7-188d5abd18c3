{"DungeonInfo": [{"Id": "TestDungeon", "Name": "矿洞地下城", "RandamMapCondition": "MapScript.CheckMineStoryStep()", "DungeonRooms": [{"Step": 0, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_Entrance_01", "Condition": ""}]}, {"Step": 1, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_07", "Condition": "MapScript.CheckMineStoryStepAndSetValue(0,50)"}, {"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_01", "Condition": "MapScript.CheckMineStoryStepAndSetValue(1,50)"}]}, {"Step": 2, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_01", "Condition": "MapScript.CheckMineStoryStepAndSetValue(0,50)"}, {"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_07", "Condition": "MapScript.CheckMineStoryStepAndSetValue(1,50)"}]}, {"Step": 3, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_05", "Condition": ""}]}, {"Step": 4, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_03", "Condition": ""}]}, {"Step": 5, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_09", "Condition": ""}]}, {"Step": 6, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_06", "Condition": ""}]}, {"Step": 7, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_02", "Condition": ""}]}, {"Step": 8, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_04", "Condition": ""}]}, {"Step": 9, "RoomList": [{"RoomLevelPath": "/Game/Maps/MainMapWhiteBox/LA_Dungeon_Entrance_02", "Condition": ""}]}], "PresetTileMap": [{"Id": "TestDungeonTileMap03", "TileList": [{"Step": 0, "RoomSeat": ["x=0,y=0"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=0,y=1", "DoorLinkedStep": 1}]}, {"Step": 1, "RoomSeat": ["x=0,y=1"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=0,y=-1", "DoorLinkedStep": 0}, {"DoorSeatIndex": 0, "DoorDir": "x=0,y=1", "DoorLinkedStep": 2}, {"DoorSeatIndex": 0, "DoorDir": "x=1,y=0", "DoorLinkedStep": 7}]}, {"Step": 2, "RoomSeat": ["x=0,y=2"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=0,y=-1", "DoorLinkedStep": 1}, {"DoorSeatIndex": 0, "DoorDir": "x=0,y=1", "DoorLinkedStep": 3}, {"DoorSeatIndex": 0, "DoorDir": "x=-1,y=0", "DoorLinkedStep": 9}]}, {"Step": 3, "RoomSeat": ["x=0,y=3"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=0,y=-1", "DoorLinkedStep": 2}, {"DoorSeatIndex": 0, "DoorDir": "x=-1,y=0", "DoorLinkedStep": 5}, {"DoorSeatIndex": 0, "DoorDir": "x=1,y=0", "DoorLinkedStep": 4}]}, {"Step": 4, "RoomSeat": ["x=1,y=3"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=-1,y=0", "DoorLinkedStep": 3}]}, {"Step": 5, "RoomSeat": ["x=-1,y=3"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=1,y=0", "DoorLinkedStep": 3}, {"DoorSeatIndex": 0, "DoorDir": "x=0,y=1", "DoorLinkedStep": 6}]}, {"Step": 6, "RoomSeat": ["x=-1,y=4"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=0,y=-1", "DoorLinkedStep": 5}]}, {"Step": 7, "RoomSeat": ["x=1,y=1"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=-1,y=0", "DoorLinkedStep": 1}, {"DoorSeatIndex": 0, "DoorDir": "x=1,y=0", "DoorLinkedStep": 8}]}, {"Step": 8, "RoomSeat": ["x=2,y=1", "x=2,y=2"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=-1,y=0", "DoorLinkedStep": 7}]}, {"Step": 9, "RoomSeat": ["x=-1,y=2"], "DoorList": [{"DoorSeatIndex": 0, "DoorDir": "x=1,y=0", "DoorLinkedStep": 2}]}]}], "MobLootTag": ["TestDungeon"], "ChestLootTag": ["TestDungeon"], "RewardLootTag": [], "Camps": [{"CampId": "<PERSON><PERSON><PERSON>", "StartProgress": 800, "Event": [{"EventId": "RatManKingLastAppear", "ProgressMin": 1, "ProgressMax": 100, "Limit": 1, "TriggerFunc": []}, {"EventId": "RatManBelow300_RodianStory03StepTo3", "ProgressMin": 0, "ProgressMax": 300, "Limit": 1, "TriggerFunc": ["DungeonCampModFunc.CheckForSetRodianStory03Step()", "DesignerScript/TriggerScript_Country.ShowTaskTipsInCampEvent()"]}, {"EventId": "RatManBelow800_MineStoryStepTo2", "ProgressMin": 0, "ProgressMax": 800, "Limit": 1, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "RatManBelow500_MineStoryStepTo4", "ProgressMin": 0, "ProgressMax": 500, "Limit": 1, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory0", "ProgressMin": 400, "ProgressMax": 500, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory1", "ProgressMin": 300, "ProgressMax": 400, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory2", "ProgressMin": 200, "ProgressMax": 300, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory3", "ProgressMin": 100, "ProgressMax": 200, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}]}, {"CampId": "Goblin", "StartProgress": 0, "Event": [{"EventId": "CheckForMineStory4", "ProgressMin": 900, "ProgressMax": 1000, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory5", "ProgressMin": 800, "ProgressMax": 900, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory65", "ProgressMin": 700, "ProgressMax": 800, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory7", "ProgressMin": 600, "ProgressMax": 700, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory8", "ProgressMin": 500, "ProgressMax": 600, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory9", "ProgressMin": 400, "ProgressMax": 500, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory10", "ProgressMin": 300, "ProgressMax": 400, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory11", "ProgressMin": 200, "ProgressMax": 300, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory12", "ProgressMin": 100, "ProgressMax": 200, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}, {"EventId": "CheckForMineStory13", "ProgressMin": 0, "ProgressMax": 100, "Limit": 50, "TriggerFunc": ["DungeonCampModFunc.CheckForSetMineStoryStep()"]}]}]}]}