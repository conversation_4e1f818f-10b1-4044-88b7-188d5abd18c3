{"Actions范例": [{"Id": "一个Action的Id", "Cmds": ["这个Action的命令，可以有好几个"], "Tags": [{"Tag": "可以Cancel别人的Tag", "From": "<Float> Cancel别人后从第几秒开始播放自己"}], "BeCancelledTags": {"0": ["可以被Cancel的Tag，对应其他动作的Tags下的Tag", "一段可以有好几个Tag", "开头数字对应AnimNotifyState中填写的数字"], "1": ["可以有好几个，但是主要得在动作中拉CancellableArea的AnimNotifyState"]}, "Priority": "<Int> 动作优先级，数字越大，越可能被播放", "Anim": {"Period": "是否是每一帧都要重新算一次TickChecker的，否代表只运行一次", "StateFunc": "一个判断状态然后返回选取Anim中第几条作为当前状态应该播放的动画的函数，在DesignerScript下", "Anim": ["ArtResource/Anim/BlendSpace/Human/Move/Warrior/Move", "要播放的对应BlendSpace或者Montage的路径，至于是要BlendSpace还是Montage，取决于用这条数据的地方", "如果是State用，就是BlendSpace；如果不是State，那就是Montage"]}, "CanStopSprint": "true/false, 做这个 Action 的时候是否会停止冲刺，默认是true，比如 Jump/JustFallMoving 这种动作为 false", "Cost": {"解释": "释放动作需要的花费，比如需要 -1HP，-10MP，-20SP", "HP": 0, "MP": 0, "SP": 0, "ReplaceCmd": "如果资源不够，会添加这个 Cmd 键入", "ReplaceAction": "如果资源不够，会播放这个Action（使用 PreorderAction 调用，优先级比 ReplaceCmd 高）"}, "CanUseOnFalling": "<bool> 在下落状态是否能做这个动作，值得注意的是，飞行怪是没有下落状态的", "CanOnlyUseOnFalling": "<bool> 只有下落状态能用", "Damage": {"0": {"MainHand": "<float>0号点上按照主手武器的伤害倍率，这个倍率其实是动作值x主手武器效果占比得来的，最终x主手武器攻击力得出伤害力，默认是1", "OffHand": "<float>副手伤害倍率，默认0", "Ranged": "<float>远程伤害倍率，默认0", "Concealed": "<float>暗器伤害倍率，默认0"}}, "AddBuff": {"0": {"Id": "在这个点下要添加的Buff的Id，一个点只允许一个Buff添加，并且是Action使用者自己对自己上的", "Stack": "<int>要添加的Buff的层数", "Time": "<float>要添加的Buff的时间长度（秒）", "Infinity": "<bool>如果是true，则代表无限时间，Time就因此无效了"}}, "FXInfo": {"0": {"VFX": "这个点对应播放的视觉特效", "SFX": "这个点对应的音频特效"}}, "InitAction": "<bool> 是否一开始就学会了，没学会的还不让做"}]}