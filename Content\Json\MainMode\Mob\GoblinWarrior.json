{"Mob": [{"Id": "GoblinWarrior1", "Tag": ["Goblin"], "AI": ["Goblin_ClearRoaredBuff", "Go<PERSON>_CelebrateVictory", "Go<PERSON>_Roar", "GoblinSwordman_AddBuff", "Goblin_DodgeBack", "Goblin_DodgeLeftOrRight", "Goblin_LeftJump", "Goblin_RightJump", "Goblin_FrontStepAttack", "Goblin_RageFrontStepAttack", "Goblin_JumpAttack", "Goblin_RageJumpAttack", "Goblin_BigJumpAttack", "Goblin_RageBigJumpAttack", "Goblin_<PERSON>", "Goblin_Turn", "MoveToClosetViewedEnemy", "Goblin_MoveToNextPathNode"], "BpPath": "Core/Characters/Goblin/GoblinSwordman/Goblin_Swordman", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "Goblin", "ModValue": -1}], "Flyable": false, "LootPackageId": "Goblin", "MountsTypeRotate": false, "Name": "哥布林战士", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 500, "SightHalfAngleDregee": 90, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 100, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 1, "Attack": 5, "Balance": 10, "MoveSpeed": [325, 655, 980], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1, "Infinity": false}], "Equipments": [{"Id": "GoblinSwordShield", "Rate": 1}], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 0.5}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/Goblin/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/Protector_Hit_F", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/Protector_Hit_B", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/Protector_Hit_F", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/Protector_Hit_B"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Hurt/HurtUp"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Protector_Die_2"]}, "InitAction": true}, {"说明": "原地跳来跳去", "Id": "CelebrateVictory", "Cmds": ["CelebrateVictory"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Taunt/Taunt_01"]}, "InitAction": true}, {"说明": "跟在其他后面的发呆", "Id": "Daze02", "Cmds": ["Daze02"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Taunt/Taunt_03"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Taunt/FindTarget01"]}, "InitAction": true}, {"说明": "加buff", "Id": "AddBuff", "Cmds": ["AddBuff"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Taunt/Roar"]}, "InitAction": true}, {"说明": "前踏斩", "Id": "FrontStepAttack", "Cmds": ["FrontStepAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/NormalAttack_S1"]}, "InitAction": true}, {"说明": "前踏三连击（愤怒）", "Id": "RageFrontStepAttack", "Cmds": ["RageFrontStepAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/RageAttack_S1"]}, "InitAction": true}, {"说明": "前跳二连击", "Id": "JumpAttack", "Cmds": ["JumpAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/NormalAttack_M1"]}, "InitAction": true}, {"说明": "前跳三连击（愤怒）", "Id": "RageJumpAttack", "Cmds": ["RageJumpAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/RageAttack_M1"]}, "InitAction": true}, {"说明": "大跳震地", "Id": "BigJumpAttack", "Cmds": ["BigJumpAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "大跳震地（愤怒）", "Id": "RageBigJumpAttack", "Cmds": ["RageBigJumpAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Attack/RageAttack_L1"]}, "InitAction": true}, {"说明": "向左跳", "Id": "LeftJump", "Cmds": ["LeftJump"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Dodge_JumpStep_Left"]}, "InitAction": true}, {"说明": "向右跳", "Id": "RightJump", "Cmds": ["RightJump"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Dodge_JumpStep_Right"]}, "InitAction": true}, {"说明": "左踱步", "Id": "LeftPace", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/RunSet_LeftThenRight"]}, "InitAction": true}, {"说明": "右踱步", "Id": "RightPace", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/RunSet_RightThenLeft"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Dodge_JumpStep_Back"]}, "InitAction": true}, {"说明": "向左侧滚闪避", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Dodge_Roll_Left"]}, "InitAction": true}, {"说明": "向右侧滚闪避", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Dodge_Roll_right"]}, "InitAction": true}, {"说明": "向左90度转身", "Id": "TurnLeft_90", "Cmds": ["TurnLeft_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Turn_Left_90"]}}, {"说明": "向左180度转身", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Turn_Left_180"]}}, {"说明": "向右90度转身", "Id": "TurnRight_90", "Cmds": ["TurnRight_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Turn_Right_90"]}}, {"说明": "向右180度转身", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Swordman/Dodge/Turn_Right_180"]}}]}]}