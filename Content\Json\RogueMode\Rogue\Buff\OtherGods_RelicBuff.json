{"说明": "遗物相关的Buff", "Buff1": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中概率引发额外突刺", "Id": "Anim_Pierce<PERSON>it", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.DoExtraOffense(0.2,3,<PERSON><PERSON><PERSON><PERSON><PERSON>,Physical)"]}, {"说明": "动画中造成的break上升", "Id": "Anim_BreakUp", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "OnHit": ["BuffUtils.BreakDamageUp(0.05)"]}, {"分割": "-------------------------------------------Other-----------------------------------------"}, {"说明": "攻击时产生一个剑气", "Id": "AttackCreateBulletOnSocket", "Tag": ["CreateBullet", "Rogue_Slash", "NotSave", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Bullet_SwordLight,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"]}, {"说明": "突刺攻击时刻产生额外距离Aoe", "Id": "Rogue_Pierce1_Effect", "Tag": ["Hit", "NotSave", "<PERSON><PERSON><PERSON>", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(<PERSON>_<PERSON><PERSON>_<PERSON>,1,<PERSON>_<PERSON>et)"]}, {"说明": "突刺攻击命中时额外造成伤害", "Id": "Rogue_Pierce2_Effect", "Tag": ["NotSave", "Rogue_Pierce_State", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_PierceHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_PierceHit,1,0,true)"]}, {"说明": "钝击攻击时刻产生额外Aoe", "Id": "Rogue_Bludgeon1_Effect", "Tag": ["Hit", "NotSave", "Rogue_Bludgeon", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Blu<PERSON>on,1,Root)"]}, {"说明": "钝击攻击时造成的break上升", "Id": "Rogue_Bludgeon2_Effect", "Tag": ["Hit", "NotSave", "Rogue_Bludgeon_State", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_BreakUp,10,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_BreakUp,10,0,true)"]}, {"说明": "造成伤害时x%概率给对方一个会受到额外伤的DeBuff", "Id": "BloodlyHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,<PERSON>_<PERSON>ly,1,8,true,false)"]}, {"说明": "造成暴击时回复1点生命", "Id": "HealHpOnCrit", "Tag": ["Relic", "<PERSON>rit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnCrit": ["RogueBuff.HealHpOnCrit(1)"]}, {"说明": "造成暴击时回复充能", "Id": "RecoverItemOnCrit", "Tag": ["Relic", "<PERSON>rit", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0, "OnCrit": ["RogueBuff.RecoverItemOnCrit(0.01)"]}, {"说明": "受到伤害时候+1金币", "Id": "<PERSON><PERSON><PERSON>", "Tag": ["Relic", "Hurted", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.GiveRogueCurrencyOnHurt(Rogue_Coin,1)"]}, {"说明": "击杀怪物时增加主动道具充能", "Id": "AddItemRecoverOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnKill": ["RogueBuff.AddItemRecoverOnKill(0.01)"]}, {"说明": "击杀精英怪或Boss时永久加生命上限", "Id": "LifeOfKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["RogueBuff.AddMaxLifByKillOnOccur(5,999999)"], "OnKill": ["RogueBuff.AddMaxLifeOnKill(Elite,5,999999)"]}, {"说明": "击杀敌人时候获得3s的加速25%的Buff", "Id": "Rogue_Add30SpeedOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.AddBuffOnKill(Rogue_SpeedUpOnKill_01,3000,3,false,true)"]}, {"说明": "击杀敌人时候获得3s的加速15%的Buff", "Id": "Rogue_Add20SpeedOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.AddBuffOnKill(Rogue_SpeedUpOnKill_01,2000,3,false,true)"]}, {"说明": "肉鸽击杀后速度提高1（最高50%）", "Id": "Rogue_SpeedUpOnKill_01", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽击杀后10%概率生成范围治疗", "Id": "Rogue_CreateHealAoeOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.ChanceCreateAoeOnKill(0.1,<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5,root)"]}, {"说明": "身上每有一个圣遗物，全攻击力增加0.01%，上限300%约等于没有上限", "Id": "Rogue_RelicCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToAttackPercent(1,30000)"]}, {"说明": "身上每有一种神的圣遗物，全攻击力增加0.01%", "Id": "Rogue_RelicGodCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToAttackPercent(1)"]}, {"说明": "身上每有一块钱，全攻击力增加0.01%", "Id": "Rogue_MoneyCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastCoinNumToAttackPercent(1)"]}, {"说明": "身上每有一个圣遗物，暴击率增加0.01%，上限100%（约等于没有上限", "Id": "Rogue_RelicCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalChance(0.0001,1)"]}, {"说明": "身上每有一种神的圣遗物，暴击率增加0.01%", "Id": "Rogue_RelicGodCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalChance(0.0001)"]}, {"说明": "身上每有一个圣遗物，暴击倍率增加0.01%，上限200%", "Id": "Rogue_RelicCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalRate(0.0001,2)"]}, {"说明": "身上每有一种神的圣遗物，暴击率增加0.01%", "Id": "Rogue_RelicGodCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalRate(0.0001)"]}, {"说明": "身上每有一个圣遗物，最大生命增加1，上限100（约等于没有上限", "Id": "Rogue_CastRelicNumToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicNumToMaxHp(1,100)"], "OnTick": ["RogueBuff.CastRelicNumToMaxHp(1,100)"]}, {"说明": "身上每有一种神的圣遗物，最大生命增加1", "Id": "Rogue_CastRelicGodTagToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicGodTagToMaxHp(1)"], "OnTick": ["RogueBuff.CastRelicGodTagToMaxHp(1)"]}, {"说明": "肉鸽主动道具充能时,提升暴击率", "Id": "Rogue_CriticalChanceUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalChanceUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "肉鸽主动道具充能时,提升暴击倍率", "Id": "Rogue_CriticalRateUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalRateUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "敌人没有或break值低于50%的时候暴击率提升1%", "Id": "敌人没有或break值低于0%（breakdown时间延长故条件收紧）的时候暴击率提升1%", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.LowBreakEnemyCriticalChanceUp(0.5,0.0001)"]}, {"说明": "对生命值低于30%的敌人造成的伤害提升1%", "Id": "Rogue_DamageUpToLowHPEnemy", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.DamageUpToLowHpEnemy(0.3,0.01)"]}, {"说明": "每过n秒没有直接攻击,则下次攻击造成的伤害+1%", "Id": "Rogue_TimeIntervalDamageUp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.TimeIntervalDamageUp(3,0.01)"]}, {"描述": "-------------------------------------XXX后提升------------------------------------------------"}, {"说明": "肉鸽动作后速度提高", "Id": "Rogue_SpeedUpOnAction", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽动作后攻击力提高", "Id": "Rogue_AttackUpOnAction", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "动作后,角色造成的直接动作伤害提高,限制层数", "Id": "Rogue_AfterDirectDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 100, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "使用法器后，添加攻击速度提升30%的Buff", "Id": "UseItem_SpeedUp01", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem_01,3000,3,false,true)"]}, {"说明": "肉鸽使用法器后速度提高(大)", "Id": "Rogue_SpeedUpOnUseItem_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "使用法器后，添加攻击速度提升20%的Buff", "Id": "UseItem_SpeedUp02", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem_02,2000,3,false,true)"]}, {"说明": "肉鸽使用法器后速度提高(中)", "Id": "Rogue_SpeedUpOnUseItem_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "使用法器后，添加攻击速度提升10%的Buff", "Id": "UseItem_SpeedUp03", "Tag": ["Relic", "NotSave", "Rogue_MagicItem"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SpeedUpOnUseItem_03,1000,3,false,true)"]}, {"说明": "肉鸽使用法器后速度提高(小)", "Id": "Rogue_SpeedUpOnUseItem_03", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "击飞技能后,角色造成的直接动作伤害提高,限制层数", "Id": "Rogue_RisePhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 50, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "击飞技能后，添加物理伤害增强20%的Buff,同种至多叠加50%", "Id": "Rise_AddPhysicalDamage01", "Tag": ["Relic", "NotSave", "Rogue_BelowStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_RisePhysicalDamageUpLimited,20,15,false,true)"]}, {"说明": "肉鸽击飞动作后速度提高(大)", "Id": "Rogue_SpeedUpOnBelow_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽击飞动作后攻击力提高(大)", "Id": "Rogue_AttackUpOnBelow_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "击飞技能后，添加物理伤害增强10%的Buff", "Id": "Rise_AddPhysicalDamage02", "Tag": ["Relic", "NotSave", "Rogue_BelowStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_RisePhysicalDamageUpLimited,10,15,false,true)"]}, {"说明": "肉鸽击飞动作后速度提高(小)", "Id": "Rogue_SpeedUpOnBelow_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽击飞动作后攻击力提高(小)", "Id": "Rogue_AttackUpOnBelow_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "下砸技能后,角色造成的物理伤害提高,限制层数", "Id": "Rogue_SmashPhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 50, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "下砸技能后，添加物理伤害增强20%的Buff", "Id": "Smash_AddPhysicalDamage01", "Tag": ["Relic", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SmashPhysicalDamageUpLimited,20,15,false,true)"]}, {"说明": "肉鸽下砸动作后速度提高(大)", "Id": "Rogue_SpeedUpOnSmash_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽下砸动作后攻击力提高(大)", "Id": "Rogue_AttackUpOnSmash_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "下砸技能后，物理伤害增强10%的Buff", "Id": "Smash_AddPhysicalDamage02", "Tag": ["Relic", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_SmashPhysicalDamageUpLimited,10,15,false,true)"]}, {"说明": "肉鸽下砸动作后速度提高(小)", "Id": "Rogue_SpeedUpOnSmash_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽下砸动作后攻击力提高(小)", "Id": "Rogue_AttackUpOnSmash_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "位移技能后,角色造成的物理伤害提高,限制层数", "Id": "Rogue_DashPhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 50, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "位移技能后，添加物理伤害增强10%的Buff", "Id": "Dash_AddPhysicalDamage01", "Tag": ["Relic", "NotSave", "Rogue_DashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_DashPhysicalDamageUpLimited,10,15,false,true)"]}, {"说明": "肉鸽位移动作后速度提高(大)", "Id": "Rogue_SpeedUpOnDash_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽位移动作后攻击力提高(大)", "Id": "Rogue_AttackUpOnDash_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "位移技能后，添加物理伤害增强5%的Buff", "Id": "Dash_AddPhysicalDamage02", "Tag": ["Relic", "NotSave", "Rogue_DashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_DashPhysicalDamageUpLimited,5,15,false,true)"]}, {"说明": "肉鸽位移动作后速度提高(小)", "Id": "Rogue_SpeedUpOnDash_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽位移动作后攻击力提高(小)", "Id": "Rogue_AttackUpOnDash_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "蓄力满后,角色造成的物理伤害提高,限制层数", "Id": "Rogue_PowerFinalPhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 100, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "蓄力满后，物理伤害增强50%的Buff", "Id": "PowerFinal_AddPhysicalDamage01", "Tag": ["Relic", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Rogue_PhysicalDamageUpLimited,50,15,false,true)"]}, {"说明": "肉鸽蓄力满后速度提高(大)", "Id": "Rogue_SpeedUpOnPowerFinal_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽蓄力满后攻击力提高(大)", "Id": "Rogue_AttackUpOnPowerFinal_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "蓄力满后，物理伤害增强30%的Buff", "Id": "PowerFinal_AddPhysicalDamage02", "Tag": ["Relic", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Rogue_PhysicalDamageUpLimited,30,3,false,true)"]}, {"说明": "肉鸽蓄力满后速度提高(小)", "Id": "Rogue_SpeedUpOnPowerFinal_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽蓄力满后攻击力提高(小)", "Id": "Rogue_AttackUpOnPowerFinal_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "蓄力满后,角色造成的物理伤害提高,限制层数", "Id": "Rogue_JustDodgePhysicalDamageUpLimited", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 100, "OnHit": ["RogueBuff.DirectDamageUp(0.01)"]}, {"说明": "完美闪避后，物理伤害增强50%的Buff", "Id": "JustDodge_AddPhysicalDamage01", "Tag": ["Relic", "NotSave", "Rogue_JustDodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_JustDodgePhysicalDamageUpLimited,50,15,false,true)"]}, {"说明": "肉鸽完美闪避后速度提高(大)", "Id": "Rogue_SpeedUpOnJustDodge_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽完美闪避后攻击力提高(大)", "Id": "Rogue_AttackUpOnJustDodge_01", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 5000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "完美闪避后，物理伤害增强30%的Buff", "Id": "JustDodge_AddPhysicalDamage02", "Tag": ["Relic", "NotSave", "Rogue_JustDodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.AddBuffObj(Rogue_JustDodgePhysicalDamageUpLimited,30,3,false,true)"]}, {"说明": "肉鸽完美闪避后速度提高(小)", "Id": "Rogue_SpeedUpOnJustDodge_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 2000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽完美闪避后攻击力提高(小)", "Id": "Rogue_AttackUpOnJustDodge_02", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"PAtk": 1, "MAtk": 1}]}, {"说明": "使肉鸽一级元素伤害可以暴击", "Id": "Rogue_EnableJuniorElementalCrit", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["RogueBuff.EnableElementalCrit(Fire,Wind,Thunder,Ice,Light,Darkness)", "RogueBuff.EnableDamageTypeCrit(RogueJuniorDamage)"]}, {"说明": "使肉鸽二级级元素伤害可以暴击", "Id": "Rogue_EnableSeniorElementalCrit", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["RogueBuff.EnableElementalCrit(Fire,Wind,Thunder,Ice,Light,Darkness)", "RogueBuff.EnableDamageTypeCrit(RogueSeniorDamage)"]}]}