{"Class": [{"说明": "剑客-苏棠-Tang", "Id": "<PERSON><PERSON><PERSON><PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "BladeDancer", "<PERSON><PERSON><PERSON>", "Warrior_Elf"], "Buffs": ["Character_Element"], "BaseActionType": "<PERSON>_<PERSON><PERSON><PERSON>", "StateActions": {"Ground": {"Armed": "SwordShield_Move", "UnArmed": "SwordShield_Unarmed_Move"}, "Flying": {"Armed": "SwordShield_Move", "UnArmed": "SwordShield_Unarmed_Move"}, "Falling": {"Armed": "SwordShield_Fall", "UnArmed": "SwordShield_Unarmed_Fall"}, "Attached": {"Armed": "SwordShield_Ride", "UnArmed": "SwordShield_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "SwordShield_Hurt", "UnArmed": "SwordShield_Hurt"}, "Blow": {"Armed": "SwordShield_Blow", "UnArmed": "SwordShield_Blow"}, "Frozen": {"Armed": "SwordShield_Frozen", "UnArmed": "SwordShield_Frozen"}, "Bounced": {"Armed": "SwordShield_Bounced", "UnArmed": "SwordShield_Bounced"}, "Dead": {"Armed": "SwordShield_Dead", "UnArmed": "SwordShield_Dead"}, "Landing": {"Armed": "SwordShield_JustFall", "UnArmed": "SwordShield_Unarmed_JustFall"}, "SecondWind": {"Armed": "SwordShield_SecWind", "UnArmed": "SwordShield_SecWind"}, "GetUp": {"Armed": "SwordShield_RevivedOnSecWind", "UnArmed": "SwordShield_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 890], "BeStrikeRate": 1.0, "CriticalChance": 0.1, "CriticalRate": 1.5, "AirDodgePoint": 1}, "WeaponType": "SwordShield", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "ChangeToSwordsman", "ClassBuff": [{"Id": "Rise_FireAoe", "Stack": 1, "Infinity": true}], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________剑盾徒手基础动作________________________________"}, {"说明": "剑盾徒手走路站立", "Id": "SwordShield_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge", "SwordShield_Aim", "SwordShield_DrawWeapon", "Unarm_UseItem", "SwordShield_DrawAttack", "Interactive", "SwordShield_SkillAttack"], "1": ["SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge", "SwordShield_Aim", "SwordShield_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON><PERSON><PERSON>/UnarmedMove"]}}, {"说明": "剑盾徒手起跳", "Id": "SwordShield_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "SwordShield_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Air_DrawAttack", "SwordShield_AirSkillAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Player/Ji<PERSON><PERSON>/Movement/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "剑盾徒手翻滚", "Id": "SwordShield_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "剑盾徒手下落", "Id": "SwordShield_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "SwordShield_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON><PERSON><PERSON>/UnarmedFall"]}, "Priority": 1}, {"说明": "剑盾徒手下落着地", "Id": "SwordShield_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "SwordShield_Unarmed_Jump", "Interactive", "SwordShield_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Player/Ji<PERSON><PERSON>/Movement/UnarmedJustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Movement/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "剑盾收刀", "Id": "SheathSwordShield", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "SwordShield_SheathWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Action/SheathWeapon"]}}, {"说明": "剑盾拔刀", "Id": "DrawSwordShield", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "SwordShield_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Action/DrawWeapon"]}}, {"Line": "_______________________________剑盾(LevelSquencer)动作________________________________"}, {"说明": "Rogue新手教程开始动作", "Id": "Rogue_TeachStart", "Cmds": ["Rogue_TeachStart"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/Rogue_TeachStart"]}, "InitAction": true}, {"说明": "Rogue大厅开场动作", "Id": "Rogue_Hall_Begin", "Cmds": ["Rogue_Hall_Begin"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Action/Rogue_Begin_Hall"]}, "InitAction": true}, {"说明": "Rogue大厅重生动作", "Id": "<PERSON>_<PERSON>_Respawn", "Cmds": ["<PERSON>_<PERSON>_Respawn"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Unarmed_Jump", "SwordShield_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Action/Rogue_Begin_DeathRespawn_Hall"]}, "InitAction": true}, {"说明": "Rogue房间开始动作", "Id": "Rogue_Room_Start", "Cmds": ["Rogue_Room_Start"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/Rogue_Begin_Dungeon"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_01", "Cmds": ["Rogue_SecondRoundEndSeq_01"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Sequence"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/Rogue_SecondRoundEndSeq_01"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_02", "Cmds": ["Rogue_SecondRoundEndSeq_02"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/Rogue_SecondRoundEndSeq_02"]}, "InitAction": true}, {"说明": "Rogue31关最终结束动画", "Id": "Rogue_FinalSeq", "Cmds": ["Rogue_FinalSeq"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/Rogue_FinalSeq"]}, "InitAction": true}, {"Line": "_______________________________剑盾(持武器)基础动作________________________________"}, {"Id": "SwordShield_Move", "Cmds": ["SwordShield_Move"], "Tags": [{"Tag": "SwordShield_Move", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive", "Sequence"], "1": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive", "Sequence"], "2": ["SwordShield_Attack_GSN1", "SwordShield_Attack_GSH1"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Ji<PERSON><PERSON>/Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON><PERSON><PERSON>/Defense", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/<PERSON><PERSON><PERSON>/Defense"]}}, {"Id": "SwordShield_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_HurtCounter"], "1": ["SwordShield_Dodge"], "2": ["SwordShield_QS_B"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Hit/Hurt_Air"]}}, {"Id": "SwordShield_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_QS_B"], "1": ["SwordShield_QS_F"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Blow_Front"]}}, {"Id": "SwordShield_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/<PERSON>im/<PERSON><PERSON>/Player/Ji<PERSON><PERSON>/Hit/Dead"]}, "InitAction": true}, {"Id": "SwordShield_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "SwordShield_Jump", "From": 0}, {"Tag": "SwordShield_Dodge", "From": 0}, {"Tag": "SwordShield_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_Air_Dodge_Step"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Movement/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "SwordShield_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Ji<PERSON><PERSON>/Fall"]}, "Priority": 1}, {"Id": "SwordShield_JustFall", "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump", "SwordShield_Dodge", "Interactive", "SwordShield_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Player/Ji<PERSON><PERSON>/Movement/JustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Movement/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "SwordShield_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Move", "SwordShield_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "SwordShield_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"]}, "Balance": 6, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON><PERSON><PERSON>/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "SwordShield_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON><PERSON><PERSON>/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "SwordShield_Air_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Air_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_Air_Dodge_Step_Second"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/Air_Dodge_Dash"]}, "InitAction": true}, {"Id": "SwordShield_Air_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "SwordShield_Air_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/Air_Dodge_Dash_Second"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "SwordShield_Just_Dodge_Success", "Cmds": ["_"], "Tags": [{"Tag": "SwordShield_Just_Dodge_Success", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_SkillAttack", "SwordShield_Dodge_Step", "SwordShield_Just_Dodge_CounterAtk", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Dodge/Dodge_F_Just_Success"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "SwordShield_Just_Dodge_Success_CounterAtk", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "SwordShield_Just_Dodge_CounterAtk", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"], "1": ["SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_JustDodge_CounterAtk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "SwordShield_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "SwordShield_QS_B", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Dodge", "SwordShield_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身动作前翻", "Id": "SwordShield_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "SwordShield_QS_F", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "SwordShield_Dodge", "SwordShield_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "SwordShield_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Player/Ji<PERSON><PERSON>/Hit/SecondWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "SwordShield_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 999, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Hit/RevivedOnSecondWind"]}}, {"Line": "_______________________________剑盾_防御_动作_______________________________"}, {"Id": "Swordsman_Defense", "Cmds": ["Aim"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "SwordShield_Defense", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step"], "2": ["SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3", "SwordShield_InitAttack", "SwordShield_SkillAttack"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense"]}}, {"说明": "防御成功", "Id": "Swordsman_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Dodge", "SwordShield_Defense"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "MontageAnimPickFunc.GetActionByPriorityDistance(4)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_Success_Big", "ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_Success_Small"]}, "Cost": {"SP": 0, "ReplaceAction": "Swordsman_Defense_Success_Broken"}}, {"说明": "防御成功", "Id": "Swordsman_Defense_Success2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Dodge", "SwordShield_Defense"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "MontageAnimPickFunc.GetActionByPriorityDistance(4)", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_Success_Small2"]}, "Cost": {"SP": 0, "ReplaceAction": "Swordsman_Defense_Success_Broken"}}, {"说明": "防御成功_但体力不足_破防", "Id": "Swordsman_Defense_Success_Broken", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_Success_Broken"]}}, {"说明": "完美防御_成功(justblock)", "Id": "Swordsman_Defense_JustBlock", "Cmds": [], "Tags": ["SwordShield_JustBlock"], "BeCancelledTags": {"0": ["SwordShield_JustBlock_Success_Attack"], "1": ["SwordShield_Dodge_Step"], "2": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "IsInvincibleOnFirstFrame": "true", "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_JustBlock_Success"]}}, {"说明": "*完美防御_成功的后续动作_击退", "Id": "Swordsman_Defense_JustBlock_Success_Attack1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "SwordShield_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x", "AwakenSkill", "UseArtifact"], "1": ["SwordShield_Dodge_Step"], "2": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "IsInvincibleOnFirstFrame": "true", "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/<PERSON><PERSON><PERSON>/Battle/G_Defense_JustBlock_Counter"]}}, {"Line": "_______________________________剑盾特殊动作________________________________"}, {"Line": "_______________________________剑盾受击(特殊)动作________________________________"}, {"Id": "SwordShield_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Hit/Hurt_Frozen"]}}, {"Line": "_______________________________剑盾基础(附加)动作________________________________"}, {"说明": "剑盾弹刀动作", "Id": "SwordShield_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Jump", "SwordShield_Dodge", "SwordShield_Aim", "SwordShield_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/<PERSON>im/<PERSON><PERSON>/Player/<PERSON><PERSON><PERSON>/Battle/Bounced"]}}, {"说明": "瞄准动作", "Id": "SwordShield_Aim", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "SwordShield_Defense", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack"], "1": ["SwordShield_Dodge"], "2": ["SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense"]}}, {"Line": "_______________________________剑盾触发动作________________________________"}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Swordsman_DashSting_UpSlash_C", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashSting_UpSlash_C"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Sword<PERSON>_DashSting_ShieldSmash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashSting_ShieldSmash_C"]}}, {"Line": "_______________________________法器反击_______________________________"}, {"说明": "法器反击成功了就自动变成这个了(仅用于justblock)", "Id": "FrozenParry_JustBlock", "Cmds": [], "Tags": [{"Tag": "FrozenParry_JustBlock", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Action/MagicItem/MagicItem_Parry_CounterAtk"]}}], "RogueBattleActions": [{"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jianke/Action/PickUp"]}}, {"Line": "_______________________________剑盾_普通攻击_动作_______________________________"}, {"Line": "_______________________________剑盾_普攻_地面_______________________________"}, {"Id": "Swordsman_LAttack01", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_LAttack1", "From": 0}, {"Tag": "SwordShield_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_LAttack2", "SwordShield_LAttack2_T1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["SwordShield_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash0"]}, "InitAction": true}, {"Id": "Swordsman_LAttack02", "Cmds": [], "Tags": [{"Tag": "SwordShield_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash1"]}, "InitAction": true}, {"Id": "Swordsman_LAttack03", "Cmds": [], "Tags": [{"Tag": "SwordShield_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash2"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击1", "Id": "Swordsman_AttackB1", "Cmds": [], "Tags": [{"Tag": "SwordShield_AttackB1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_AttackB2"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_SlashB1"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击2", "Id": "Swordsman_AttackB2", "Cmds": [], "Tags": [{"Tag": "SwordShield_AttackB2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_AttackB3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_SlashB2"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击3", "Id": "Swordsman_AttackB3", "Cmds": [], "Tags": [{"Tag": "SwordShield_AttackB3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_AttackB4"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_SlashB3"]}, "InitAction": true}, {"说明": "剑盾分支普通攻击4", "Id": "Swordsman_AttackB4", "Cmds": [], "Tags": [{"Tag": "SwordShield_AttackB4", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_SlashB4"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击2", "Id": "Swordsman_LAttack02_T1", "Cmds": [], "Tags": [{"Tag": "SwordShield_LAttack2_T1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack3_T1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash1_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击3", "Id": "Swordsman_LAttack03_T1", "Cmds": [], "Tags": [{"Tag": "SwordShield_LAttack3_T1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack4_T1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash2_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击4", "Id": "Swordsman_LAttack04_T1", "Cmds": [], "Tags": [{"Tag": "SwordShield_LAttack4_T1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_LAttack1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Slash3_T1"]}, "InitAction": true}, {"Line": "_______________________________剑盾_普攻_空中_______________________________"}, {"Id": "Swordsman_AirAttack1", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirAttack1", "From": 0}, {"Tag": "SwordShield_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirAttack2", "SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/A_Slash0"]}, "InitAction": true}, {"Id": "Swordsman_AirAttack2", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirAttack1"], "1": ["_"], "2": ["SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/A_Slash1"]}, "InitAction": true}, {"说明": "*完美防御_成功的后续动作_击飞", "Id": "Swordsman_Defense_JustBlock_Success_Attack2", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "IsInvincibleOnFirstFrame": "true", "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/G_ShieldUpperStrike"]}}, {"说明": "*完美防御_成功的后续动作_突袭", "Id": "Swordsman_Defense_JustBlock_Success_Attack3", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "IsInvincibleOnFirstFrame": "true", "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/G_ShieldDashSlash"]}}, {"说明": "*完美防御_成功的后续动作_下砸", "Id": "Swordsman_Defense_JustBlock_Success_Attack4", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem", "SwordShield_Defense_Attack1x", "SwordShield_Defense_Attack2x", "SwordShield_Defense_Attack3x"], "1": ["SwordShield_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "IsInvincibleOnFirstFrame": "true", "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_GroundSmash"]}}, {"Line": "_______________________________剑盾战斗动作_______________________________"}, {"Line": "_______________________________剑盾_技能A_地面Action2_______________________________"}, {"Id": "Swordsman_RiseSlash", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_AirSkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_RiseSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Swordsman_RiseComboSlash", "Cmds": ["_"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Rise2Slash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Swordsman_UpSlash", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_UpSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_技能A_空中Action2_______________________________"}, {"Id": "Swordsman_AirDownSlashAttack1", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/A_DashSlash_FallDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Swordsman_AirSlashAttack", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/A_DownSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_技能B_地面Action3_______________________________"}, {"Id": "Swordsman_DashSlash", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_DashAttack2", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/G_DashSlash"]}, "Cost": {"MP": 0}}, {"Id": "Swordsman_DashSlash2", "Cmds": [], "Tags": [{"Tag": "SwordShield_DashAttack2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashSlash1"]}, "Cost": {"MP": 0}}, {"Id": "Swordsman_DashSting_UpSlash", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_DashAttack2", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashSting_UpSlash"]}, "Cost": {"MP": 0}}, {"Id": "Sword<PERSON>_DashSting_ShieldSmash", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_DashAttack2", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashSting_ShieldSmash"]}, "Cost": {"MP": 0}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Swordsman_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_RiseSlash"]}}, {"Id": "Swordsman_DashShield", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_DashShield"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_技能B_空中Action3_______________________________"}, {"Id": "Swordsman_AirDashSting", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/A_DashSting_Forward"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_防御_动作_______________________________"}, {"说明": "防御时Action1的动作", "Id": "Swordsman_Defense_Attack1", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_DrawAttack", "From": 0}, {"Tag": "SwordShield_Defense_Attack1", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense_Attack1_2", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/G_ShieldSweapSmash0"]}, "InitAction": true}, {"说明": "防御时Action1的连招动作", "Id": "Swordsman_Defense_Attack1_2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Defense_Attack1_2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "Swordsman_Defense_Attack1", "SwordShield_Defense_Attack2", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_ShieldSweapSmash1"]}, "InitAction": true}, {"说明": "防御时Action2的动作", "Id": "Swordsman_Defense_Attack2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "SwordShield_Defense_Attack1", "SwordShield_Defense_Attack3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/G_ShieldUpperSmash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "防御时Action3的动作", "Id": "Swordsman_Defense_Attack3", "Cmds": [], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_Defense_Attack3", "From": 0.0}, {"Tag": "SwordShield_SkillAttack", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense", "SwordShield_Defense_Attack1", "SwordShield_Defense_Attack2"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_ShieldChargeSmash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "防御时Action2的动作", "Id": "Swordsman_Defense_Attack2B", "Cmds": [], "Tags": [{"Tag": "SwordShield_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Defense"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_InitAttack", "SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/G_Defense_CounterSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_技能C_空中Action4_______________________________"}, {"Id": "Swordsman_AirShieldSmash", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_AirInitAttack", "SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/A_ShieldSmash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Swordsman_AirDownShieldSmash", "Cmds": [], "Tags": [{"Tag": "SwordShield_AirInitAttack", "From": 0}, {"Tag": "SwordShield_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/A_ShieldSmash_FallDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________剑盾_动作连招式_轻重击_______________________________"}, {"说明": "剑盾地面普攻1", "Id": "Swordsman_Attack_GN1", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN1", "From": 0}, {"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN2", "SwordShield_Attack_GH2"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["SwordShield_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GN1"]}, "InitAction": true}, {"说明": "剑盾地面普攻2", "Id": "Swordsman_Attack_GN2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN2", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN3", "SwordShield_Attack_GH3"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GN2"]}, "InitAction": true}, {"说明": "剑盾地面普攻3", "Id": "Swordsman_Attack_GN3", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN3", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN4", "SwordShield_Attack_GH4"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_GN3"]}, "InitAction": true}, {"说明": "剑盾地面普攻4", "Id": "Swordsman_Attack_GN4", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN4", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN5", "SwordShield_Attack_GH5"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_GN4"]}, "InitAction": true}, {"说明": "剑盾地面普攻5", "Id": "Swordsman_Attack_GN5", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN5", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_GN5"]}, "InitAction": true}, {"说明": "剑盾地面普攻6", "Id": "Swordsman_Attack_GN6", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN6", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN7", "SwordShield_Attack_GH7"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_GN6"]}, "InitAction": true}, {"说明": "剑盾地面普攻7", "Id": "Swordsman_Attack_GN7", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GN7", "From": 0.0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_GN7"]}, "InitAction": true}, {"说明": "剑盾地面重攻1", "Id": "Swordsman_Attack_GH1", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH1", "From": 0}, {"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻2", "Id": "Swordsman_Attack_GH2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻3", "Id": "Swordsman_Attack_GH3", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH3", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻4", "Id": "Swordsman_Attack_GH4", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH4", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH4"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻5", "Id": "Swordsman_Attack_GH5", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH5", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH5"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻6", "Id": "Swordsman_Attack_GH6", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH6", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH6"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面重攻7", "Id": "Swordsman_Attack_GH7", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GH7"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面空中普攻1", "Id": "Swordsman_Attack_AN1", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_AN1", "From": 0}, {"Tag": "SwordShield_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_AN2", "SwordShield_Attack_AH1", "SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_AN1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面空中普攻2", "Id": "Swordsman_Attack_AN2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_AN2", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_AN3", "SwordShield_Attack_AH1", "SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_AN2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面空中普攻3", "Id": "Swordsman_Attack_AN3", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_AN3", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_AH1", "SwordShield_AirSkillAttack", "SwordShield_Air_Dodge_Step"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Ji<PERSON><PERSON>/Battle/Attack_AN3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面空中重攻1", "Id": "Swordsman_Attack_AH1", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_AH1", "From": 0}, {"Tag": "SwordShield_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "Unarm_UseItem"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_AH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面技能1", "Id": "Swordsman_Attack_GS1", "Cmds": [], "Tags": [{"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "SwordShield_InitAttack", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GS1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "剑盾地面技能2", "Id": "Swordsman_Attack_GS2", "Cmds": [], "Tags": [{"Tag": "SwordShield_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Attack_GN1", "SwordShield_Attack_GH1"], "1": ["SwordShield_Dodge_Step", "SwordShield_Defense"], "2": ["SwordShield_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Jian<PERSON>/Battle/Attack_GS2"]}, "InitAction": true, "Cost": {"MP": 0}}]}], "Buff": [], "Aoe": []}