{"DebugConfig": [{"CameraSpringMoveSpeedLimit": 1000.0, "CameraMoveSpeedLimit": 2000.0, "ShowPopText": true, "StartFall": 0.44, "JumpFall": 0.01, "FreezingRate": 0.01, "FreezingMin": 0.01, "FreezingPassedThreshold": 1, "FreezingMinEnemyCount": 10, "IncreaseMobPropsWithLevel": false, "RelicDiceWeight": 1.0, "NewRelicDiceWeight": 1.0, "AntiRelicDiceWeight": 0.1, "AntiRelicDice": false, "RogueItemDamageRecoverBase": 0.1, "RogueItemDamageRecoverOnceMax": 300.0, "PopText": [{"Line": "____________________ SceneItem ____________________"}, {"Id": "SceneItemDamaged_Physical", "R": 230, "G": 230, "B": 230, "Size": 28, "Priority": 0}, {"Id": "SceneItemDamaged_Elemental", "R": 230, "G": 230, "B": 230, "Size": 32, "Priority": 0}, {"Id": "SceneItem_Attack_Normal_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 3}, {"Id": "SceneItem_Attack_Weak_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 32, "Priority": 3}, {"Id": "SceneItem_Attack_Normal_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 22, "Priority": 3}, {"Id": "SceneItem_Attack_Weak_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 32, "Priority": 3}, {"Id": "SceneItem_Attack_Normal_Player_Physical", "R": 255, "G": 13, "B": 13, "Size": 28, "Priority": 4}, {"Id": "SceneItem_Attack_Weak_Player_Physical", "R": 255, "G": 13, "B": 13, "Size": 32, "Priority": 4}, {"Id": "SceneItem_Attack_Normal_Player_Elemental", "R": 255, "G": 13, "B": 13, "Size": 28, "Priority": 4}, {"Id": "SceneItem_Attack_Weak_Player_Elemental", "R": 255, "G": 13, "B": 13, "Size": 32, "Priority": 4}, {"Line": "____________________ Player Enemy ____________________"}, {"Id": "Player_Attack_Normal_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 3}, {"Id": "Player_Attack_Weak_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 34, "Priority": 3}, {"Id": "Player_Attack_Normal_Enemy_Elemental", "R": 255, "G": 186, "B": 53.5, "Size": 25, "Priority": 3}, {"Id": "Player_Attack_Weak_Enemy_Elemental", "R": 255, "G": 186, "B": 53.5, "Size": 35, "Priority": 3}, {"Id": "Player_Attack_Normal_Enemy_Elemental_Senior", "R": 255, "G": 112, "B": 0, "Size": 28, "Priority": 5}, {"Id": "Player_Attack_Weak_Enemy_Elemental_Senior", "R": 255, "G": 112, "B": 0, "Size": 36, "Priority": 5}, {"Line": "____________________ Enemy Player ____________________"}, {"Id": "Enemy_Attack_Normal_Player_Physical", "R": 255, "G": 13, "B": 13, "Size": 28, "Priority": 4}, {"Id": "Enemy_Attack_Weak_Player_Physical", "R": 255, "G": 13, "B": 13, "Size": 32, "Priority": 4}, {"Id": "Enemy_Attack_Normal_Player_Elemental", "R": 255, "G": 13, "B": 13, "Size": 28, "Priority": 4}, {"Id": "Enemy_Attack_Weak_Player_Elemental", "R": 255, "G": 13, "B": 13, "Size": 32, "Priority": 4}, {"Line": "____________________ Enemy Enemy ____________________"}, {"Id": "Enemy_Attack_Normal_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 3}, {"Id": "Enemy_Attack_Weak_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 32, "Priority": 3}, {"Id": "Enemy_Attack_Normal_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 22, "Priority": 3}, {"Id": "Enemy_Attack_Weak_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 32, "Priority": 3}, {"Line": "____________________ Player Player ____________________"}, {"Id": "Player_Heal_Normal_Player_Physical", "R": 75, "G": 255, "B": 75, "Size": 25, "Priority": 4}, {"Id": "Player_He<PERSON>_Weak_Player_Physical", "R": 75, "G": 255, "B": 75, "Size": 32, "Priority": 4}, {"Id": "Player_Heal_Normal_Player_Elemental", "R": 75, "G": 255, "B": 75, "Size": 25, "Priority": 4}, {"Id": "Player_Heal_Weak_Player_Elemental", "R": 75, "G": 255, "B": 75, "Size": 32, "Priority": 4}, {"Id": "close_Player_Attack_Normal_Player_Physical", "R": 200, "G": 10, "B": 8, "Size": 22, "Priority": 4}, {"Id": "close_Player_Attack_Weak_Player_Physical", "R": 200, "G": 10, "B": 8, "Size": 26, "Priority": 4}, {"Line": "____________________ Enemy NPC ____________________"}, {"Id": "close_Enemy_Attack_Normal_NPC_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 0}, {"Id": "close_Enemy_Attack_Weak_NPC_Physical", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 0}, {"Id": "close_Enemy_Attack_Normal_NPC_Elemental", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 0}, {"Id": "close_Enemy_Attack_Weak_NPC_Elemental", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 0}, {"Line": "____________________ Player NPC ____________________"}, {"Id": "close_Player_Attack_Normal_NPC_Physical", "R": 228, "G": 228, "B": 228, "Size": 22, "Priority": 3}, {"Id": "close_Player_Attack_Weak_NPC_Physical", "R": 216, "G": 112, "B": 6, "Size": 26, "Priority": 3}, {"Id": "close_Player_Attack_Normal_NPC_Elemental", "R": 228, "G": 228, "B": 228, "Size": 22, "Priority": 3}, {"Id": "close_Player_Attack_Weak_NPC_Elemental", "R": 216, "G": 112, "B": 6, "Size": 26, "Priority": 3}, {"Line": "____________________ NPC Player ____________________"}, {"Id": "close_N<PERSON>_Heal_Normal_Player_Physical", "R": 75, "G": 255, "B": 75, "Size": 22, "Priority": 0}, {"Id": "close_<PERSON><PERSON>_<PERSON><PERSON>_Weak_Player_Physical", "R": 75, "G": 255, "B": 75, "Size": 26, "Priority": 0}, {"Id": "close_NPC_Attack_Normal_Player_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 1}, {"Id": "close_NPC_Attack_Weak_Player_Physical", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 1}, {"Id": "close_NPC_Attack_Normal_Player_Elemental", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 1}, {"Id": "close_NPC_Attack_Weak_Player_Elemental", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 1}, {"Line": "____________________ NPC Enemy ____________________"}, {"Id": "NPC_Attack_Normal_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 2}, {"Id": "NPC_Attack_Weak_Enemy_Physical", "R": 230, "G": 230, "B": 230, "Size": 32, "Priority": 2}, {"Id": "NPC_Attack_Normal_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 22, "Priority": 2}, {"Id": "NPC_Attack_Weak_Enemy_Elemental", "R": 255, "G": 112, "B": 0, "Size": 32, "Priority": 2}, {"Line": "____________________ NPC NPC ____________________"}, {"Id": "close_NPC_Attack_Normal_NPC_Physical", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 0}, {"Id": "close_NPC_Attack_Weak_NPC_Physical", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 0}, {"Id": "close_NPC_Attack_Normal_NPC_Elemental", "R": 230, "G": 230, "B": 230, "Size": 22, "Priority": 0}, {"Id": "close_NPC_Attack_Weak_NPC_Elemental", "R": 230, "G": 230, "B": 230, "Size": 26, "Priority": 0}], "SP_FreezeDur_Short": 1, "SP_FreezeDur_Long": 3, "SP_RecoverySpeed": 600, "SP_SprintConsumeSpeed": 200, "MP_RecoverySpeed": 400, "MP_FreezeDur": 1, "TempFloat": 3, "CameraOffset": "X=20 Y=100 Z=50", "CameraArmLength": 450, "ShowAttackBox": false, "ShowNormalHitBox": false, "ShowGuardHitBox": false, "ShowJustDodgeBox": false, "Version": "Demo 0.1"}]}