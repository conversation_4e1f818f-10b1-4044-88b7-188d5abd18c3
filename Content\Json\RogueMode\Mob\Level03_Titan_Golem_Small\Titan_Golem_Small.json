{"Mob": [{"Id": "<PERSON>_<PERSON>_Golem_Small", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "Titan_Golem_SmallMoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Golem/Titan_Golem/Titan_Golem_Small", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "泰坦魔俑(小)", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 500, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 6, "PAtk": 9, "Balance": 10, "MoveSpeed": [100, 150, 300], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Titan_Golem/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Titan_Golem/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Golem/Titan_Golem/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Break_Down"]}, "InitAction": true}, {"说明": "中距离攻击1", "Id": "NormalAttack_X1", "Cmds": ["NormalAttack_X1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem_Small/Battle/NormalAttack_X1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_X2", "Cmds": ["NormalAttack_X2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem_Small/Battle/NormalAttack_X2"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem_Small/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "左走走", "Id": "Action_Walk_Left", "Cmds": ["Action_Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Walk_Left"]}, "InitAction": true}, {"说明": "右走走", "Id": "Action_Walk_Right", "Cmds": ["Action_Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Walk_Right"]}, "InitAction": true}]}]}