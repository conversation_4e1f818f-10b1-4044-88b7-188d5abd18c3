{"Class": [{"说明": "双刀战士", "Id": "BladeDancer", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "<PERSON><PERSON><PERSON>", "Swordsman"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "TwinSword_Move", "Unarmed": "TwinSword_Unarmed_Move"}, "Flying": {"Armed": "TwinSword_Move", "Unarmed": "TwinSword_Unarmed_Move"}, "Falling": {"Armed": "TwinSword_Fall", "Unarmed": "TwinSword_Unarmed_Fall"}, "Attached": {"Armed": "TwinSword_Ride", "Unarmed": "TwinSword_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "TwinSword_Hurt", "UnArmed": "TwinSword_Hurt"}, "Blow": {"Armed": "TwinSword_Blow", "UnArmed": "TwinSword_Blow"}, "Frozen": {"Armed": "TwinSword_Frozen", "UnArmed": "TwinSword_Frozen"}, "Bounced": {"Armed": "TwinSword_Bounced", "UnArmed": "TwinSword_Bounced"}, "Dead": {"Armed": "TwinSword_Dead", "UnArmed": "TwinSword_Dead"}, "Landing": {"Armed": "TwinSword_JustFall", "UnArmed": "TwinSword_Unarmed_JustFall"}, "SecondWind": {"Armed": "TwinSword_SecWind", "UnArmed": "TwinSword_SecWind"}, "GetUp": {"Armed": "TwinSword_RevivedOnSecWind", "UnArmed": "TwinSword_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "TwinSword", "DefaultWeapons": ["Skeleton_Sword", "Skeleton_Sword"], "ActionOnChangeTo": "ChangeToBladeDancer", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________双剑徒手基础动作________________________________"}, {"说明": "双剑徒手走路站立", "Id": "TwinSword_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge", "TwinSword_Aim", "TwinSword_DrawWeapon", "Unarm_UseItem", "Interactive", "TS_DrawAttack"], "1": ["TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge", "TwinSword_Aim", "TwinSword_DrawWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/BladeDancer_Male/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "双剑徒手起跳", "Id": "Unarmed_TwinSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "TwinSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["TS_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "双剑徒手翻滚", "Id": "TwinSword_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "TwinSword_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_Unarmed_Move", "TwinSword_Unarmed_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_Male/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "双剑徒手下落", "Id": "TwinSword_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "TwinSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/BladeDancer_Male/UnarmedFall"]}, "Priority": 1}, {"说明": "双剑徒手下落着地", "Id": "TwinSword_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "TwinSword_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "双剑收刀", "Id": "SheathTwinSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "TwinSword_ShealthWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/SheathWeapon"]}}, {"说明": "双剑拔刀", "Id": "DrawTwinSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "TwinSword_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/DrawWeapon"]}}, {"Line": "_______________________________双剑(LevelSquencer)动作________________________________"}, {"说明": "双剑_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________双剑(持武器)基础动作________________________________"}, {"Id": "TwinSword_Move", "Cmds": ["TS_Move"], "Tags": [{"Tag": "TS_Move", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"], "1": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/BladeDancer_Male/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/BladeDancer_Male/Move_AimState"]}}, {"Id": "TwinSword_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["TS_Dodge"], "2": ["TwinSword_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Hurt_Air"]}}, {"Id": "TwinSword_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["TwinSword_QS_B"], "1": ["TwinSword_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Blow_Front"]}}, {"Id": "TwinSword_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "TwinSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "TS_Jump", "From": 0}, {"Tag": "TS_Dodge", "From": 0}, {"Tag": "TS_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "TwinSword_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["TS_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/BladeDancer_Male/Fall"]}, "Priority": 1}, {"Id": "TwinSword_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "TS_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/AttachOnTarget"]}}, {"Id": "TwinSword_JustFall", "BeCancelledTags": {"0": ["TS_Move", "TS_Jump", "TS_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "TwinSword_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_Dodge", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_<PERSON>_AJ", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "TwinSword_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "TwinSword_QS_B", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_InitAttack", "TwinSword_SkillAttack", "TwinSword_Dodge", "TwinSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身双剑动作前翻", "Id": "TwinSword_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "TwinSword_QS_F", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_InitAttack", "TwinSword_SkillAttack", "TwinSword_Dodge", "TwinSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "TwinSword_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "TwinSword_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/RevivedOnSecondWind"]}}, {"Id": "TwinSword_Ride", "Cmds": [], "Tags": [{"Tag": "TS_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["TS_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________双剑受击(特殊)动作________________________________"}, {"Id": "TwinSword_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________双剑基础(附加)动作________________________________"}, {"说明": "切换到双剑的耍帅动作，至于帅不帅，反正凑合", "Id": "ChangeToBladeDancer", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/ChangeToBladeDancer1"]}}, {"说明": "双剑弹刀动作", "Id": "TwinSword_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/Bounced"]}}, {"说明": "瞄准动作", "Id": "TS_Aim", "Cmds": [], "Tags": [{"Tag": "TS_Aim", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge", "TS_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/BladeDancer_Aim"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_Male/PickUp"]}}, {"Line": "_______________________________双剑命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "TS_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "TS_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/OrderBuddy/OrderBuddyMoveToTarget"]}}], "RogueBattleActions": [{"Line": "_______________________________双剑_普攻_地面Action1_______________________________"}, {"说明": "普攻1", "Id": "BladeDancer_LAttack01", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_LAttack2"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4", "TS_<PERSON>_<PERSON>_<PERSON>"], "4": ["TS_BranchAttack2"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_Slash0"]}, "InitAction": true}, {"说明": "普攻2", "Id": "BladeDancer_LAttack02", "Cmds": [], "Tags": [{"Tag": "TS_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_LAttack3"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_Slash1"]}, "InitAction": true}, {"说明": "普攻3", "Id": "BladeDancer_LAttack03", "Cmds": [], "Tags": [{"Tag": "TS_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_Slash2"]}, "InitAction": true}, {"说明": "普攻的 JustAttack 1", "Id": "<PERSON><PERSON><PERSON><PERSON>_LAttack_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_SkillAttack", "TS_InitAttack"], "1": ["TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 1, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_Slash_AJ2"]}, "InitAction": true}, {"说明": "普攻的 JustAttack 2", "Id": "BladeDancer_LAttack_AJ2", "Cmds": ["-"], "Tags": [{"Tag": "TS_AttackAJ4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_SkillAttack", "TS_InitAttack"], "1": ["TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 1, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_Slash_AJ1"]}, "InitAction": true}, {"说明": "分支 普攻 2", "Id": "BladeDancer_BranchAttack2", "Cmds": [], "Tags": [{"Tag": "TS_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_BranchAttack3"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_SlashB1"]}, "InitAction": true}, {"说明": "分支 普攻 3", "Id": "BladeDancer_BranchAttack3", "Cmds": [], "Tags": [{"Tag": "TS_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_BranchAttack4"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_SlashB2"]}, "InitAction": true}, {"说明": "分支 普攻 4", "Id": "BladeDancer_BranchAttack4", "Cmds": [], "Tags": [{"Tag": "TS_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_SlashB3_2"]}, "InitAction": true}, {"Line": "_______________________________双剑_普攻_空中Action1_______________________________"}, {"说明": "空中普攻 1", "Id": "BladeDancer_AirLAttack1", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirLAttack1"], "1": ["_"], "2": ["TS_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_Slash0"]}, "InitAction": true}, {"说明": "空中普攻 2", "Id": "BladeDancer_AirLAttack2", "Cmds": [], "Tags": [{"Tag": "TS_AirLAttack1", "From": 0}], "BeCancelledTags": {"0": ["TS_AirLAttack2"], "1": ["_"], "2": ["TS_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_Slash1"]}, "InitAction": true}, {"说明": "空中普攻 3", "Id": "BladeDancer_AirLAttack3", "Cmds": [], "Tags": [{"Tag": "TS_AirLAttack2", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["_"], "2": ["TS_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_Slash2"]}, "InitAction": true}, {"Line": "_______________________________双剑_技能A_地面Action2_______________________________"}, {"说明": "RiseComboSlash，地面砍一下后再升飞攻击并后跳", "Id": "BladeDancer_RiseComboSlash", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_RiseComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "RiseComboSlash击中目标瞬间的Attack Just攻击", "Id": "BladeDancer_RiseComboSlash_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack"], "1": ["TS_Dodge_Step"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_RiseComboSlashAJ"]}, "Cost": {"MP": 0}}, {"说明": "RiseSlash，单次升飞攻击", "Id": "BladeDancer_RiseSlash", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_RiseSlash"]}, "Cost": {"MP": 0}}, {"说明": "RiseSlash击中目标瞬间的Attack Just攻击", "Id": "BladeDancer_RiseSlash_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack"], "1": ["TS_Dodge_Step"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_RiseSlash_AJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能A_空中Action2_______________________________"}, {"Id": "BladeDancer_AirTwiceComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["_"], "2": ["TS_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_2ComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能B_地面Action3_______________________________"}, {"说明": "向前冲刺挥出双刃", "Id": "BladeDancer_DashAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击A，也是向前冲一下", "Id": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step", "TS_Jump"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃，左砍一刀", "Id": "BladeDancer_DashAttackLeft", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "TS_DashAttackLeft", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_DashAttackRight"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_Left"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃，右砍一刀", "Id": "BladeDancer_DashAttackRight", "Cmds": [], "Tags": [{"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "TS_DashAttackRight", "From": 0}], "BeCancelledTags": {"0": ["TS_DashAttackLeft"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_Right"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击B，左砍一刀", "Id": "BladeDancer_DashAttackLeft_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step", "TS_Jump"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_Left_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击C，右砍一刀", "Id": "BladeDancer_DashAttackRight_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step", "TS_Jump"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_Right_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击C，先左砍一刀再右砍一刀", "Id": "BladeDancer_DashAttackLeftRight_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step", "TS_Jump"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashOneSlash_LeftRight_AJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能B_空中Action3_______________________________"}, {"说明": "向下冲刺挥出双刃", "Id": "BladeDancer_AirDashAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_DashOneSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "在空中转一圈向下冲刺挥出双刃", "Id": "BladeDancer_AirDashComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_RiseComboDashSlash"]}, "InitAction": true}, {"Line": "_______________________________双剑_技能C_地面Action4_______________________________"}, {"说明": "双刀乱舞", "Id": "BladeDancer_SwrodDanceComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ3"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_CircleComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "双刀移动乱舞", "Id": "BladeDancer_SwrodDanceComboMoveAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ3"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_ComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "JustAttack双刀乱舞", "Id": "BladeDancer_SwrodDanceDashComboAttack_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/G_DashComboSlashAJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能C_空中Action4_______________________________"}, {"Id": "BladeDancer_AirSwrodDanceComboAttack_Fall", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"]}, "Priority": 4, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_FallCircleComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "BladeDancer_AirSwrodDanceComboAttack_Dash", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack"], "1": ["TS_Dodge_Step"]}, "Priority": 4, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/BladeDancer_Male/Attack/TwinSword/A_DashComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}]}], "Buff": [], "Aoe": []}