{"VideoPath": [{"———————————————————— 剑盾技能 Gerasso Swordman ————————————————————": ""}, {"Id": "Gerasso_Defense_Strike_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Gerasso_Defense_UpperStrike_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "G<PERSON>sso_Defense_DashSlash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Gerasso_Defense_GroundSmash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "G<PERSON>sso_DashSlash_Video", "Path": "MoviesInRogue/Ability_Swordman/DashSlash"}, {"Id": "G<PERSON>sso_RiseSlash_Video", "Path": "MoviesInRogue/Ability_Swordman/RiseSlash"}, {"Id": "Gerasso_ComboRiseSlash_Video", "Path": "MoviesInRogue/Ability_Swordman/SplitSlash"}, {"Id": "Gerasso_ChargeSmash_Video", "Path": "MoviesInRogue/Ability_Swordman/ChargeSmash"}, {"Id": "Gerasso_AirDashSting_Video", "Path": "MoviesInRogue/Ability_Swordman/AirDashSting"}, {"Id": "Gerasso_AirShieldSmash_Video", "Path": "MoviesInRogue/Ability_Swordman/AirShieldSmash"}, {"Id": "Gerasso_AirDownSlash_Video", "Path": "MoviesInRogue/Ability_Swordman/DownStrike"}, {"———————————————————— 大剑技能 Tierdagon Warrior ————————————————————": ""}, {"Id": "Tierdagon_ChargeAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeWhirlwind_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeJumpAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeSlashAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_DashSlash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_StingDash_Up_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_StingDash_Slash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_Parry_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirChargeAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirFallSlash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirStrike_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirDemonSlayer_Video", "Path": "MoviesInRogue/Temp"}, {"———————————————————— 精灵大剑技能 Tierdagon Warrior ————————————————————": ""}, {"Id": "Tierdagon_ChargeAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeWhirlwind_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeJumpAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_ChargeSlashAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_DashSlash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_StingDash_Up_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_StingDash_Slash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_Parry_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirChargeAttack_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirFallSlash_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirStrike_Video", "Path": "MoviesInRogue/Temp"}, {"Id": "Tierdagon_AirDemonSlayer_Video", "Path": "MoviesInRogue/Temp"}, {"———————————————————— 双剑技能 Henrik BladeDancer ————————————————————": ""}, {"Id": "<PERSON>_<PERSON>A<PERSON>ck_Video", "Path": "MoviesInRogue/Ability_BladeDancer/ShadowBlade"}, {"Id": "<PERSON>DashAttackLeftRight_Video", "Path": "MoviesInRogue/Ability_BladeDancer/ShadowMoonBlade"}, {"Id": "<PERSON>_DanceCombo_Video", "Path": "MoviesInRogue/Ability_BladeDancer/DanceBlade"}, {"Id": "<PERSON>_DanceComboMove_Video", "Path": "MoviesInRogue/Ability_BladeDancer/DanceBlade"}, {"Id": "<PERSON>_RiseSlash_Video", "Path": "MoviesInRogue/Ability_BladeDancer/RiseSlash"}, {"Id": "<PERSON>_RiseComboSlash_Video", "Path": "MoviesInRogue/Ability_BladeDancer/RiseComboSlash"}, {"Id": "<PERSON>_AirTwiceCombo_Video", "Path": "MoviesInRogue/Ability_BladeDancer/DoubleAttack"}, {"Id": "<PERSON>_AirDash_Video", "Path": "MoviesInRogue/Ability_BladeDancer/AirSmush"}, {"Id": "Henrik_AirDashCombo_Video", "Path": "MoviesInRogue/Ability_BladeDancer/SwallowWhirl"}, {"Id": "Henrik_AirSwrodDance_Dash_Video", "Path": "MoviesInRogue/Ability_BladeDancer/Windmill"}, {"Id": "<PERSON>_AirSwrodDance_Fall_Video", "Path": "MoviesInRogue/Ability_BladeDancer/WindmillHit"}, {"———————————————————— 长枪技能 Sola Spearman ————————————————————": ""}, {"Id": "Sola_ConsecutiveSpike_Video", "Path": "MoviesInRogue/Ability_Spearman/FuryAttack"}, {"Id": "Sola_SweapAttack_Video", "Path": "MoviesInRogue/Ability_Spearman/SpearDance"}, {"Id": "Sola_DashSpike_Combo_Video", "Path": "MoviesInRogue/Ability_Spearman/VanguardStrike"}, {"Id": "Sola_DashSpike_Rise_Video", "Path": "MoviesInRogue/Ability_Spearman/FishDiveStrike"}, {"Id": "Sola_DashSlash_RiseSlash_Video", "Path": "MoviesInRogue/Ability_Spearman/BackThrust"}, {"Id": "Sola_DashSlash_HitJump_Video", "Path": "MoviesInRogue/Ability_Spearman/AirThrust"}, {"Id": "Sola_BackJumpSlash_DownSlash_Video", "Path": "MoviesInRogue/Ability_Spearman/LeapStrike"}, {"Id": "Sola_BackJumpSlash_HitJump_Video", "Path": "MoviesInRogue/Ability_Spearman/AirStrike"}, {"Id": "Sola_AirDashSpike_Forward_Video", "Path": "MoviesInRogue/Ability_Spearman/VanguardLeap"}, {"Id": "Sola_AirDownSpike_Video", "Path": "MoviesInRogue/Ability_Spearman/VanguardSmash"}, {"Id": "Sola_AirDashSlash_DownSlash_Video", "Path": "MoviesInRogue/Ability_Spearman/AirDance"}, {"Id": "Sola_AirDashSlash_HitJump_Video", "Path": "MoviesInRogue/Ability_Spearman/AirBackThrust"}, {"———————————————————— 觉醒技能 Awake Skill ————————————————————": ""}, {"Id": "DragonSummon", "Path": "MoviesInRogue/AwakeSkill/Dragon_s_Wrath"}, {"Id": "<PERSON><PERSON><PERSON><PERSON>", "Path": "MoviesInRogue/AwakeSkill/God_of_War_s_Might"}, {"Id": "BloodlyThirsty", "Path": "MoviesInRogue/AwakeSkill/Journey_Of_Life"}, {"Id": "Earthquake", "Path": "MoviesInRogue/AwakeSkill/Power_of_the_Lord_of_Destruction"}, {"———————————————————— 法器 Artifact ————————————————————": ""}, {"Id": "FireCannon_Video", "Path": "MoviesInRogue/Artifact/FireCannon"}, {"Id": "FrozenDash_Video", "Path": "MoviesInRogue/Artifact/FrozenDash"}, {"Id": "IceScythe_Video", "Path": "MoviesInRogue/Artifact/IceScythe"}, {"Id": "LightingHammer_Video", "Path": "MoviesInRogue/Artifact/LightingHammer"}, {"Id": "ShadowBlades_Video", "Path": "MoviesInRogue/Artifact/ShadowBlades"}, {"Id": "WindProtect_Video", "Path": "MoviesInRogue/Artifact/WindProtect"}]}