{"说明": "肉鸽特定词条相关的Buff，相当于一些基础词条buff上套一层以限制层数等属性,最终总max是基于基础buff词条的", "Buff": [{"说明": "燃烧:角色每秒受到伤害1点,最高100层", "Id": "Rogue_Burning", "Tag": ["<PERSON>_Harm", "Dot", "Fire", "ShowUI", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "TickTime": 1, "Priority": 0, "MaxStack": 100, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.PlayVFXOnCha_Occur(Burn,ArtResource/ProjectRogue/VFX/Project/ps_Fire_01_01_BoneSocket,Mesh,false,1)"], "OnTick": ["BuffUtils.DamageOverTime(Fire,1,1.5)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Burn)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Burn)"]}, {"说明": "腐败:角色受到的伤害提高，每层6%,最高5层", "Id": "Rogue_Corruption", "Tag": ["<PERSON>_Harm", "DamageModify", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 100, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.PlayVFXOnCha_Occur(Dark,ArtResource/ProjectRogue/VFX/ParagonZ/P_Countess_TeleportArrive_R_Loop_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Dark)"], "OnBeHurt": ["BuffUtils.HurtDamageUp(0.01)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Dark)"]}, {"说明": "暗噬:根据腐败层数造成基础伤害+目标最大生命0.1-0.5%的值，如果斩杀目标则传染周围一圈敌人一层腐败", "Id": "Rogue_DarkDevour", "Tag": ["<PERSON>rm", "DarkDevour"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnOccur": ["RogueBuff.CostAllStackToMaxLifeOffense(1,0,0.15,0.001,5,Darkness)", "BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis2,1,<PERSON>)"], "OnBeKilled": ["BuffUtils.CreateAoeOnBeKilled(Rogue_Aoe_Eminendanis2_2,1,Root)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(DarkDevour)"]}, {"说明": "流血:角色受到额外物理伤害，1+20%攻击者攻击力", "Id": "Rogue_Bloodly", "Tag": ["<PERSON>_Harm", "DamageModify", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.TakeExtraDamageByCasterProp(Physical,1,0.2)"]}, {"说明": "寒霜:角色速度减少,每层6%,最高5层", "Id": "Rogue_Ice", "Tag": ["<PERSON>_Harm", "IceCream", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.AddSubBuffObj(Rogue_ActionSpeedDown,600,0,true)", "BuffUtils.AddSubBuffObj(Rogue_MoveSpeedDown,600,0,true)", "BuffUtils.PlayVFXOnCha_Occur(Ice,ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Freeze_Rooted_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Ice)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Rogue_ActionSpeedDown,600,0,true)", "BuffUtils.RemoveSubBuffObjStack(Rogue_MoveSpeedDown,600,0,true)", "BuffUtils.StopVFXOnCha_Remove(Ice)"]}, {"说明": "特殊寒霜:角色速度减少,每层5%,最高1层", "Id": "Rogue_SpIce", "Tag": ["<PERSON>rm", "IceCream"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.AddSubBuffObj(Rogue_ActionSpeedDown,1000,0,true)", "BuffUtils.AddSubBuffObj(Rogue_MoveSpeedDown,1000,0,true)", "BuffUtils.PlayVFXOnCha_Occur(SpIce,ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Freeze_Rooted_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(SpIce)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Rogue_ActionSpeedDown,1000,0,true)", "BuffUtils.RemoveSubBuffObjStack(Rogue_MoveSpeedDown,1000,0,true)", "BuffUtils.StopVFXOnCha_Remove(SpIce)"]}, {"说明": "蛛丝减速:角色速度减少,每层30%,最高1层", "Id": "Rogue_SpiderWebSpeedDown", "Tag": ["<PERSON>_Harm"], "Priority": 0, "MaxStack": 1, "IgnoreDifferentCaster": true, "OnOccur": ["BuffUtils.AddSubBuffObj(Rogue_ActionSpeedDown,3000,0,true)", "BuffUtils.AddSubBuffObj(Rogue_MoveSpeedDown,3000,0,true)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Rogue_ActionSpeedDown,3000,0,true)", "BuffUtils.RemoveSubBuffObjStack(Rogue_MoveSpeedDown,3000,0,true)"]}, {"说明": "冻结：根据寒霜层数“冻结”敌人，每层增加0.8秒。", "Id": "Rogue_IceCastFrozen", "Tag": ["<PERSON>rm", "Cast", "Frozen"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnOccur": ["RogueBuff.BuffChanceCastById_StackToTime(1,<PERSON>_<PERSON>,Standard_PlayFrozen,0.8,1,true,false)"]}, {"说明": "不消耗寒霜的冻结：根据寒霜层数“冻结”敌人，每层增加0.5秒。", "Id": "Rogue_IceCastFrozenNoCost", "Tag": ["<PERSON>rm", "Cast", "Frozen"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnOccur": ["RogueBuff.BuffChanceCastById_StackToTime(1,<PERSON>_<PERSON>,Standard_PlayFrozen,0.5,1,true,true,false)"]}, {"说明": "电击:角色受到的break伤害增加,每层6%,最高5层", "Id": "Rogue_Thunder", "Tag": ["<PERSON>_Harm", "PropertyDown", "Thunder", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.AddSubBuffObj(Standard_HurtBreakUp,600,0,true)", "BuffUtils.PlayVFXOnCha_Occur(Thunder,ArtResource/ProjectRogue/VFX/ParagonZ/P_Chains_Hooked_Loop_Purple_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Thunder)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Standard_HurtBreakUp,600,0,true)", "BuffUtils.StopVFXOnCha_Remove(Thunder)"]}, {"说明": "风切:角色空中时受到暴击概率提升,每层6%,最高5层", "Id": "Rogue_Wind", "Tag": ["<PERSON>_Harm", "PropertyDown", "Wind", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.AddSubBuffObj(Rogue_Wind_HurtCriticalChanceUp,600,0,true)", "BuffUtils.PlayVFXOnCha_Occur(Wind,ArtResource/ProjectRogue/VFX/ParagonZ/p_Ult_WindzoneEnd_RM_NoFlare_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Wind)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Rogue_Wind_HurtCriticalChanceUp,600,0,true)", "BuffUtils.StopVFXOnCha_Remove(Wind)"]}, {"说明": "风蚀:根据层数每次tick造成一次Offense,不确定使用", "Id": "Rogue_WindErosion", "Tag": ["<PERSON>_Harm", "PropertyDown", "WindErosion"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "TickTime": 0.1, "OnOccur": [], "OnTick": ["RogueBuff.CostStackToOffenseSelf(1,10,0.7,Wind)"], "OnRemoved": []}, {"说明": "耀光:角色造成的伤害减少,每层6%,最高5层", "Id": "Rogue_Light", "Tag": ["<PERSON>_Harm", "PropertyDown", "Light", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 5, "OnOccur": ["RogueBuff.AddBasicElementalBuffDuration(Rogue_BasicElementalDurationUp,0.1)", "BuffUtils.AddSubBuffObj(Standard_DamageDown,600,0,true)", "BuffUtils.PlayVFXOnCha_Occur(Light,ArtResource/ProjectRogue/VFX/ParagonZ/P_Gideon_RMB_CastPortal_NoRune_Yellow_BoneSocket,Mesh,false,1)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Light)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Standard_DamageDown,600,0,true)", "BuffUtils.StopVFXOnCha_Remove(Light)"]}, {"说明": "耀光:角色缴械(其他Component比如AIComponent逻辑中判断该Buff是否存在,或者判断ControlState)", "Id": "Rogue_Flash", "Tag": ["<PERSON>rm", "SoftControl", "Flash", "CanNotAttack"], "ControlState": {"CanAttack": "OutOfControl"}, "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Flash,ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_HitChar_Yellow,Mesh,false,1)", "RogueBuff.BuffTimeCaluTargetResistance(ControlBuffResistance)"], "OnBeKilled": ["BuffUtils.StopVFXOnOnBeKilled_Remove(Flash)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Flash)"]}]}