{"Item": [{"Id": "HealingPotion", "OnUse": {"UseActionId": "DrinkPotion", "RemoveOnUsed": "true", "UseEffects": ["ItemUseEffect.RestoreHealth(0.3)"]}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/HealingPotion", "BindPointId": ["LeftWeapon", "LeftWeapon"], "AppearanceType": "Normal"}], "Durability": 1}, {"Id": "FireBallScroll", "OnUse": {"UseActionId": "FireBallScroll", "RemoveOnUsed": "true", "UseEffects": ["ItemUseEffect.CreateAoeOnRoot(FireAoeByItem,1)"]}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/FireBall_Pot", "BindPointId": ["RightWeapon", "RightWeapon"], "AppearanceType": "Normal"}], "Durability": 1}, {"Id": "Cheese", "OnUse": {"UseActionId": "AimThrowOut", "RemoveOnUsed": "true", "UseEffects": ["ItemUseEffect.ThrowCheese(1500,200,0.5)"]}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/HealingPotion", "BindPointId": ["RightWeapon", "RightWeapon"], "AppearanceType": "Normal"}], "Durability": 1}, {"Id": "<PERSON>ch", "OnUse": {"UseActionId": "<PERSON><PERSON>_<PERSON>ch", "RemoveOnUsed": "true", "UseEffects": []}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Torch", "BindPointId": ["LeftWeapon", "LeftWeapon"], "AppearanceType": "Normal"}], "Durability": 1}, {"说明": "回复满血的啤酒，这是酒馆里面的那个，测试版就靠这个回满了，喝酒喝到一半打断动作可能会导致药水放在背包里，不过也没办法了，就这么地", "Id": "FullyRestoreBeer", "OnUse": {"UseActionId": "DrinkPotion", "RemoveOnUsed": "true", "UseEffects": ["ItemUseEffect.RestoreHealth(2)"]}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Beer", "BindPointId": ["LeftWeapon", "LeftWeapon"], "AppearanceType": "Normal"}], "Durability": 1}]}