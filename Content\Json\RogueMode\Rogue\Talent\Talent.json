{"RogueTalentInfo": [{"说明": "全伤害提升", "id": "DamageUp", "Desc": "DamageUp_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["20", "50", "100", "200", "300", "500"], "EffectBuff": ["Rogue_DamageUp(5)"]}, {"说明": "多给几条命", "id": "AddOtherLife", "Desc": "AddOtherLife_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["500"], "EffectBuff": ["Rogue_OtherLife(1)"]}, {"说明": "房间结束获得金币", "id": "GetCoinOnRoomEnd", "Desc": "GetCoinOnRoomEnd_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["50", "120", "200"], "EffectBuff": ["Rogue_GetCoinOnRoomEnd(5)"]}, {"说明": "最大生命值提升", "id": "CharacterMaxHealthUp", "Desc": "CharacterMaxHealthUp_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["10", "30", "50", "100", "200", "300"], "EffectBuff": ["Rogue_MaxHealthUp(10)"]}, {"说明": "药水回复量提升", "id": "PotionHealUp", "Desc": "PotionHealUp_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["10", "30", "50", "100", "200", "300"], "EffectBuff": ["Rogue_TalentPotionHealLevelUp(10)"]}, {"说明": "休息处回血", "id": "HealOnUpgrade", "Desc": "HealOnUpgrade_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["10", "100", "200", "300", "400", "500"], "EffectBuff": ["Rogue_HealOnUpgrade(10)"]}, {"说明": "游戏开始获得初始金币", "id": "GetCoinOnGameStart", "Desc": "GetCoinOnGameStart_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["50", "100", "200"], "EffectBuff": ["Rogue_GiveMoneyOnGameStart(50)"]}, {"说明": "商店刷新打折", "id": "AddShopRefreshDiscount", "Desc": "AddShopRefreshDiscount_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["10", "100", "200", "300", "500"], "EffectBuff": ["Rogue_ShopRefreshDiscount(10)"]}], "RogueTalentInfo1": [{"分割": "-------------------------------------------以下弃用-----------------------------------------"}, {"分割": "-------------------------------------------以下弃用-----------------------------------------"}, {"分割": "-------------------------------------------以下弃用-----------------------------------------"}, {"分割": "-------------------------------------------以下弃用-----------------------------------------"}, {"说明": "精英房概率增加", "id": "AddEliteRoomPercent", "Desc": "AddEliteRoomPercent_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["100", "300", "1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddEliteRoom(1)"]}, {"说明": "<PERSON>奖励增加", "id": "AddBossReward", "Desc": "AddBossReward_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddBossReward"]}, {"说明": "每个房间结束回血", "id": "HealOnRoomEnd", "Desc": "HealOnRoomEnd_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["30", "80", "150", "250", "350"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_HealOnRoomEnd(3)"]}, {"说明": "多拿一些魂", "id": "GetSoulUp", "Desc": "GetSoulUp_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["30", "80", "150", "300"], "EffectBuff": ["Rogue_GetSoulUp(5)"]}, {"说明": "每个房间结束回觉醒充能", "id": "RecoverApOnRoomEnd", "Desc": "RecoverApOnRoomEnd_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["50", "100", "150"], "EffectBuff": ["Rogue_RecoverApOnRoomEnd(5)"]}, {"说明": "攻击力固定数值提高", "id": "TalentAttackUp", "Desc": "TalentAttackUp_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["50", "100", "200", "300", "500"], "EffectBuff": ["Rogue_AttackUp(2)"]}, {"说明": "对精英特攻", "id": "EliteDamageUp", "Desc": "EliteDamageUp_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["100", "200", "300"], "EffectBuff": ["Rogue_EliteDamageUp(10)"]}, {"说明": "对普通敌人特攻", "id": "NormalDamageUp", "Desc": "NormalDamageUp_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["100", "200", "300"], "EffectBuff": ["Rogue_NormalDamageUp(10)"]}, {"说明": "精英房概率增加", "id": "AddEliteRoomPercent", "Desc": "AddEliteRoomPercent_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["100", "300", "1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddEliteRoom(1)"]}, {"说明": "<PERSON>奖励增加", "id": "AddBossReward", "Desc": "AddBossReward_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddBossReward"]}, {"说明": "每个房间结束回血", "id": "HealOnRoomEnd", "Desc": "HealOnRoomEnd_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["30", "80", "150", "250", "350"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_HealOnRoomEnd(3)"]}, {"说明": "捡到钥匙或魂晶回血", "id": "HealOnGetKeyAndSoul", "Desc": "HealOnGetKeyAndSoul_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["40", "100", "280"], "EffectBuff": ["Rogue_HealOnGetKeyAndSoul(5)"]}, {"说明": "精英房概率增加", "id": "AddEliteRoomPercent", "Desc": "AddEliteRoomPercent_Desc", "Tags": ["Tag_IncreaseResources"], "CostSourceId": "Rogue_Soul", "Costs": ["100", "300", "1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddEliteRoom(1)"]}, {"说明": "<PERSON>奖励增加", "id": "AddBossReward", "Desc": "AddBossReward_Desc", "Tags": ["Tag_DamageIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["1000"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_AddBossReward"]}, {"说明": "每个房间结束回血", "id": "HealOnRoomEnd", "Desc": "HealOnRoomEnd_Desc", "Tags": ["Tag_SurvivalIncrease"], "CostSourceId": "Rogue_Soul", "Costs": ["30", "80", "150", "250", "350"], "ShowAfterSwitch": true, "EffectBuff": ["Rogue_HealOnRoomEnd(3)"]}]}