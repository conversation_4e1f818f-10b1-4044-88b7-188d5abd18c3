{"Dialogs": [{"说明": "Guardian1_FirstDialog", "Id": "Guardian1_FirstDialog", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech0"}, {"Type": "DoAction", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Nope"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech1"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech2"}, {"Type": "DoAction", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_ShortSpeech1"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech3"}], "Selections": []}]}, {"说明": "Guardian1_SpeakOnMine200", "Id": "Guardian1_SpeakOnMine200", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech4"}], "Selections": []}]}, {"说明": "Guardian1_SpeakOnMine120", "Id": "Guardian1_SpeakOnMine120", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Deep_Breath_Sad"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech5"}], "Selections": []}]}, {"说明": "Guardian1_SpeakOnMine60", "Id": "Guardian1_SpeakOnMine60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech6"}], "Selections": []}]}, {"说明": "Guardian1_SpeakOnMine0", "Id": "Guardian1_SpeakOnMine0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech7"}, {"Type": "DoAction", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_ShortSpeech2"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech8"}], "Selections": []}]}, {"说明": "Guardian1_Goblin100", "Id": "Guardian1_Goblin100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Nope"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech14"}], "Selections": []}]}, {"说明": "Guardian1_Goblin60", "Id": "Guardian1_Goblin60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Salute"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech15"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech16"}], "Selections": []}]}, {"说明": "Guardian1_Goblin30", "Id": "Guardian1_Goblin30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech17"}, {"Type": "DoAction", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Nope"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech18"}], "Selections": []}]}, {"说明": "Guardian1_Goblin0", "Id": "Guardian1_Goblin0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Bow"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech19"}], "Selections": []}]}, {"说明": "Guardian1_WereRat100", "Id": "Guardian1_WereRat100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_ShortSpeech1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech9"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech10"}], "Selections": []}]}, {"说明": "Guardian1_WereRat60", "Id": "Guardian1_WereRat60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Deep_Breath_Sad"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech11"}], "Selections": []}]}, {"说明": "Guardian1_WereRat30", "Id": "Guardian1_WereRat30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Salute"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech12"}], "Selections": []}]}, {"说明": "Guardian1_WereRat0", "Id": "Guardian1_WereRat0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardA_Bow"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Guardian1_Name", "Text": "Guardian1_Speech13"}], "Selections": []}]}]}