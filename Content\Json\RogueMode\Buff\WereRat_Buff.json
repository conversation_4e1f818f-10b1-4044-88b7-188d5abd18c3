{"Buff": [{"Id": "WereRat_HasRoared", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 1}, {"Id": "WereRat_EatedCheese", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 1, "OnOccur": ["WereRatBuff.DeleteCheeseAOE()"]}, {"Id": "EatingCheese", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["WereRatBuff.AddAngryBuff()"]}, {"Id": "CanGetCheese", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 10}, {"Id": "WereRat_Angry", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 1}, {"Id": "WereRat_Timid", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 1, "OnOccur": ["WereRatBuff.GetClosetEscapePoint()"]}, {"说明": "鼠人挖矿buff，有这个buff的鼠人会挖矿", "Id": "DoMine", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 20, "OnOccur": []}, {"说明": "受击时会给自己添加累积伤害的buff，buff层数等于受到的伤害值", "Id": "WereRat_BeHurt", "Tag": [], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["WereRatBuff.AccumulateDamage()"]}, {"说明": "记录一段时间累积伤害，伤害值等于buff层数", "Id": "WereRat_AccumulateDamage", "Tag": [], "Priority": 0, "MaxStack": 10000}, {"说明": "记录萨满已经摇过铃了", "Id": "WereRat_HasRung", "Tag": [], "Priority": 0, "MaxStack": 1}, {"说明": "萨满在出生几秒后删除这个buff，这个buff在删除时会给萨满上一个和范围内的鼠人数量相同层数的记录buff，并给所有鼠人上一个死亡时会减少萨满身上记录buff层数的buff", "Id": "WereRat_DelayRecordNum", "Tag": [], "Priority": 0, "MaxStack": 1, "OnRemoved": ["WereRatBuff.RecordWereRatNum(3000)"]}, {"说明": "萨满上记录出生时鼠人数量和当前剩余的数量的buff", "Id": "RecordWereRatNum", "Tag": [], "Priority": 0, "MaxStack": 100, "OnOccur": ["WereRatBuff.RecordMaxStack()"]}, {"说明": "鼠人死亡时会减少这个buff释放者身上的RecordWereRatNum层数", "Id": "RecordWereRatNum", "Tag": [], "Priority": 0, "MaxStack": 1, "OnBeKilled": ["WereRatBuff.ReduceWereRatNumRecord()"]}]}