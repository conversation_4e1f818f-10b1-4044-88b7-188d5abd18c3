{"说明": "觉醒技能相关的Buff", "Buff": [{"说明": "造成伤害产生吸血", "Id": "Awake_BloodlyThirsty", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnOccur": ["AwakeBuff.ActiveAwakeSkill(BloodlyThirsty)", "BuffUtils.PlayVFXOnCha_Occur(Awake_BloodlyThirsty,Temp/Effect/Project/AwakenSkill/P_HolyStateActive_Loop_Green,Body,false,1)"], "OnHit": ["AwakeBuff.BloodlyThirsty(0.15)"], "OnTick": ["AwakeBuff.CheckAwakeBuffCost()"], "OnRemoved": ["AwakeBuff.CloseAwakeSkill(BloodlyThirsty)", "BuffUtils.StopVFXOnCha_Remove(Awake_BloodlyThirsty)"]}, {"说明": "周期性产生地震波", "Id": "Awake_Earthquake", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "OnOccur": ["AwakeBuff.ActiveAwakeSkill(Earthquake)", "BuffUtils.PlayVFXOnCha_Occur(Awake_Earthquake,Temp/Effect/Project/AwakenSkill/P_HolyStateActive_Loop_Flame,Body,false,1)"], "OnTick": ["AwakeBuff.CheckAwakeBuffCost()", "BuffUtils.CreateAoeOnCarrierOnTick(Aoe_Awake_EarthQuakeWave,0.35,0.35,Root)"], "OnRemoved": ["AwakeBuff.CloseAwakeSkill(Earthquake)", "BuffUtils.StopVFXOnCha_Remove(Awake_Earthquake)"]}, {"说明": "提升攻击力,提升造成伤害的break值", "Id": "<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0.1, "Property": [{}, {"PAtk": 10000, "MAtk": 10000}], "OnOccur": ["AwakeBuff.ActiveAwakeSkill(Berserker)", "BuffUtils.PlayVFXOnCha_Occur(<PERSON><PERSON><PERSON>_<PERSON>,Temp/Effect/ParagonZ/FX_Countess/Particles/Abilities/BlinkStrike/FX/P_Countess_TeleportArrive_R_Ash_Decal_Loop,Body,false,1)"], "OnTick": ["AwakeBuff.CheckAwakeBuffCost()"], "OnHit": ["BuffUtils.BreakDamageUp(1)"], "OnRemoved": ["AwakeBuff.CloseAwakeSkill(Berserker)", "BuffUtils.StopVFXOnCha_Remove(Awake_Berserker)", "BuffUtils.RemoveSubBuffObjStack()"]}]}