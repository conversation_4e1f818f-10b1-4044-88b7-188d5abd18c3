{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个燃烧", "Id": "Anim_BurningHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Burning,1,8,true,false)"]}, {"说明": "动画中引发“爆燃", "Id": "Anim_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(1,<PERSON>_<PERSON>,<PERSON>_FireBurst,1,1)"]}, {"说明": "动画中引发无消耗“爆燃", "Id": "Anim_FireBrustHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(1,<PERSON>_<PERSON>,<PERSON>_FireBurst,1,1,<PERSON><PERSON><PERSON><PERSON>,false)"]}, {"说明": "动画中概率引发“爆燃", "Id": "Anim_ChanceFireBrustHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(0,<PERSON>_<PERSON>,<PERSON>_FireBurst,1,1)"]}, {"说明": "动画中引发火焰旋涡", "Id": "Anim_JustDodgeFireSpiral", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Igni11,4,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeFireSpiral,1,0,true)"]}, {"说明": "动画中挨打概率持续给燃烧", "Id": "<PERSON><PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_Burning,1,5,true,false)"]}, {"说明": "动画中完美闪避生成火焰Aoe", "Id": "An<PERSON>_DogeCreateFireSpiral", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Igni11,4,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeFireSpiral,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeFireSpiral,1,0,true)"]}, {"分割": "-------------------------------------------Fire-----------------------------------------"}, {"说明": "造成伤害33%时给对方一个燃烧", "Id": "BurningHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Fire"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_BurningHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_BurningHit,1,0,true)"]}, {"说明": "造成伤害时,若燃烧层数叠加到3层以上时，50%引发“爆燃", "Id": "FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceFireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceFireBrustHit,1,0,true)"]}, {"说明": "下砸时候向下落方向点生成一道火柱", "Id": "Smash_FireBulletAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_3_IgniBullet,10,Root_Bullet,BulletScript.GoStraightAhead(5000))"]}, {"说明": "下砸时候对击中的目标产生一次爆燃", "Id": "Smash_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "位移技能时造成伤害则触发燃烧转换", "Id": "Dash_FireCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "蓄力时被打,反手给个燃烧", "Id": "Power_BeHurtCastBurn", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次爆燃", "Id": "Power_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个延迟引爆的Aoe", "Id": "Doge_FireSpiral", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON>oe_Igni10,2.5,<PERSON>)"]}, {"说明": "击飞技能时对击中的目标产生一次爆燃", "Id": "Rise_FireBrustHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState", "FXRelic_Below_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "造成火元素伤害的时候概率对火元素的主动道具进行额外充能", "Id": "AddItemRecover_Fire", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Fire)"]}, {"说明": "每过TickTime秒发射一个风火轮", "Id": "Launch_FireTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Igni17,0,1.2,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON>rit_FireAoe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Igni18,0.3,Root)"]}]}