{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Survivor_Goblin_Fighter", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 100, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 9}], "OutofCamera": -10, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 2}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 15, "MaxActionCD": 20}, {"Id": "Walk_Left", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 2}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 5}, {"Id": "Walk_Right", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 2}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 5}, {"Id": "Walk_Front", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 2}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 10, "MaxActionCD": 15}, {"Id": "Walk_Back", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 2}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 20, "MaxActionCD": 25}]}]}