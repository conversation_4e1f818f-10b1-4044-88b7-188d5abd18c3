{"Dialogs": [{"说明": "副官（引导士兵）的第一段对话", "Id": "Adjutant<PERSON><PERSON><PERSON>", "FirstClip": "Dialog1a", "NpcId": [], "Clips": [{"Id": "Dialog1a", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "AdjutantGuard_Name", "Text": "Adjutant<PERSON><PERSON>_Speech1a1"}], "Selections": [{}]}]}, {"说明": "副官（引导士兵）的第一段对话", "Id": "Adjutant<PERSON><PERSON><PERSON>", "FirstClip": "Dialog2b", "NpcId": [], "Clips": [{"Id": "Dialog2b", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "AdjutantGuard_Name", "Text": "Adjutant<PERSON><PERSON>_Speech2b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "AdjutantGuard_Name", "Text": "Adjutant<PERSON><PERSON>_Speech2b2"}], "Selections": [{}]}]}]}