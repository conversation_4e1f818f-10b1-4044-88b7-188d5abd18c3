{"说明": "物品显示在游戏的UI上所需要的数据", "范例": {"ThingType": "东西的类型，暂时只支持Character、Buff、Currency、Equipment、Item、Switch、WeaponModel、WeaponObj、Pointer", "ThingId": "对应东西的id，有些玩法也会对此有歧义，比如武器（WeaponObj）在这里就是按照WeaponType来的", "Icon": "使用的图标", "Name": "使用的名称", "Description": "一些描述文字，描述文字针对不同类型的物品，显示方式不同（有些甚至不显示）", "Rank": "<int>档次值，可以理解为0-4对应白绿蓝紫橙"}, "ThingUIInfo": [{"i": "_____________________________________Item____________________________________________"}, {"ThingType": "<PERSON><PERSON>", "ThingId": "<PERSON>ch", "Icon": "ArtResource/UI/Icon/Item/Prop", "Name": "<PERSON>ch", "Description": "就是能着照亮东西的火把，怎么的？", "Rank": 0}, {"ThingType": "<PERSON><PERSON>", "ThingId": "Cheese", "Icon": "ArtResource/UI/Icon/Item/Drug", "Name": "Cheese", "Description": "我自己不吃，给老鼠吃的", "Rank": 0}, {"i": "_____________________________________Currency____________________________________________"}, {"ThingType": "<PERSON><PERSON><PERSON><PERSON>", "ThingId": "TestItem", "Icon": "ArtResource/UI/Icon/Item/Coin", "Name": "拼UI人员辛勤奖章", "Description": "没啥卵用，但他就是有", "Rank": 0}, {"i": "_____________________________________Weapon___Spear______________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Harpoon", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "Harpoon", "Description": "", "Rank": 4, "SceneCameraLocation": "X=-450,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "<PERSON><PERSON><PERSON><PERSON>", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "<PERSON><PERSON><PERSON><PERSON>", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-450,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Golden_Trident", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "Golden_Trident", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-480,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "<PERSON>_Halberd", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "<PERSON>_Halberd", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-470,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "<PERSON><PERSON>_<PERSON>", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "<PERSON><PERSON>_<PERSON>", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-450,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Weapon___GreatSwords_________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "OldFashionedNoble_GreatSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "OldFashionedNoble_GreatSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "DarkKnight_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "DarkKnight_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "DarkKnight_GreatSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "DarkKnight_GreatSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "KingsKnight_GreatSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "KingsKnight_GreatSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Weapon___Shields_____________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Wooden_Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Wooden_Shield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=0", "ModelRotator": "P=-9.084525,Y=24.405729,R=95.997337", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "Elven_Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Elven_Shield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-10.220591,Y=37.888573,R=14.136207", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "Golden_Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Golden_Shield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-10.220591,Y=37.888573,R=14.136207", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "Golden_LargeShield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Golden_LargeShield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-10.220591,Y=37.888573,R=14.136207", "ModelLocation": "X=0,Y=0,Z=0"}, {"i": "_____________________________________Weapon___OneHandSwords_______________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Iron_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Iron_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=-3", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Goblin_<PERSON>ber", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Goblin_<PERSON>ber", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "<PERSON><PERSON><PERSON>", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "<PERSON><PERSON><PERSON>", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Ceremony_Saber", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Ceremony_Saber", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Golden_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Golden_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Weapon___Test____________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "PoleArm", "Icon": "ArtResource/UI/Icon/Class/class_icon_S<PERSON>man_", "Name": "觉醒者长枪", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4}, {"ThingType": "WeaponObj", "ThingId": "BigSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "BigSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-330,Y=0,Z=-6p", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "TestSword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "BigSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-250,Y=0,Z=5", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "TestSpear", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "BigSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-500,Y=2,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "TestShield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "BigSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-250,Y=0,Z=0", "ModelRotator": "P=-10.220591,Y=37.888573,R=14.136207", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "TwinSword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "TwinSword", "Description": "图标上有两把剑，一把任何人都看得到，另一把是聪明人才能看到的，你看到了吗？", "Rank": 3, "SceneCameraLocation": "X=-400,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "OneHandSword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "OneHandSword", "Description": "咱全村最好的剑！-村好剑", "Rank": 3, "SceneCameraLocation": "X=-230,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Shield", "Description": "咱全村最好的盾！-村好盾", "Rank": 3, "SceneCameraLocation": "X=-230,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=-0.268858,Y=19.669182,Z=65.779297"}, {"i": "_____________________________________Equipment____________________________________________"}, {"ThingType": "Equipment", "ThingId": "Test_Warrior_Male", "Icon": "ArtResource/UI/Icon/EquipPart/Head", "Name": "测试头盔", "Description": "就是个测试头盔，嗨害嗨", "Rank": 0, "SceneCameraLocation": "X=-150,Y=0,Z=20", "ModelRotator": "R=-80,P=10,Y=30"}, {"ThingType": "Equipment", "ThingId": "<PERSON><PERSON>", "Icon": "ArtResource/UI/Icon/EquipPart/Head", "Name": "羽毛头盔", "Description": "呆毛头盔，哈哈哈哈哈哈", "Rank": 0, "SceneCameraLocation": "X=-170,Y=0,Z=40", "ModelRotator": "R=-120,P=256,Y=90"}, {"ThingType": "Equipment", "ThingId": "Armor_Coats_Green", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "易爆大氅", "Description": "易燃易爆炸的易爆大氅", "Rank": 0, "SceneCameraLocation": "X=-210,Y=,Z=20", "ModelRotator": "R=-111,P=333,Y=222"}, {"ThingType": "Equipment", "ThingId": "TestBra", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "易爆外衣", "Description": "易燃易爆炸的易爆外衣", "Rank": 0, "SceneCameraLocation": "X=-280,Y=0,Z=50", "ModelRotator": "R=-70,P=350,Y=50"}, {"ThingType": "Equipment", "ThingId": "WarriorGlove", "Icon": "ArtResource/UI/Icon/EquipPart/Arm", "Name": "易爆腕轮", "Description": "易燃易爆炸的易爆腕轮", "Rank": 0, "SceneCameraLocation": "X=-222,Y=0,Z=20", "ModelRotator": "R=-228,P=110,Y=245"}, {"ThingType": "Equipment", "ThingId": "WarriorBoot", "Icon": "ArtResource/UI/Icon/EquipPart/Leg", "Name": "易爆皮裤", "Description": "易燃易爆炸的易爆皮裤", "Rank": 0, "SceneCameraLocation": "X=-360,Y=0,Z=40", "ModelRotator": "R=-250,P=44,Y=84"}, {"ThingType": "Equipment", "ThingId": "WarriorDemon_Armor", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "新版战士皮裤", "Description": "新版战士皮裤，新装备哟", "Rank": 0, "SceneCameraLocation": "X=-360,Y=5,Z=40", "ModelRotator": "R=0,P=0,Y=180"}]}