{"AIScript": [{"Notes": "在矿点附近, 做收好矿石或者丢掉矿石动作", "Id": "WereRatGrenadier_DoActionOnMinePoint", "Condition": ["WereRatCommandoAIScript.CheckIsNearMinePoint(70)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction()"], "Tag": ""}, {"说明": "移动到矿点", "Id": "WereRatGrenadier_MoveToNearestMinePoint", "Condition": ["MobAIScript.CheckMoveToNextPathNode()"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveToNearestMinePoint()"], "Tag": ""}, {"说明": "发呆", "Id": "WereRatGrenadier_Daze", "Condition": [], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(Daze3)"], "Tag": ""}, {"说明": "在右手生成一个炸弹+扑向敌人+爆炸", "Id": "WereRatGrenadier_SelfExplosion", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(2)", "MobAIScript.CheckHPLess(0.3)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyDoAction(SelfExplosion)"]}, {"说明": "往四周扔东西", "Id": "WereRatGrenadier_ThrowAround", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(2)", "MobAIScript.CheckHasBuffStackLess(ThrowBombCD,2)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(ThrowAround)"]}, {"说明": "向敌人投掷1个炸弹", "Id": "WereRatGrenadier_ThrowOneBomb", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(300,3000)", "MobAIScript.CheckHasBuffStackLess(ThrowBombCD,2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(500,3000,ThrowOneBomb)"]}, {"说明": "向敌人连续投掷3个炸弹", "Id": "WereRatGrenadier_ThrowThreeBombs", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(300,3000)", "MobAIScript.CheckFightingWillLevelEquals(2)", "MobAIScript.CheckHasBuffStackLess(ThrowBombCD,2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(500,3000,ThrowThreeBombs)"]}, {"说明": "趴下四肢着地向敌人跑过来", "Id": "WereRatGrenadier_Crawling", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(2000,4000)"], "OnReady": [], "Action": ["WereRatAIScript.RunToViewedClosetEnemy(2000,4000,2800,Crawling_Short,Crawling_<PERSON>)"]}, {"说明": "向右/左踱步", "Id": "WereRatGrenadier_Pace", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(500,2000)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveAroundViewedClosetEnemy(500,2000,RightPace,LeftPace)"]}, {"说明": "后跳", "Id": "WereRatGrenadier_DodgeBack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,500,-45,45)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Dodge_Back)"]}]}