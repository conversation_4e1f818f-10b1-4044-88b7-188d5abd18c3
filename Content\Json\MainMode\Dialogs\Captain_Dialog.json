{"Dialogs": [{"________________________________________11.29__": "__新对话______________________________________________________"}, {"________________________________________主线任务__": "__1b______________________________________________________"}, {"说明": "主线任务1b对话", "Id": "CaptainSmith_MainQuest1b", "FirstClip": "Dialog1b1", "NpcId": [], "Clips": [{"Id": "Dialog1b1", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b2)"}]}, {"Id": "Dialog1b2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b3"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b4", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b3)"}]}, {"Id": "Dialog1b3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b5"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b5"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b6"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b6"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b7", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b4)"}]}, {"Id": "Dialog1b4", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b8"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b8"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b9", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b5)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b11", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b6)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b15", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b7)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory01,3)", "DialogAction.SetRoleSwitchTo(RodianStory02,1)", "DesignerScript/TriggerScript_Country.SetMineWoodenPlankCanDestroy()", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine01,RodianMainLine02)"]}]}, {"Id": "Dialog1b5", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b10"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b10"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b9", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b5)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b11", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b6)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b15", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b7)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory01,3)", "DialogAction.SetRoleSwitchTo(RodianStory02,1)", "DesignerScript/TriggerScript_Country.SetMineWoodenPlankCanDestroy()", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine01,RodianMainLine02)"]}]}, {"Id": "Dialog1b6", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b12"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b12"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b13"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b13"}, {"Type": "PlayAudio", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b14"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b14"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b9", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b5)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b11", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b6)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech1b15", "NextEventFunc": "DialogAction.DirectGoTo(Dialog1b7)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory01,3)", "DialogAction.SetRoleSwitchTo(RodianStory02,1)", "DesignerScript/TriggerScript_Country.SetMineWoodenPlankCanDestroy()", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine01,RodianMainLine02)"]}]}, {"Id": "Dialog1b7", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech1b16"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1b16"}], "Selections": []}], "EndDialogScript": []}, {"________________________________________主线任务__": "__2b______________________________________________________"}, {"说明": "主线任务2b对话", "Id": "Captain<PERSON>mith_MainQuest2b", "FirstClip": "Dialog2b1", "NpcId": [], "Clips": [{"Id": "Dialog2b1", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2b1"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRoleSwitchTo(PlayRodianTownStep01Seq,1)"]}, {"________________________________________主线任务__": "__2c______________________________________________________"}, {"说明": "主线任务2c对话", "Id": "Captain<PERSON><PERSON>_MainQuest2c", "FirstClip": "Dialog2c1", "NpcId": [], "Clips": [{"Id": "Dialog2c1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c1"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c2"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c2"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c3", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c10", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c3)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c16", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c4)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c18", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c5)"}]}, {"Id": "Dialog2c2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c4"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c4"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c5"}, {"Type": "PlayAudio", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c6"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c6"}, {"Type": "PlayAudio", "Id": "Step8", "NextId": "Step9", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c7"}, {"Type": "Speak", "Id": "Step9", "NextId": "Step10", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c7"}, {"Type": "PlayAudio", "Id": "Step10", "NextId": "Step11", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c8"}, {"Type": "Speak", "Id": "Step11", "NextId": "Step12", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c8"}, {"Type": "PlayAudio", "Id": "Step12", "NextId": "Step13", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c9"}, {"Type": "Speak", "Id": "Step13", "NextId": "Step14", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c9"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c14", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c8)"}]}, {"Id": "Dialog2c3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c11"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c11"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c12"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c12"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c13", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c7)"}]}, {"Id": "Dialog2c7", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c2"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c2"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c3", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c10", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c3)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c16", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c4)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c18", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c5)"}]}, {"Id": "Dialog2c4", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c17a"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c17a"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c17b"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c17b"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c3", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c10", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c3)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c16", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c4)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c18", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c5)"}]}, {"Id": "Dialog2c5", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c19"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c19"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c20", "NextEventFunc": "DialogAction.DirectGoTo()", "Actions": ["DialogAction.GivePlayerPackage(MainQuest2eReward)", "DialogAction.SetRoleSwitchTo(RodianStory02,5)", "DialogAction.SetRoleSwitchTo(RodianStory03,1)", "DialogAction.GivePlayerPackage(Warrior02_Armor)", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine02,RodianMainLine03)"]}]}, {"Id": "Dialog2c8", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech2c2"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2c2"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c3", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c10", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c3)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c16", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c4)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech2c18", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2c5)"}]}], "EndDialogScript": []}, {"________________________________________主线任务__": "__3a______________________________________________________"}, {"说明": "主线任务3a对话", "Id": "Captain<PERSON><PERSON>_MainQuest3a", "FirstClip": "Dialog3a1", "NpcId": [], "Clips": [{"Id": "Dialog3a1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3a1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3a1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech3a2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog3a2)"}]}, {"Id": "Dialog3a2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3a3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3a3"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3a4"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3a4"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech3a6", "NextEventFunc": "DialogAction.DirectGoTo()", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory03,2)"]}]}], "EndDialogScript": []}, {"________________________________________主线任务__": "__3b______________________________________________________"}, {"说明": "主线任务3b对话", "Id": "Captain<PERSON>mith_MainQuest3b", "FirstClip": "Dialog3b", "NpcId": [], "Clips": [{"Id": "Dialog3b", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3b1"}], "Selections": []}], "EndDialogScript": []}, {"________________________________________主线任务__": "__3c______________________________________________________"}, {"说明": "主线任务3c对话", "Id": "Captain<PERSON><PERSON>_MainQuest3c", "FirstClip": "Dialog3c1", "NpcId": [], "Clips": [{"Id": "Dialog3c1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3c1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3c1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech3c2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog3c2)"}]}, {"Id": "Dialog3c2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3c3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3c3"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech3c4", "NextEventFunc": "DialogAction.DirectGoTo(Dialog3c3)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory03,4)", "DialogAction.SetRoleSwitchTo(RodianStory04,1)", "DesignerScript/TriggerScript_Country.DestroyCaptainWhenRodianStory04()", "DialogAction.GivePlayerPackage(Warrior02_Helmet)", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine03,RodianMainLine04)"]}]}, {"Id": "Dialog3c3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech3c5"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3c5"}], "Selections": []}], "EndDialogScript": []}, {"________________________________________主线任务__": "__4a______________________________________________________"}, {"说明": "主线任务4a对话", "Id": "Captain<PERSON><PERSON>_MainQuest4a", "FirstClip": "Dialog4a1", "NpcId": [], "Clips": [{"Id": "Dialog4a1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4a1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4a1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech4a2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog4a2)"}]}, {"Id": "Dialog4a2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4a3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4a3"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech4a4", "NextEventFunc": "DialogAction.DirectGoTo(Dialog4a3)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory04,2)"]}]}, {"Id": "Dialog4a3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4a5"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4a5"}], "Selections": []}], "EndDialogScript": []}, {"________________________________________主线任务__": "__4c______________________________________________________"}, {"说明": "主线任务4c对话", "Id": "Captain<PERSON><PERSON>_MainQuest4c", "FirstClip": "Dialog4cNormal", "NpcId": [], "Clips": [{"Id": "Dialog4cNormal", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4a5"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4a5"}], "Selections": []}]}, {"________________________________________主线任务__": "__4d______________________________________________________"}, {"说明": "主线任务4d对话", "Id": "Captain<PERSON><PERSON>_MainQuest4d", "FirstClip": "Dialog4d1", "NpcId": [], "Clips": [{"Id": "Dialog4d1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4d1"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4d1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Speech4d2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog4d2)", "Actions": ["DialogAction.SetRoleSwitchTo(RodianStory04,5)", "DialogAction.SetRoleSwitchTo(RodianStory05,1)", "DesignerScript/TriggerScript_Country.ShowTaskTips(RodianMainLine04,RodianMainLine05)"]}]}, {"Id": "Dialog4d2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech4d3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4d3"}], "Selections": []}], "EndDialogScript": []}, {"________________________________________主线任务__": "__5a______________________________________________________"}, {"说明": "主线任务5a对话单次", "Id": "Captain<PERSON><PERSON>_MainQuest5a", "FirstClip": "Dialog5a", "NpcId": [], "Clips": [{"Id": "Dialog5a", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5a1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5a1"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5a2"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5a2"}, {"Type": "PlayAudio", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5a3"}, {"Type": "Speak", "Id": "Step7", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5a3"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRoleSwitchTo(RodianStory05,2)"]}, {"________________________________________主线任务__": "__5b______________________________________________________"}, {"说明": "主线任务5b对话", "Id": "Captain<PERSON>mith_MainQuest5b", "FirstClip": "Dialog5b", "NpcId": [], "Clips": [{"Id": "Dialog5b", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardLeader_TurnLeft", "ActionId_TurnRight": "GuardLeader_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5b1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5b1"}, {"Type": "PlayAudio", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5b2"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5b2"}, {"Type": "PlayAudio", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5b3"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5b3"}, {"Type": "PlayAudio", "Id": "Step8", "NextId": "Step9", "TargetType": "Target", "AudioId": "Captain<PERSON><PERSON>_Speech5b4"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5b4"}], "Selections": []}]}, {"________________________________________11.23__": "__旧对话______________________________________________________"}, {"说明": "Captain<PERSON><PERSON>_FirstDial<PERSON>", "Id": "Captain<PERSON><PERSON>_FirstDial<PERSON>", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech0"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "LongDialog1"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech1"}, {"Type": "FocusTarget", "Id": "Step5", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection0", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog01)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection1", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog02)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection2", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog03)"}]}, {"Id": "FirstDialog01", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech2"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue0)"}]}, {"Id": "FirstDialog02", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Confidence"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech3"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue0)"}]}, {"Id": "FirstDialog03", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog2"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech4"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue0)"}]}, {"Id": "FirstDialogContinue0", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Angry"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech5"}, {"Type": "FocusTarget", "Id": "Step3", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection3", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue1)"}]}, {"Id": "FirstDialogContinue1", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog3"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech6"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "MediumDialog2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech7"}, {"Type": "FocusTarget", "Id": "Step5", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection4", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection5", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue2)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection6", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue2)"}]}, {"Id": "FirstDialogContinue2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "MediumDialog3"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech8"}, {"Type": "FocusTarget", "Id": "Step3", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection7", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue3)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection8", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue3)"}]}, {"Id": "FirstDialogContinue3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Persuade1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech9"}, {"Type": "FocusTarget", "Id": "Step3", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON><PERSON>_Selection9", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog21)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "CaptainSmith_Selection10", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog22)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection11", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialog23)"}]}, {"Id": "FirstDialog21", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Pity"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech10"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue4)"}]}, {"Id": "FirstDialog22", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech11"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue4)"}]}, {"Id": "FirstDialog23", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog4"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech12"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "NextEventFunc": "DialogAction.DirectGoTo(FirstDialogContinue4)"}]}, {"Id": "FirstDialogContinue4", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Encouragement"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech13"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_SpeakOnMine200", "Id": "Captain<PERSON><PERSON>_SpeakOnMine200", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Sad"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech14"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_SpeakOnMine120", "Id": "Captain<PERSON><PERSON>_SpeakOnMine120", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Nope"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech15"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_SpeakOnMine60", "Id": "Captain<PERSON><PERSON>_SpeakOnMine60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Encouragement"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech16"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_SpeakOnMine0", "Id": "Captain<PERSON><PERSON>_SpeakOnMine0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Victory"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_<PERSON>17"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_Goblin100", "Id": "Captain<PERSON><PERSON>_Goblin100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ShortDialog1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech22"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_<PERSON>blin60", "Id": "Captain<PERSON><PERSON>_<PERSON>blin60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "LongDialog1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech23"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_<PERSON><PERSON>30", "Id": "Captain<PERSON><PERSON>_<PERSON><PERSON>30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Fighting"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech24"}, {"Type": "FocusTarget", "Id": "Step3", "NextId": "", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection12", "NextEventFunc": "DialogAction.DirectGoTo(Captain<PERSON>mith_Goblin30_11)"}, {"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Captain<PERSON>mith_Selection13", "NextEventFunc": "DialogAction.DirectGoTo(Captain<PERSON>mith_Goblin30_12)"}]}, {"Id": "Captain<PERSON><PERSON>_<PERSON><PERSON>30_11", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech25"}], "Selections": []}, {"Id": "Captain<PERSON><PERSON>_<PERSON>blin30_12", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Nope"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_<PERSON>26"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_Goblin0", "Id": "Captain<PERSON><PERSON>_Goblin0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Excited"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech27"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_WereRat100", "Id": "Captain<PERSON><PERSON>_WereRat100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Angry"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech18"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_WereRat60", "Id": "Captain<PERSON><PERSON>_WereRat60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Demonstrate1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech19"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_WereRat30", "Id": "Captain<PERSON><PERSON>_WereRat30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Fighting"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech20"}], "Selections": []}]}, {"说明": "Captain<PERSON><PERSON>_WereRat0", "Id": "Captain<PERSON><PERSON>_WereRat0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Excited"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "Captain<PERSON>mith_Name", "Text": "Captain<PERSON><PERSON>_Speech21"}], "Selections": []}]}]}