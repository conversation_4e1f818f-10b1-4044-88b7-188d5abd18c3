{"Mob": [{"Id": "MineTeamBlacksmith", "Tag": ["Npc"], "BpPath": "Core/Characters/NPC/Dwarf/Dwarf_New", "AI": ["StopAIBuff"], "AIOrder": [], "StartFightingWillLevel": 1, "MaxFightingWillLevel": 4, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "MineTeamBlacksmith_Name", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightHalfAngleDregee": 45, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 50, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 10, "PAtk": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "", "UnArmed": ""}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Dwarf/Move"]}, "InitAction": true}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "MontageAnimPickFunc.Random(1)", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Hurt/Dead1", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/Dead2"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Hurt/HurtFromFront", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/AirHurt", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/AirHurt"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/BlowFromBack", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Dwarf/Hurt/BlowFromBack"]}}, {"说明": "打铁1", "Id": "Dwarf_Hit1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Hit1"]}, "InitAction": true}, {"说明": "打铁2", "Id": "Dwarf_Hit2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Hit2"]}, "InitAction": true}, {"说明": "环顾四周1", "Id": "LookAround1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/LookAround1"]}, "InitAction": true}, {"说明": "环顾四周2", "Id": "LookAround2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/LookAround2"]}, "InitAction": true}, {"说明": "打铁中突发恶疾", "Id": "Dwarf_Shout", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Shout"]}, "InitAction": true}, {"说明": "站立", "Id": "Dwarf_Idle", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Idle"]}, "InitAction": true}, {"说明": "长对话1", "Id": "Dwarf_LongSpeech1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_LongSpeech1"]}, "InitAction": true}, {"说明": "长对话2", "Id": "Dwarf_LongSpeech2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_LongSpeech2"]}, "InitAction": true}, {"说明": "长对话3", "Id": "Dwarf_LongSpeech3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_LongSpeech3"]}, "InitAction": true}, {"说明": "长对话4", "Id": "Dwarf_LongSpeech4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_LongSpeech4"]}, "InitAction": true}, {"说明": "中对话1", "Id": "Dwarf_MediumSpeech1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_MediumSpeech1"]}, "InitAction": true}, {"说明": "中对话2", "Id": "Dwarf_MediumSpeech2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_MediumSpeech2"]}, "InitAction": true}, {"说明": "中对话3", "Id": "Dwarf_MediumSpeech3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_MediumSpeech3"]}, "InitAction": true}, {"说明": "中对话4", "Id": "Dwarf_MediumSpeech4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_MediumSpeech4"]}, "InitAction": true}, {"说明": "否认", "Id": "Dwarf_Nope", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_Nope"]}, "InitAction": true}, {"说明": "短对话1", "Id": "Dwarf_ShortSpeech1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_ShortSpeech1"]}, "InitAction": true}, {"说明": "短对话2", "Id": "Dwarf_ShortSpeech2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_ShortSpeech2"]}, "InitAction": true}, {"说明": "短对话3", "Id": "Dwarf_ShortSpeech3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_ShortSpeech3"]}, "InitAction": true}, {"说明": "短对话4", "Id": "Dwarf_ShortSpeech4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Dialog/Dwarf_ShortSpeech4"]}, "InitAction": true}, {"说明": "高兴", "Id": "Dwarf_Happy", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Happy"]}, "InitAction": true}, {"说明": "否认", "Id": "Dwarf_Nope", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Nope"]}, "InitAction": true}, {"说明": "极力否认", "Id": "Dwarf_Nope_Extremely", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_Nope_Extremely"]}, "InitAction": true}, {"说明": "向左转", "Id": "Dwarf_TurnLeft", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_TurnLeft180"]}, "InitAction": true}, {"说明": "向右转", "Id": "Dwarf_TurnRight", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Dwarf/Action/Dwarf_TurnRight180"]}, "InitAction": true}]}]}