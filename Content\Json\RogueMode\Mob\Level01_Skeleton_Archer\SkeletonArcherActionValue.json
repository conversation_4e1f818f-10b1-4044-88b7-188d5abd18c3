{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "<PERSON>_Skel<PERSON>_Archer", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 4000, "Weight": 10}, {"MinRange": 2000, "MaxRange": 99999, "Weight": -99}], "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 4000, "Weight": 1}, {"MinRange": 4000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面", "MinRange": "-60", "MaxRange": "60", "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 6, "MaxActionCD": 12}, {"Id": "Action_Stare02", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 1000, "Weight": 0}, {"MinRange": 1000, "MaxRange": 2000, "Weight": 3}, {"MinRange": 4000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 5, "MaxActionCD": 8}, {"Id": "Walk_BHDLeft", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 4000, "Weight": 1}, {"MinRange": 4000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 5}, {"Id": "Walk_BHDRight", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 4000, "Weight": 1}, {"MinRange": 4000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 5}, {"Id": "Walk_FWD", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 800, "Weight": 0}, {"MinRange": 800, "MaxRange": 4000, "Weight": 5}, {"MinRange": 4000, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 6}]}]}