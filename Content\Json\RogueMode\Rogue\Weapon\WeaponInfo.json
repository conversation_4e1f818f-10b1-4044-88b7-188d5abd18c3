{"ClassWeapons": {"Swordsman_Gerasso": ["SwordAndShield_1", "SwordAndShield_2", "SwordAndShield_3", "SwordAndShield_4", "SwordAndShield_5"], "BladeDancer_Henrik": ["DualSwords_1", "DualSwords_2", "DualSwords_3", "DualSwords_4", "DualSwords_5"], "Spearman_Sola": ["Spear_1", "Spear_2", "Spear_3", "Spear_4", "Spear_5"], "Warrior_Tierdagon": ["GreatSword_1", "GreatSword_2", "GreatSword_3", "GreatSword_4", "GreatSword_5"], "Warrior_Caelynn": ["GreatSword_1", "GreatSword_2", "GreatSword_3", "GreatSword_4", "GreatSword_5"], "CiKe_Yin": ["DualSwords_1", "DualSwords_2", "DualSwords_3", "DualSwords_4", "DualSwords_5"], "WuJiang_Kwang": ["GreatSword_1", "GreatSword_2", "GreatSword_3", "GreatSword_4", "GreatSword_5"]}, "RogueWeaponInfo": [{"Id": "Example", "Name": "Chinese表Id,武器名称", "Icon": "ItemIcon表Id", "MainHand": "主手武器模型", "OffHand": "副手武器模型", "DesignIdeas": "设计思路(注释)", "Desc1": "Chinese表Id,武器描述1", "Desc2": "Chinese表Id,武器描述2", "LevelDesc": ["Chinese表Id", "初始描述", "等级+1", "等级+2", "等级+3", "..."], "Buffs_Instructions": "下面武器 Buffs 正确Key 为 Buffs ,但因先版本不需要武器有额外效果，以后版本想使武器重新拥有额外效果把 Buffs 改为 Buffs", "Buffs": [[{"Id": "初始 buff1", "Stack": 1, "Time": 1, "Infinity": true}], [{"Id": "+1 buff1", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "+1 buff2", "Stack": 2, "Time": 1, "Infinity": true}], [{"Id": "+2 buff1", "Stack": 2, "Time": 1, "Infinity": true}, {"Id": "+2 buff2", "Stack": 4, "Time": 1, "Infinity": true}], [{"Id": "+3 buff1", "Stack": 3, "Time": 1, "Infinity": true}, {"Id": "+3 buff2", "Stack": 6, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": ["lv2 需要的key", "lv3 需要的key", "lv4 需要的key", "lv5 需要的key"]}, {"Line": "______________________________ Sword And Shield ______________________________"}, {"Id": "SwordAndShield_1", "Name": "SwordAndShield_1_Name", "Icon": "SwordAndShield_1", "MainHand": "<PERSON>_<PERSON>_<PERSON>uin's_Explorer", "OffHand": "<PERSON>_<PERSON>_<PERSON>uin's_Explorer", "DesignIdeas": "普通强化剑盾,平衡向", "Buffs": [[{"Id": "Rogue_AttackPercentUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 300, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 900, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1200, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [0]}, {"Id": "SwordAndShield_2", "Name": "SwordAndShield_2_Name", "Icon": "SwordAndShield_2", "MainHand": "<PERSON>_<PERSON>_<PERSON><PERSON>'s_Proof", "OffHand": "Rouge_<PERSON>_<PERSON><PERSON>'s_Proof", "DesignIdeas": "强化剑（比较长的剑）,弱化盾（比较小的盾）,轻剑,长距离+高攻速", "Buffs": [[{"Id": "Rogue_ActionSpeedUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 200, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 400, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 300, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 400, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 800, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 500, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 600, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 1200, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "SwordAndShield_3", "Name": "SwordAndShield_3_Name", "Icon": "SwordAndShield_3", "MainHand": "Rouge_Sword_Guardian's_Horn", "OffHand": "Rouge_Shield_Guardian's_Horn", "DesignIdeas": "强化盾（比较大的盾）,弱化剑（比较短的剑）,强调盾和防反", "Buffs": [[{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 3000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 20, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 40, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 50, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_DefenceCounter", "Stack": 60, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "SwordAndShield_4", "Name": "SwordAndShield_4_Name", "Icon": "SwordAndShield_4", "MainHand": "Rouge_Sword_Blood_Seal", "OffHand": "Rouge_Shield_Blood_Seal", "DesignIdeas": "看起来和神有关,局内偏元素向", "Buffs": [[{"Id": "Rogue_ElementalDamageUp", "Stack": 15, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 8, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 12, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 16, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 24, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "SwordAndShield_5", "Name": "SwordAndShield_5_Name", "Icon": "SwordAndShield_5", "MainHand": "<PERSON>_Sword_Truly_God's_Grace", "OffHand": "<PERSON>_Shield_Truly_God's_Grace", "DesignIdeas": "资源刀,给不影响直接战斗体验的收益", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}, {"Line": "______________________________ DualSwords ______________________________"}, {"Id": "DualSwords_1", "Name": "DualSwords_1_Name", "Icon": "DualSwords_1", "MainHand": "Rouge_DualSword_Whispering_Blades", "OffHand": "Rouge_DualSword_Whispering_Blades", "DesignIdeas": "普通强化双刀,平衡向", "Buffs": [[], [{"Id": "Rogue_AttackPercentUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 900, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1200, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [0]}, {"Id": "DualSwords_2", "Name": "DualSwords_2_Name", "Icon": "DualSwords_2", "MainHand": "Rouge_DualSword_<PERSON><PERSON>_<PERSON>uitter", "OffHand": "Rouge_DualSword_<PERSON><PERSON>_<PERSON>uitter", "DesignIdeas": "JustAttack刀,强化双刀justattack收益", "Buffs": [[{"Id": "Rogue_DamageUp_JustAttack", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_JustAttack", "Stack": 10, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_JustAttack", "Stack": 15, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_JustAttack", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_JustAttack", "Stack": 25, "Time": 1, "Infinity": true}], [{"Id": "Rogue_DamageUp_JustAttack", "Stack": 30, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "DualSwords_3", "Name": "DualSwords_3_Name", "Icon": "DualSwords_3", "MainHand": "Rouge_DualSword_The_Queen's_Skeleton", "OffHand": "Rouge_DualSword_The_Queen's_Skeleton", "DesignIdeas": "吸血刀,普通吸血", "Buffs": [[{"Id": "Rogue_MaxHealthPercentDown", "Stack": 5000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 5, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 1, "Time": 1, "Infinity": true}], [{"Id": "Rogue_MaxHealthPercentDown", "Stack": 6000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 60, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 1, "Time": 1, "Infinity": true}], [{"Id": "Rogue_MaxHealthPercentDown", "Stack": 5000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 50, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 2, "Time": 1, "Infinity": true}], [{"Id": "Rogue_MaxHealthPercentDown", "Stack": 4000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 40, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 2, "Time": 1, "Infinity": true}], [{"Id": "Rogue_MaxHealthPercentDown", "Stack": 3000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 3, "Time": 1, "Infinity": true}], [{"Id": "Rogue_MaxHealthPercentDown", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_HurtUp", "Stack": 20, "Time": 1, "Infinity": true}, {"Id": "Rogue_RestoreHpOnHit", "Stack": 3, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "DualSwords_4", "Name": "DualSwords_4_Name", "Icon": "DualSwords_4", "MainHand": "Rouge_DualSword_Night_Ravens", "OffHand": "Rouge_DualSword_Night_Ravens", "DesignIdeas": "看起来和神有关,局内偏元素向", "Buffs": [[{"Id": "Rogue_ElementalDamageUp", "Stack": 10, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 8, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 12, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 16, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 24, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "DualSwords_5", "Name": "DualSwords_5_Name", "Icon": "DualSwords_5", "MainHand": "Rouge_DualSword_Glory_Swords", "OffHand": "Rouge_DualSword_Glory_Swords", "DesignIdeas": "资源刀,给不影响直接战斗体验的收益", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}, {"Line": "______________________________ Spear ______________________________"}, {"Id": "Spear_1", "Name": "Spear_1_Name", "Icon": "Spear_1", "MainHand": "<PERSON>_<PERSON><PERSON>_Sheen_<PERSON>s", "OffHand": "", "DesignIdeas": "普通强化长枪,平衡向", "Buffs": [[], [{"Id": "Rogue_AttackPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 900, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1200, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "Spear_2", "Name": "Spear_2_Name", "Icon": "Spear_2", "MainHand": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>nan", "OffHand": "", "DesignIdeas": "攻速暴击枪,更大范围高暴", "Buffs": [[], [{"Id": "Rogue_ActionSpeedUp", "Stack": 1500, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 2000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 200, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 400, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 300, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 400, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 800, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedUp", "Stack": 500, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 1000, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "Spear_3", "Name": "Spear_3_Name", "Icon": "Spear_3", "MainHand": "<PERSON>_<PERSON><PERSON>_Omen_of_Descent", "OffHand": "", "DesignIdeas": "强调空中战斗", "Buffs": [[{"Id": "Rogue_AttackUpOnAir_1_4_20", "Stack": 1, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackUpOnAir_1_3_15", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalRateUp", "Stack": 400, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackUpOnAir_0.8_3_15", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalRateUp", "Stack": 800, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackUpOnAir_0.8_4_20", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalRateUp", "Stack": 1200, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackUpOnAir_0.6_4_20", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalRateUp", "Stack": 1600, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackUpOnAir_0.5_5_25", "Stack": 1, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalRateUp", "Stack": 2000, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "Spear_4", "Name": "Spear_4_Name", "Icon": "Spear_4", "MainHand": "<PERSON>_<PERSON><PERSON>_Tear_of_Tidal", "OffHand": "", "DesignIdeas": "看起来和神有关,局内偏元素向", "Buffs": [[{"Id": "Rogue_ElementalDamageUp", "Stack": 25, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 8, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 12, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 16, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 24, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "Spear_5", "Name": "Spear_5_Name", "Icon": "Spear_5", "MainHand": "<PERSON>_<PERSON><PERSON>_Pride_of_Dwarf", "OffHand": "<PERSON>_<PERSON><PERSON>_Pride_of_Dwarf", "DesignIdeas": "资源枪,给不影响直接战斗体验的收益", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}, {"Id": "Spear_6", "Name": "Spear_6_Name", "Icon": "Spear_6", "MainHand": "<PERSON>_<PERSON><PERSON>_<PERSON>uan<PERSON>o", "OffHand": "<PERSON>_<PERSON><PERSON>_<PERSON>uan<PERSON>o", "DesignIdeas": "Guandao", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}, {"Id": "Spear_7", "Name": "Spear_7_Name", "Icon": "Spear_7", "MainHand": "<PERSON>_<PERSON>_Tang<PERSON>", "OffHand": "<PERSON>_<PERSON>_Tang<PERSON>", "DesignIdeas": "<PERSON><PERSON><PERSON>", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}, {"Line": "______________________________ GreatSword ______________________________"}, {"Id": "GreatSword_1", "Name": "GreatSword_1_Name", "Icon": "GreatSword_1", "MainHand": "<PERSON>_BigS<PERSON>_Oath_Breaker", "OffHand": "", "DesignIdeas": "普通强化大剑,平衡向", "Buffs": [[], [{"Id": "Rogue_AttackPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 1500, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}], [{"Id": "Rogue_AttackPercentUp", "Stack": 2500, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [0]}, {"Id": "GreatSword_2", "Name": "GreatSword_2_Name", "Icon": "GreatSword_2", "MainHand": "Rouge_BigSword_King's_Blade", "OffHand": "", "DesignIdeas": "轻大剑,高攻速低伤害,鼓励打物理伤害", "Buffs": [[{"Id": "Rogue_ActionSpeedUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_CriticalChanceUp", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 10, "Time": 1, "Infinity": true}], [{"Id": "Rogue_PowerSpeedUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageDown", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_ActionSpeedUp", "Stack": 200, "Time": 1, "Infinity": true}, {"Id": "Rogue_PhysicalDamageUp", "Stack": 3, "Time": 1, "Infinity": true}], [{"Id": "Rogue_PowerSpeedUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageDown", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_ActionSpeedUp", "Stack": 400, "Time": 1, "Infinity": true}, {"Id": "Rogue_PhysicalDamageUp", "Stack": 6, "Time": 1, "Infinity": true}], [{"Id": "Rogue_PowerSpeedUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageDown", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_ActionSpeedUp", "Stack": 600, "Time": 1, "Infinity": true}, {"Id": "Rogue_PhysicalDamageUp", "Stack": 9, "Time": 1, "Infinity": true}], [{"Id": "Rogue_PowerSpeedUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageDown", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_ActionSpeedUp", "Stack": 800, "Time": 1, "Infinity": true}, {"Id": "Rogue_PhysicalDamageUp", "Stack": 12, "Time": 1, "Infinity": true}], [{"Id": "Rogue_PowerSpeedUp", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageDown", "Stack": 30, "Time": 1, "Infinity": true}, {"Id": "Rogue_ActionSpeedUp", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PhysicalDamageUp", "Stack": 15, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "GreatSword_3", "Name": "GreatSword_3_Name", "Icon": "GreatSword_3", "MainHand": "Rouge_BigS<PERSON>_<PERSON>'s_Will", "OffHand": "", "DesignIdeas": "重大剑,强化重但高伤特色", "Buffs": [[{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 5000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 30, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 10, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 15, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 25, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ActionSpeedDown", "Stack": 1000, "Time": 1, "Infinity": true}, {"Id": "Rogue_MaxHealthPercentUp", "Stack": 2000, "Time": 1, "Infinity": true}, {"Id": "Rogue_PowerDamageUp", "Stack": 30, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "GreatSword_4", "Name": "GreatSword_4_Name", "Icon": "GreatSword_4", "MainHand": "Rouge_BigSword_Wrath_of_<PERSON>f", "OffHand": "", "DesignIdeas": "看起来和神有关,局内偏元素向", "Buffs": [[{"Id": "Rogue_ElementalDamageUp", "Stack": 20, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 16, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 24, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 32, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 40, "Time": 1, "Infinity": true}], [{"Id": "Rogue_ElementalDamageUp", "Stack": 48, "Time": 1, "Infinity": true}]], "UnlockSoul": 300, "Cost": [5]}, {"Id": "GreatSword_5", "Name": "GreatSword_5_Name", "Icon": "GreatSword_5", "MainHand": "Rouge_BigSword_Golden_Oath", "OffHand": "", "DesignIdeas": "资源大剑,给不影响直接战斗体验的收益", "Buffs": [[{"Id": "Rogue_GetSoulUp", "Stack": 10, "Time": 1, "Infinity": true}, {"Id": "Rogue_GetKeyUp", "Stack": 2, "Time": 1, "Infinity": true}]], "Cost": [0]}]}