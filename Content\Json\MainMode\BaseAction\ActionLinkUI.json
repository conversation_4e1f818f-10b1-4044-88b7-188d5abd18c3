{"ActionSelectionUI描述": {"Id": "挂向ActionSelectionId", "InAir": "<bool>是否是地面分类", "Actions": [{"ActionId": "动作对应的id", "Icon": "动作对应的icon", "Desc": "描述文字", "Name": "名称：猎魔突袭斩", "Desc_Example": "", "Name_Example": "", "Slots": [{"Slot": "槽的id", "Icon": "对应效果的图标", "Desc": "描述文字"}]}]}, "ActionLinkUI描述文字": {"Id": "挂向ActionLink的Id", "MainActionIcon": "", "MainActionName": "", "MainActionDesc": "", "Slots": [{"Slot": "槽的id", "Icon": "对应效果的图标", "Desc": "描述文字"}], "CandidateActions": [{"ActionId": "这个子动作的id", "Trigger": "怎么触发的描述文字", "ActionIcon": "子动作的icon", "ActionName": "子动作的名称", "ActionDesc": "", "ActionName_Example": "", "Desc_Example": ""}]}, "ActionSelectionUI": [{"Line": "_______________________________Warrior________________________________"}, {"Id": "Warrior_Action3", "CmdIcon": "Key_R1", "InAir": false, "Actions": [{"ActionId": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Icon": "ArtResource/UI/Icon/Skill/2-3", "Desc": "<PERSON>_Sting<PERSON>ash<PERSON><PERSON>ck_Desc", "Name": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Desc_Example": "提起大剑向前中范围冲锋并造成多端伤害。", "Name_Example": "巨刃突刺"}, {"ActionId": "Warrior_DashSlashAttack1", "Icon": "ArtResource/UI/Icon/Skill/2-1", "Desc": "Warrior_DashSlashAttack1_Desc", "Name": "", "Desc_Example": "向前方快速位移一小段距离并造成范围伤害。", "Name_Example": "突袭迅斩"}]}, {"Id": "Warrior_Action3_Air", "CmdIcon": "Key_R1", "InAir": true, "Actions": [{"ActionId": "Warrior_AirDoubleStrike0", "Icon": "ArtResource/UI/Icon/Skill/2-1", "Desc": "Warrior_AirDoubleStrike0_Desc", "Name": "", "Desc_Example": "滞空时使用此技能对敌人造成最多二段的伤害，第二段攻击能将小体型目标挑入空中随后自身落地。", "Name_Example": "空中突袭迅斩"}]}, {"Id": "Warrior_Action4", "CmdIcon": "Key_R2", "InAir": false, "Actions": [{"ActionId": "<PERSON><PERSON><PERSON>", "Icon": "ArtResource/UI/Icon/Skill/2-6", "Desc": "<PERSON>_<PERSON>_<PERSON>", "Name": "", "Desc_Example": "敌人攻击命中的瞬间按下防御，可无视其攻击并对其造成大量伤害。", "Name_Example": "剑意反击"}]}, {"Id": "Warrior_Action4_Air", "CmdIcon": "Key_R2", "InAir": true, "Actions": [{"ActionId": "Warrior_AirRush_DemonSlayer", "Icon": "ArtResource/UI/Icon/Skill/2-2", "Desc": "Warrior_AirRush_DemonSlayer_Desc", "Name": "", "Desc_Example": "滞空时使用此技能向上跳跃一小段距离后，奋力向前冲刺，落地的同时利用强力的斩击对对手造成猛烈的一击。", "Name_Example": "猎魔突袭斩"}, {"ActionId": "Warrior_Air_FallSlash", "Icon": "ArtResource/UI/Icon/Skill/2-4", "Desc": "Warrior_Air_FallSlash_Desc", "Name": "", "Desc_Example": "滞空时使用此技能，向下冲击的同时对地面敌人造成范围伤害。", "Name_Example": "冲地一击"}]}, {"Id": "Warrior_Action2", "CmdIcon": "Key_Triangle", "InAir": false, "Actions": [{"ActionId": "Warrior_ChargeAttack", "Icon": "ArtResource/UI/Icon/Skill/2-1", "Desc": "Warrior_ChargeAttack_Desc", "Name": "", "Desc_Example": "抬起大剑蓄力，最多可达3段蓄力，最后一段蓄力斩先向前横扫压制对手，随后再向前方奋力一劈。", "Name_Example": "蓄力斩"}, {"ActionId": "Warrior_ChargeJumpAttack", "Icon": "ArtResource/UI/Icon/Skill/2-5", "Desc": "Warrior_ChargeJumpAttack_Desc", "Name": "", "Desc_Example": "抬起大剑蓄力，最多可达3段蓄力，最后一段蓄力斩向前方跃起后向下重劈，造成范围伤害。", "Name_Example": "崩裂蓄力斩"}]}, {"Id": "Warrior_Action2_Air", "CmdIcon": "Key_Triangle", "InAir": true, "Actions": [{"ActionId": "Warrior_AirChargeAttack", "Icon": "ArtResource/UI/Icon/Skill/2-5", "Desc": "Warrior_AirChargeAttack_Desc", "Name": "", "Desc_Example": "滞空时使用此技能。对地面敌人造成范围伤害并落地。", "Name_Example": "空中蓄力斩"}]}, {"Line": "_______________________________BladeDancer________________________________"}, {"Id": "BladeDancer_Action3", "CmdIcon": "Key_R1", "InAir": false, "Actions": [{"ActionId": "BladeDancer_DashAttack", "Icon": "ArtResource/UI/Icon/Skill/3-1", "Desc": "BladeDancer_DashAttack_Desc", "Name": "", "Desc_Example": "向前突进的同时向前方挥砍双剑，并对沿途敌人造成伤害。", "Name_Example": "迅捷斩"}]}, {"Id": "BladeDancer_Action2_Air", "CmdIcon": "Key_Triangle", "InAir": true, "Actions": [{"ActionId": "BladeDancer_AirTwiceComboAttack", "Icon": "ArtResource/UI/Icon/Skill/3-5", "Desc": "BladeDancer_AirTwiceComboAttack_Desc", "Name": "", "Desc_Example": "滞空时以迅捷的动作发动两段斩击。", "Name_Example": "片刃双击"}]}, {"Id": "BladeDancer_Action2", "CmdIcon": "Key_Triangle", "InAir": false, "Actions": [{"ActionId": "BladeDancer_RiseComboSlash", "Icon": "ArtResource/UI/Icon/Skill/3-2", "Desc": "BladeDancer_RiseComboSlash_Desc", "Name": "", "Desc_Example": "使用二连斩击，后跳的同时挑飞身前敌人。", "Name_Example": "连击升空斩"}, {"ActionId": "BladeDancer_RiseSlash", "Icon": "ArtResource/UI/Icon/Skill/3-2", "Desc": "BladeDancer_RiseSlash_Desc", "Name": "", "Desc_Example": "快速的二连斩击，跃起的同时挑飞身前敌人。", "Name_Example": "升空斩"}]}, {"Id": "BladeDancer_Action3_Air", "CmdIcon": "Key_R1", "InAir": true, "Actions": [{"ActionId": "BladeDancer_AirDashAttack", "Icon": "ArtResource/UI/Icon/Skill/3-1", "Desc": "BladeDancer_AirDashAttack_Desc", "Name": "", "Desc_Example": "滞空时，向前突进并对沿途敌人造成伤害。", "Name_Example": "空中迅捷斩"}, {"ActionId": "BladeDancer_AirDashComboAttack", "Icon": "ArtResource/UI/Icon/Skill/3-1", "Desc": "BladeDancer_AirDashComboAttack_Desc", "Name": "", "Desc_Example": "滞空时，向后转身并且挥出迅捷的一剑，再向前突进并对沿途敌人造成伤害。", "Name_Example": "刃风迅捷斩"}]}, {"Id": "BladeDancer_Action4", "CmdIcon": "Key_R2", "InAir": false, "Actions": [{"ActionId": "BladeDancer_SwrodDanceComboAttack", "Icon": "ArtResource/UI/Icon/Skill/3-5", "Desc": "BladeDancer_SwrodDanceComboAttack_Desc", "Name": "", "Desc_Example": "使用双剑带动身体旋转而使出的剑舞动作", "Name_Example": "锋刃剑舞"}]}, {"Id": "BladeDancer_Action4_Air", "CmdIcon": "Key_R2", "InAir": true, "Actions": [{"ActionId": "BladeDancer_AirSwrodDanceComboAttack_Fall", "Icon": "ArtResource/UI/Icon/Skill/3-4", "Desc": "BladeDancer_AirSwrodDanceComboAttack_Fall_Desc", "Name": "", "Desc_Example": "滞空时，旋转自身的同时带动双剑挥砍，形成多连段的伤害直到落地。", "Name_Example": "环剑大风车"}, {"ActionId": "BladeDancer_AirSwrodDanceComboAttack_Dash", "Icon": "ArtResource/UI/Icon/Skill/3-4", "Desc": "BladeDancer_AirSwrodDanceComboAttack_Dash_Desc", "Name": "", "Desc_Example": "滞空时，旋转自身的同时带动双剑挥砍，向前位移的同时形成多连段的伤害。", "Name_Example": "回旋空乱舞"}]}, {"Line": "_______________________________Spearman________________________________"}, {"Id": "Spearman_Action2", "CmdIcon": "Key_Triangle", "InAir": false, "Actions": [{"ActionId": "Spearman_SweapAttack1", "Icon": "ArtResource/UI/Icon/Skill/4-3", "Desc": "<PERSON><PERSON><PERSON>_SweapAttack1_Desc", "Name": "", "Desc_Example": "向身边敌人发动范围攻击，最多可以打出四段连击。", "Name_Example": "回旋乱舞 I"}]}, {"Id": "<PERSON><PERSON>man_Action2_Air", "CmdIcon": "Key_Triangle", "InAir": true, "Actions": [{"ActionId": "Spearman_AirDownSpikeAttack1", "Icon": "ArtResource/UI/Icon/Skill/4-4", "Desc": "<PERSON><PERSON><PERSON>_AirDownSpikeAttack1_Desc", "Name": "", "Desc_Example": "滞空时使用此技能对下方发动突刺攻击，造成小范围伤害。", "Name_Example": "下落突刺"}]}, {"Id": "Spearman_Action3", "CmdIcon": "Key_R1", "InAir": false, "Actions": [{"ActionId": "S<PERSON><PERSON>_DashSpike", "Icon": "ArtResource/UI/Icon/Skill/4-1", "Desc": "<PERSON><PERSON><PERSON>_DashSpike_Desc", "Name": "", "Desc_Example": "向前方用力发动穿刺攻击。", "Name_Example": "突进穿刺"}]}, {"Id": "<PERSON><PERSON>man_Action3_Air", "CmdIcon": "Key_R1", "InAir": true, "Actions": [{"ActionId": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown", "Icon": "ArtResource/UI/Icon/Skill/4-1", "Desc": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown_Desc", "Name": "", "Desc_Example": "滞空时使用此技能向斜前方俯冲攻击。", "Name_Example": "俯冲突刺"}]}, {"Id": "Spearman_Action4", "CmdIcon": "Key_R2", "InAir": false, "Actions": [{"ActionId": "S<PERSON><PERSON>_DashSweapSlash", "Icon": "ArtResource/UI/Icon/Skill/4-6", "Desc": "<PERSON><PERSON><PERSON>_DashSweapSlash_Desc", "Name": "", "Desc_Example": "向前方突进一段距离并打出范围攻击。", "Name_Example": "突进横扫"}, {"ActionId": "S<PERSON><PERSON>_BackJumpSweapSlash", "Icon": "ArtResource/UI/Icon/Skill/4-7", "Desc": "<PERSON><PERSON><PERSON>_BackJumpSweapSlash_Desc", "Name": "", "Desc_Example": "向后方跳跃并对前方小范围内敌人造成范围伤害。", "Name_Example": "后跳横扫"}]}, {"Id": "<PERSON><PERSON>man_Action4_Air", "CmdIcon": "Key_R2", "InAir": true, "Actions": [{"ActionId": "Spearman_AirDashSweapSlash", "Icon": "ArtResource/UI/Icon/Skill/4-6", "Desc": "<PERSON><PERSON><PERSON>_AirDashSweapSlash_Desc", "Name": "", "Desc_Example": "滞空时向前方突进一段距离并打出范围攻击。", "Name_Example": "空中突进横扫"}]}, {"Line": "_______________________________Swordsman________________________________"}, {"Id": "Swordsman_Action2", "CmdIcon": "Key_Triangle", "InAir": false, "Actions": [{"ActionId": "Swordsman_RiseSlash", "Icon": "ArtResource/UI/Icon/Skill/5-1", "Desc": "Swordsman_RiseSlash_Desc", "Name": "", "Desc_Example": "向上挥的同时跃向空中，可将部分敌人挑入空中同时自身进入滞空。", "Name_Example": "升飞斩"}, {"ActionId": "Swordsman_RiseComboSlash", "Icon": "ArtResource/UI/Icon/Skill/5-2", "Desc": "Swordsman_RiseComboSlash_Desc", "Name": "", "Desc_Example": "先在地面挥砍一次，向上挑的同时向上跳跃，可将部分敌人挑入空中同时自身进入滞空。", "Name_Example": "裂空斩"}]}, {"Id": "Swordsman_Action2_Air", "CmdIcon": "Key_Triangle", "InAir": true, "Actions": [{"ActionId": "Swordsman_AirDownSlashAttack1", "Icon": "ArtResource/UI/Icon/Skill/2-4", "Desc": "Swordsman_AirDownSlashAttack1_Desc", "Name": "", "Desc_Example": "滞空时向下方劈，造成伤害同时落地", "Name_Example": "下冲斩"}]}, {"Id": "Swordsman_Action3", "CmdIcon": "Key_R1", "InAir": false, "Actions": [{"ActionId": "Swordsman_DashSlash", "Icon": "ArtResource/UI/Icon/Skill/4-6", "Desc": "Swordsman_DashSlash_Desc", "Name": "", "Desc_Example": "向前冲锋一小段距离并进行横向的范围挥砍。", "Name_Example": "突进斩"}]}, {"Id": "Swordsman_Action3_Air", "CmdIcon": "Key_R1", "InAir": true, "Actions": [{"ActionId": "Swordsman_AirDashSting", "Icon": "ArtResource/UI/Icon/Skill/4-1", "Desc": "Swordsman_AirDashSting_Desc", "Name": "", "Desc_Example": "滞空时向前冲锋一小段距离并对地面敌人造成范围伤害并落地。", "Name_Example": "突锋突刺"}]}, {"Id": "Swordsman_Action4", "CmdIcon": "Key_R2", "InAir": false, "Actions": [{"ActionId": "Swordsman_Defense", "Icon": "ArtResource/UI/Icon/Skill/5-4", "Desc": "Swordsman_Defense_Desc", "Name": "", "Desc_Example": "持盾进入防御姿态，能防御来自身前范围的攻击。", "Name_Example": "盾牌防御"}]}, {"Id": "Swordsman_Action4_Air", "CmdIcon": "Key_R2", "InAir": true, "Actions": [{"ActionId": "Swordsman_AirDownShieldSmash", "Icon": "ArtResource/UI/Icon/Skill/4-8", "Desc": "Swordsman_AirDownShieldSmash_Desc", "Name": "", "Desc_Example": "滞空时，使用盾向下用力砸去，对地面小范围内的敌人造成范围伤害直到落地。", "Name_Example": "盾坠击"}]}], "ActionLinkUI": [{"—————————————————————WarriorActionLinkUI—": "WarriorActionLinkUI————————————————————————"}, {"Id": "Warrior_Action2_<PERSON>_StingDashAttack", "CandidateActions": [{"ActionId": "Warrior_StingUpperSlash", "Trigger": "命中时自动播放动作", "ActionIcon": "ArtResource/UI/Icon/Skill/2-2", "ActionName": "Warrior_StingUpperSlash", "ActionDesc": "Warrior_StingUpperSlash_Desc", "ActionName_Example": "巨刃破空斩", "Desc_Example": ""}, {"ActionId": "Warrior_StingComboSlash", "Trigger": "命中时自动播放动作", "ActionIcon": "ArtResource/UI/Icon/Skill/4-2", "ActionName": "Warrior_StingComboSlash", "ActionDesc": "Warrior_StingComboSlash_Desc", "ActionName_Example": "巨刃剑风斩", "Desc_Example": ""}]}, {"Id": "Warrior_DashSlashAttack1", "CandidateActions": [{"ActionId": "Warrior_DashSlashAttack2", "Trigger": "同一Action再次触发的连招", "ActionIcon": "ArtResource/UI/Icon/Skill/4-6", "ActionName": "Warrior_DashSlashAttack2", "ActionDesc": "Warrior_DashSlashAttack2_Desc", "ActionName_Example": "突袭横扫斩", "Desc_Example": ""}]}, {"Id": "Warrior_AirDoubleStrike0", "CandidateActions": [{"ActionId": "Warrior_AirDoubleStrike1", "Trigger": "同一Action再次触发的连招", "ActionIcon": "ArtResource/UI/Icon/Skill/4-5", "ActionName": "Warrior_AirDoubleStrike1", "ActionDesc": "Warrior_AirDoubleStrike1_Desc", "ActionName_Example": "空中突袭横扫斩", "Desc_Example": ""}]}, {"Id": "<PERSON><PERSON><PERSON>", "CandidateActions": [{"ActionId": "Warrior_CounterDashAttack", "Trigger": "普通反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/2-6", "ActionName": "Warrior_CounterDashAttack", "ActionDesc": "Warrior_CounterDashAttack_Desc", "ActionName_Example": "反击迅斩", "Desc_Example": ""}, {"ActionId": "Warrior_CounterDashAttack_JustBlock", "Trigger": "完美反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/2-1", "ActionName": "Warrior_CounterDashAttack_JustBlock", "ActionDesc": "Warrior_CounterDashAttack_JustBlock_Desc", "ActionName_Example": "反击横扫斩", "Desc_Example": ""}]}, {"Id": "Warrior_ParryB", "CandidateActions": [{"ActionId": "Warrior_CounterStep_Back", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-8", "ActionName": "CounterStep_Back", "ActionDesc": "", "ActionName_Example": "没有使用", "Desc_Example": ""}]}, {"—————————————————————BladeDancerActionLinkUI—": "BladeDancerActionLinkUI————————————————————————"}, {"Id": "BladeDancer_Action2_BladeDancer_RiseComboSlash_JustAttack", "CandidateActions": [{"ActionId": "BladeDancer_RiseComboSlash_AJ", "Trigger": "命中时自动播放动作", "ActionIcon": "ArtResource/UI/Icon/Skill/3-2", "ActionName": "BladeDancer_RiseComboSlash_AJ", "ActionDesc": "BladeDancer_RiseComboSlash_AJ_Desc", "ActionName_Example": "连击升空斩", "Desc_Example": ""}]}, {"Id": "BladeDancer_Action2_BladeDancer_RiseSlash_JustAttack", "CandidateActions": [{"ActionId": "BladeDancer_RiseSlash_AJ", "Trigger": "同一Action再次触发的连招", "ActionIcon": "ArtResource/UI/Icon/Skill/2-6", "ActionName": "BladeDancer_RiseSlash_AJ", "ActionDesc": "BladeDancer_RiseSlash_AJ_Desc", "ActionName_Example": "升空快斩", "Desc_Example": ""}]}, {"Id": "BladeDancer_Action3_BladeDancer_DashAttack_JustAttack", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "Trigger": "普通反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/3-1", "ActionName": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "ActionDesc": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_AJ_Desc", "ActionName_Example": "迅捷突斩", "Desc_Example": ""}, {"ActionId": "BladeDancer_DashAttackLeft_AJ", "Trigger": "完美反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-6", "ActionName": "BladeDancer_DashAttackLeft_AJ", "ActionDesc": "BladeDancer_DashAttackLeft_AJ_Desc", "ActionName_Example": "迅捷左旋斩", "Desc_Example": ""}, {"ActionId": "BladeDancer_DashAttackRight_AJ", "Trigger": "完美反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-6", "ActionName": "BladeDancer_DashAttackRight_AJ", "ActionDesc": "BladeDancer_DashAttackRight_AJ_Desc", "ActionName_Example": "迅捷右旋斩", "Desc_Example": ""}]}, {"Id": "BladeDancer_Action4_BladeDancer_SwrodDanceComboAttack_JustAttack", "CandidateActions": [{"ActionId": "BladeDancer_SwrodDanceDashComboAttack_AJ", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/3-5", "ActionName": "BladeDancer_SwrodDanceDashComboAttack_AJ", "ActionDesc": "BladeDancer_SwrodDanceDashComboAttack_AJ_Desc", "ActionName_Example": "剑舞突袭", "Desc_Example": ""}]}, {"—————————————————————SpearmanActionLinkUI—": "SpearmanActionLinkUI————————————————————————"}, {"Id": "Spearman_Action2_Spearman_SweapAttack1", "CandidateActions": [{"ActionId": "Spearman_SweapAttack2", "Trigger": "命中时自动播放动作", "ActionIcon": "ArtResource/UI/Icon/Skill/4-3", "ActionName": "Spearman_SweapAttack2", "ActionDesc": "<PERSON><PERSON><PERSON>_SweapAttack2_Desc", "ActionName_Example": "回旋乱舞 II", "Desc_Example": ""}]}, {"Id": "<PERSON><PERSON><PERSON>_Action3_<PERSON>pearman_DashSpike", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "Trigger": "同一Action再次触发的连招", "ActionIcon": "ArtResource/UI/Icon/Skill/4-1", "ActionName": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSpike_Combo_Desc", "ActionName_Example": "枪刃三连击", "Desc_Example": ""}, {"ActionId": "<PERSON><PERSON><PERSON>_RiseSlash_Combo", "Trigger": "同一Action再次触发的连招", "ActionIcon": "ArtResource/UI/Icon/Skill/4-2", "ActionName": "<PERSON><PERSON><PERSON>_RiseSlash_Combo", "ActionDesc": "<PERSON><PERSON><PERSON>_RiseSlash_Combo_Desc", "ActionName_Example": "枪刃破空击", "Desc_Example": ""}]}, {"Id": "S<PERSON><PERSON>_Action4_Spearman_DashSweapSlash", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Trigger": "普通反击成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-5", "ActionName": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash_Desc", "ActionName_Example": "跃身斩", "Desc_Example": ""}]}, {"Id": "S<PERSON><PERSON>_Action4_Spearman_BackJumpSweapSlash", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-5", "ActionName": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash_Desc", "ActionName_Example": "跃身斩", "Desc_Example": ""}, {"ActionId": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-9", "ActionName": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2_Desc", "ActionName_Example": "升空击", "Desc_Example": ""}]}, {"Id": "Spearman_Action3_Air_Spearman_AirDashSpike_ForwardDown", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-1", "ActionName": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSpike_HitJump_Desc", "ActionName_Example": "飞空击", "Desc_Example": ""}]}, {"Id": "Spearman_Action4_Air_Spearman_AirDashSweapSlash", "CandidateActions": [{"ActionId": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-5", "ActionName": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash_Desc", "ActionName_Example": "跃身斩", "Desc_Example": ""}, {"ActionId": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "Trigger": "普通反击(闪避)成功时", "ActionIcon": "ArtResource/UI/Icon/Skill/4-9", "ActionName": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "ActionDesc": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2_Desc", "ActionName_Example": "升空击", "Desc_Example": ""}]}]}