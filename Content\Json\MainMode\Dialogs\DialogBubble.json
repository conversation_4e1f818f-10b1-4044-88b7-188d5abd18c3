{"DialogBubbles": [{"DialogBubbleGroupID": "TestGroup", "DialogBubbleGroupType": "SequenceLoop", "Priority": 0, "DialogBubbles": [{"Text": "This is a test", "LifeTime": 3, "NextDialogCoolDown": 3}, {"Text": "This is a test2", "LifeTime": 3, "FadeInTime": 1, "FadeOutTime": 1, "NextDialogCoolDown": 3}, {"Text": "This is a test3", "LifeTime": 3, "FadeInTime": 2, "FadeOutTime": 2, "NextDialogCoolDown": 5}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestGroup2", "DialogBubbleGroupType": "SequenceLoop", "Priority": 0, "DialogBubbles": [{"Text": "Captain<PERSON><PERSON>_Speech1b11", "LifeTime": 10, "FadeInTime": 1, "FadeOutTime": 5, "NextDialogCoolDown": 2}, {"Text": "Captain<PERSON><PERSON>_Speech1b15", "LifeTime": 10, "FadeInTime": 2, "FadeOutTime": 2, "NextDialogCoolDown": 3}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestGroup3", "DialogBubbleGroupType": "SequenceLoop", "Priority": 0, "DialogBubbles": [{"Text": "Captain<PERSON><PERSON>_Speech1b11", "LifeTime": 10, "FadeInTime": 1, "FadeOutTime": 5, "NextDialogCoolDown": 2}, {"Text": "BackGroundText_MainQuest3b5", "LifeTime": 7, "FadeInTime": 1, "FadeOutTime": 5, "NextDialogCoolDown": 1}, {"Text": "BackGroundText_MainQuest4c9", "LifeTime": 3, "FadeInTime": 1, "FadeOutTime": 5, "NextDialogCoolDown": 2}, {"Text": "Captain<PERSON><PERSON>_Speech1b15", "LifeTime": 10, "FadeInTime": 2, "FadeOutTime": 2, "NextDialogCoolDown": 3}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestGroup4", "DialogBubbleGroupType": "SequenceLoop", "Priority": 0, "DialogBubbles": [{"Text": "Captain<PERSON><PERSON>_Speech1b11", "LifeTime": 10, "FadeInTime": 1, "NextDialogCoolDown": 2}, {"Text": "Captain<PERSON><PERSON>_Speech1b15", "LifeTime": 10, "FadeInTime": 2, "FadeOutTime": 2, "NextDialogCoolDown": 3}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestGroup5", "DialogBubbleGroupType": "RandomOnce", "Priority": 2, "DialogBubbles": [{"Text": "Captain<PERSON><PERSON>_Speech4a2", "LifeTime": 3, "FadeOutTime": 1, "NextDialogCoolDown": 4}, {"Text": "BackGroundText_MainQuest1a3", "LifeTime": 3, "FadeInTime": 1, "FadeOutTime": 1, "NextDialogCoolDown": 4}], "CheckCondition": ["DialogCondition.TargetHasWeapon()"]}, {"DialogBubbleGroupID": "TestGroup6", "DialogBubbleGroupType": "RandomLoop", "Priority": 1, "DialogBubbles": [{"Text": "GuardGogaros_Speech2b2", "LifeTime": 5, "NextDialogCoolDown": 5}, {"Text": "Captain<PERSON><PERSON>_Speech1b2", "LifeTime": 5, "NextDialogCoolDown": 5}], "CheckCondition": ["DialogCondition.TargetInRange(400)"]}, {"DialogBubbleGroupID": "TestHit", "DialogBubbleGroupType": "RandomOnce", "Priority": 5, "DialogBubbles": [{"Text": "BackGroundText_MainQuest1b6", "LifeTime": 1, "NextDialogCoolDown": 5}, {"Text": "BubbleText_MainQuest2b5", "LifeTime": 1, "NextDialogCoolDown": 5}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestBattle", "DialogBubbleGroupType": "RandomOnce", "Priority": 4, "DialogBubbles": [{"Text": "BladeDancer_DashAttack", "LifeTime": 1, "NextDialogCoolDown": 5}, {"Text": "BladeDancer_AirDashComboAttack", "LifeTime": 1, "NextDialogCoolDown": 5}], "CheckCondition": []}, {"DialogBubbleGroupID": "TestDior", "DialogBubbleGroupType": "SequenceLoop", "Priority": 0, "DialogBubbles": [{"Text": "PatrolGuard3_Speech4a1", "FadeInTime": 0.5, "FadeOutTime": 0.5, "LifeTime": "ParamPolicyScript.FloatFromRange(3,5)", "NextDialogCoolDown": "ParamPolicyScript.FloatFromRange(6,10)"}, {"Text": "PatrolGuard1_Speech4a1", "FadeInTime": 0.5, "FadeOutTime": 0.5, "LifeTime": "ParamPolicyScript.FloatFromRange(4,6)", "NextDialogCoolDown": "ParamPolicyScript.FloatFromRange(3,7)"}, {"Text": "PatrolGuard2_Speech4a3", "FadeInTime": 0.5, "FadeOutTime": 0.5, "LifeTime": "ParamPolicyScript.FloatFromRange(4,6)", "NextDialogCoolDown": "ParamPolicyScript.FloatFromRange(8,10)"}], "CheckCondition": []}]}