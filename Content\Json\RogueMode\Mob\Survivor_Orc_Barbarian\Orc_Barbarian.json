{"Mob": [{"Id": "Survivor_<PERSON>c_Barbarian", "Tag": ["Goblin"], "AI": ["StopAIBuff", "SurvivorMob_BasicBattle", "MoveToAthenaAndEnemy"], "BpPath": "Core/Characters/Rogue_Mob/Orc/Orc_Barbarian/Orc_Barbarian", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "Goblin", "MountsTypeRotate": false, "Name": "兽人蛮子", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 13, "PAtk": 22, "Balance": 10, "MoveSpeed": [150, 350, 980], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Orc/Orc_Barbarian/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Orc/Orc_Barbarian/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "发呆", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "左踱步", "Id": "Walk_Left", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Action/Walk_Left"]}, "InitAction": true}, {"说明": "右踱步", "Id": "Walk_Right", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Action/Walk_Right"]}, "InitAction": true}, {"说明": "防御 这个和下面那个文件夹里没有", "Id": "Action_Defense", "Cmds": ["Action_Defense"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Battle/Action_Defense"]}, "InitAction": true}, {"说明": "防御成功", "Id": "Action_Defense_Success", "Cmds": ["Action_Defense_Success"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Orc/Orc_Barbarian/Battle/Action_Defense_Success"]}, "InitAction": true}]}]}