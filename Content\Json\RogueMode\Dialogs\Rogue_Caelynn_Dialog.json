{"Dialogs": [{"说明": "Rogue_<PERSON><PERSON>ynn初次对话", "Id": "<PERSON>_<PERSON><PERSON><PERSON>_<PERSON>Dialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_VeryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_VeryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_VeryFirstDialog3"}], "Selections": [{"说明": "Rogue_<PERSON><PERSON><PERSON>故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueCaelynnStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_Caelynn初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog7"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog8"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_First_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Caelynn通关后初次对话", "Id": "Rogue_<PERSON><PERSON><PERSON>_WeeklyRoundFirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "C<PERSON>ynn_WeeklyRound1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Caelynn_WeeklyRound2"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Caelynn_WeeklyRound3"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Caelynn_WeeklyRound_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Caelynn默认开始对话", "Id": "<PERSON>_<PERSON><PERSON><PERSON>_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_FirstDialog2"}], "Selections": [{"说明": "Rogue_<PERSON><PERSON><PERSON>故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueCaelynnStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_<PERSON><PERSON>ynn解锁职业", "特别说明": "为了实现解锁职业跳出另一个UI，然后解锁失败返回对话，解锁成功自动进入下一段对话，这里的NextEventFunc先为空，成功的NextEventFunc在解锁成功的UI蓝图里手动调用", "Conditions": ["DialogCondition.CheckCareerLock(Warrior_Caelynn)"], "EnableChecks": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionUnlockClass", "NextEventFunc": "", "Actions": ["DialogAction.ShowUnlockCareerUI(<PERSON><PERSON><PERSON><PERSON>,100)"], "StillInDialog": true}, {"说明": "Rogue_<PERSON><PERSON>ynn切换职业", "Conditions": ["DialogCondition.CheckCareer<PERSON><PERSON>lock(Warrior_C<PERSON>ynn)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionChangeClass", "NextEventFunc": "DialogAction.DirectGoTo(ChangeClass)", "Actions": []}, {"说明": "Rogue_<PERSON><PERSON>ynn结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "DialogAction.DirectGoTo(EndDialog)", "Actions": []}]}, {"说明": "Rogue_Caelynn默认的重复对话", "Id": "DefaultStoryDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_GuaranteeDialog"}], "Selections": []}, {"说明": "Rogue_Caelynn初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog7"}, {"Type": "Speak", "Id": "Step7", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialog8"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_First_StoryDialog,1)"]}, {"说明": "Rogue_Caelynn初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialogRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialogRepeat2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_StoryFirstDialogRepeat3"}], "Selections": []}, {"说明": "Rogue_Caelynn 别的角色死亡后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_AfterDeathDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_AfterDeathDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_AfterDeathDialog3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_StoryDialog,1)"]}, {"说明": "Rogue_Caelynn 别的角色死亡后的重复对话", "Id": "AfterDeathDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_AfterDeathDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 见到熔岩魔俑后的故事对话", "Id": "ArriveLevel20Dialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ArriveLevel20_1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ArriveLevel20_2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ArriveLevel20_3"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Shard,5)", "DialogAction.SetRogueSwitch(Caelynn_ArriveLevel20_StoryDialog,1)"]}, {"说明": "Rogue_Caelynn 见到熔岩魔俑后的重复对话", "Id": "ArriveLevel20DialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Caelynn_ArriveLevel20Repeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 通关后的重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "C<PERSON>ynn_WeeklyRoundRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 拿到1个圣杯碎片的对话", "Id": "FirstGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainFirstGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_ObtainFirstGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_ObtainFirstGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel01_Dialog,1)"]}, {"说明": "Rogue_Caelynn 拿到1个圣杯碎片的重复对话", "Id": "FirstGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainFirstGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 得知“圣杯誓言石”后的对话", "Id": "GetGrailInfoDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_ObtainGrailOathStone1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_ObtainGrailOathStone2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(C<PERSON>ynn_GetGrailInfo_Dialog,1)"]}, {"说明": "Rogue_Caelynn 得知“圣杯誓言石”后的重复对话", "Id": "GetGrailInfoDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON>ynn_ObtainGrailOathStoneRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 拿到2个圣杯碎片的对话", "Id": "SecondGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainSecondGrail1"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel02_Dialog,1)"]}, {"说明": "Rogue_Caelynn 拿到2个圣杯碎片的重复对话", "Id": "SecondGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 拿到3个圣杯碎片的对话", "Id": "ThirdGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainThirdGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainThirdGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel03_Dialog,1)"]}, {"说明": "Rogue_Caelynn 拿到3个圣杯碎片的重复对话", "Id": "ThirdGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainThirdGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 拿到4个圣杯碎片的对话", "Id": "ForthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainForthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainForthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainForthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel04_Dialog,1)"]}, {"说明": "Rogue_Caelynn 拿到4个圣杯碎片的重复对话", "Id": "ForthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_ObtainForthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 拿到5个圣杯碎片的对话", "Id": "FifthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "C<PERSON>ynn_ObtainFifthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "C<PERSON>ynn_ObtainFifthGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel05_Dialog,1)"]}, {"说明": "Rogue_Caelynn 拿到5个圣杯碎片的重复对话", "Id": "FifthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "C<PERSON>ynn_ObtainFifthGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 解除圣杯封印的对话", "Id": "UnlockGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_UnlockGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_UnlockGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_UnlockGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Caelynn_GrailLevel06_Dialog,1)"]}, {"说明": "Rogue_Caelynn 解除圣杯封印的重复对话", "Id": "UnlockGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_UnlockGrailRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_UnlockGrailRepeat2"}], "Selections": []}, {"说明": "Rogue_Caelynn 击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_KillRealDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_KillRealDeathLord2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_KillRealDeathLord3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Caelynn 击败真·真死骸骑士的重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_KillRealDeathLordRepeat1"}], "Selections": []}, {"说明": "Rogue_Caelynn 送魂之残响", "Id": "GiveEcho", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho2"}, {"Type": "GiveCurrencyToNPC", "Id": "Step2", "NextId": "", "Params": ["Rogue_Soul", "5", "GiveEchoSuccess", "GiveEchoFailure"]}], "Selections": []}, {"说明": "Rogue_Caelynn 送魂之残响成功", "Id": "GiveEchoSuccess", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho4"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho5"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho6"}], "EndClipScript": ["DialogAction.SetRogueSwitch(<PERSON><PERSON><PERSON>_<PERSON>,1)"]}, {"说明": "Rogue_Caelynn 送魂之残响失败", "Id": "GiveEchoFailure", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_FirstGiveEcho8"}], "Selections": []}, {"说明": "Rogue_Caelynn解锁职业成功并自动切换职业", "Id": "UnlockClassSuccess", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_SecondGiveEcho1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 1.5}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 2.5}], "EndClipScript": ["DialogAction.RogueC<PERSON>e<PERSON><PERSON><PERSON>(Warrior_C<PERSON>ynn)"]}, {"说明": "Rogue_<PERSON><PERSON>ynn切换职业", "Id": "ChangeClass", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_SwitchCharacter1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "ChangeClass"}, {"Type": "Wait", "Id": "Step2", "NextId": "Step3", "WaitSec": 1.5}, {"Type": "HideDialogUI", "Id": "Step3", "NextId": "Step4"}, {"Type": "Wait", "Id": "Step4", "NextId": "", "WaitSec": 2.5}], "EndClipScript": ["DialogAction.RogueC<PERSON>e<PERSON><PERSON><PERSON>(Warrior_C<PERSON>ynn)"]}, {"说明": "Rogue_<PERSON><PERSON>ynn结束对话", "Id": "EndDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "<PERSON><PERSON><PERSON>_EndDialog"}], "Selections": []}], "EndDialogScript": []}]}