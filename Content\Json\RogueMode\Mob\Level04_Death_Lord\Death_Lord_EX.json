{"Mob": [{"Id": "Rogue_<PERSON>_Lord_EX", "MobRank": "Boss", "Tag": [""], "AI": ["StopAIBuff", "Death_Lord_EX_ChangeStage", "Death_Lord_EX_ChangeStage2", "RogueMob_BasicBattle"], "BpPath": "Core/Characters/Rogue_Mob/Death_Lord/Death_Lord_EX", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Death_Lord", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 31, "PAtk": 30, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0.5, "Infinity": false}, {"Id": "Rogue_Boss_FirstStage", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "Rogue_Boss_CheckSecondStage", "Stack": 67, "Time": 0, "Infinity": true}, {"Id": "Rogue_Boss_CheckThirdStage", "Stack": 34, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 10000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 10000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Standard_Move", "UnArmed": "Standard_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Death_Lord/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Break_Down"]}, "InitAction": true}, {"说明": "冰剑连斩", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "冰剑连斩2", "Id": "NormalAttack_S1_2", "Cmds": ["NormalAttack_S1_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S1_2"]}, "InitAction": true}, {"说明": "冷冽突袭", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "跳跃冰晶弹", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "荆棘冰牢", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_M1"]}, "InitAction": true}, {"说明": "冰晶连射", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_L1"]}, "InitAction": true}, {"说明": "破霜之剑", "Id": "RageState", "Cmds": ["RageState"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageState_EX"]}, "InitAction": true}, {"说明": "寒冰结界", "Id": "RageAttack_X1", "Cmds": ["RageAttack_X1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_X1"]}, "InitAction": true}, {"说明": "冰河之怒", "Id": "RageAttack_X2", "Cmds": ["RageAttack_X2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_X2"]}, "InitAction": true}, {"说明": "冰河之怒_前置", "Id": "RageAttack_X2_PreCast", "Cmds": ["RageAttack_X2_PreCast"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_X2_PreCast"]}, "InitAction": true}, {"说明": "冰剑连斩EX", "Id": "RageAttack_S1", "Cmds": ["RageAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S1"]}, "InitAction": true}, {"说明": "冰剑连斩EX_2", "Id": "RageAttack_S1_2", "Cmds": ["RageAttack_S1_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S1_2"]}, "InitAction": true}, {"说明": "冷冽突袭EX", "Id": "RageAttack_S2", "Cmds": ["RageAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S2"]}, "InitAction": true}, {"说明": "冷冽突袭EX2", "Id": "RageAttack_S2_2", "Cmds": ["RageAttack_S2_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S2_2"]}, "InitAction": true}, {"说明": "永冻之枪", "Id": "RageAttack_S3", "Cmds": ["RageAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S3"]}, "InitAction": true}, {"说明": "荆棘冰牢EX", "Id": "RageAttack_M1", "Cmds": ["RageAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M1"]}, "InitAction": true}, {"说明": "冷冽剑", "Id": "RageAttack_M2", "Cmds": ["RageAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M2"]}, "InitAction": true}, {"说明": "死灵召唤-level0-骷髅兵", "Id": "RageAttack_M3", "Cmds": ["RageAttack_M3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M3"]}, "InitAction": true}, {"说明": "死灵召唤-level1-弓箭手", "Id": "RageAttack_M4", "Cmds": ["RageAttack_M4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M4"]}, "InitAction": true}, {"说明": "死灵召唤-level2-铁锤哥", "Id": "RageAttack_M5", "Cmds": ["RageAttack_M5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M5"]}, "InitAction": true}, {"说明": "死灵召唤-level3-弩哥", "Id": "RageAttack_M6", "Cmds": ["RageAttack_M6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M6"]}, "InitAction": true}, {"说明": "死灵召唤-level4-盖伦哥", "Id": "RageAttack_M7", "Cmds": ["RageAttack_M7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M7"]}, "InitAction": true}, {"说明": "死灵召唤-level5-死神镰刀", "Id": "RageAttack_M8", "Cmds": ["RageAttack_M8"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M8"]}, "InitAction": true}, {"说明": "死灵召唤-level6-冰魔俑", "Id": "RageAttack_M9", "Cmds": ["RageAttack_M9"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M9"]}, "InitAction": true}, {"说明": "冰晶连射EX", "Id": "RageAttack_L1", "Cmds": ["RageAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_L1"]}, "InitAction": true}, {"说明": "闪避-后", "Id": "Dodge_DashStep_Back", "Cmds": ["Dodge_DashStep_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Death_Lord/Battle/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "闪避-左", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "闪避-右", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "发呆", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/Stare01"]}, "InitAction": true}, {"说明": "向前走(发呆)", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/Walk_Front"]}, "InitAction": true}, {"说明": "向后闪避", "Id": "DashStep_Back", "Cmds": ["DashStep_Back"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Death_Lord/Battle/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "向左闪避", "Id": "DashStep_Left", "Cmds": ["DashStep_Left"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "向右闪避", "Id": "DashStep_Right", "Cmds": ["DashStep_Right"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "左转90度", "Id": "TurnLeft_90", "Cmds": ["TurnLeft_90"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnLeft_90"]}, "InitAction": true}, {"说明": "左转180度", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnLeft_180"]}, "InitAction": true}, {"说明": "右转90度", "Id": "TurnRight_90", "Cmds": ["TurnRight_90"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnRight_90"]}, "InitAction": true}, {"说明": "右转180度", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnRight_180"]}, "InitAction": true}, {"说明": "前冲", "Id": "Dodge_DashStep_Forward", "Cmds": ["Dodge_DashStep_Forward"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Forward"]}, "InitAction": true}]}], "Buff": [], "Bullet": [], "AOE": []}