{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Rogue_Skeleton_Example", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 3}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 0}], "MinActionCD": 7, "MaxActionCD": 10}, {"Id": "Stare01", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 1}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 2.8, "MaxActionCD": 3.5}]}]}