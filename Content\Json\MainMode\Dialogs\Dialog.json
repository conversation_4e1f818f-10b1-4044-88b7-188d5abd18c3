{"Dialogs": [{"Id": "TestDialog", "FirstClip": "Intro", "NpcId": [{"Slot": 0, "NpcId": "<PERSON><PERSON><PERSON><PERSON>"}], "Clips": [{"Id": "Intro", "FirstEvent": "FocusPlayer", "Dialogs": [{"Type": "FocusTarget", "Id": "FocusPlayer", "NextId": "DoSomeAction", "TargetType": "Player", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "DoAction", "Id": "DoSomeAction", "NextId": "WaitForSec", "TargetType": "Player", "ChaSlot": 0, "TodoActionId": "Warrior_Bounced"}, {"Type": "Wait", "Id": "WaitForSec", "NextId": "SayIntro", "WaitSec": 0.6}, {"Type": "Speak", "Id": "SayIntro", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "TestDialog_SpeakerName0", "Text": "TestDialog_PlayerSpeech0"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "TestDialog_PlayerSelection0", "NextEventFunc": "DialogAction.DirectGoTo(Help_Mai)", "Actions": ["DialogAction.ChangeTargetDialog(Luozi_UnHappy)", "DialogAction.ShowToast(TestDialog_Toast0)"]}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "TestDialog_PlayerSelection1", "NextEventFunc": "DialogAction.DirectGoTo(Help_Luo)"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "EnableChecks": ["DialogCondition.Never()"], "Text": "TestDialog_PlayerSelection2", "NextEventFunc": "DialogAction.DirectGoTo(Help_Luo)"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "TestDialog_PlayerSelection3", "NextEventFunc": ""}]}, {"Id": "Help_Mai", "FirstEvent": "FocusMai", "Dialogs": [{"Type": "FocusTarget", "Id": "FocusMai", "NextId": "<PERSON><PERSON><PERSON>", "TargetType": "AttendNpc", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "<PERSON><PERSON><PERSON>", "NextId": "FocusLuo", "TargetType": "AttendNpc", "Slot": 0, "Name": "TestDialog_SpeakerName2", "Text": "TestDialog_PlayerSpeech1"}, {"Type": "FocusTarget", "Id": "FocusLuo", "NextId": "LuoCry", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "LuoCry", "NextId": "", "TargetType": "AttendNpc", "Slot": 0, "Name": "TestDialog_SpeakerName1", "Text": "TestDialog_PlayerSpeech2"}], "Selections": []}, {"Id": "Help_<PERSON>o", "FirstEvent": "FocusMai", "Dialogs": [{"Type": "FocusTarget", "Id": "FocusMai", "NextId": "Mai<PERSON><PERSON>", "TargetType": "AttendNpc", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Mai<PERSON><PERSON>", "NextId": "FocusLuo", "TargetType": "AttendNpc", "Slot": 0, "Name": "TestDialog_SpeakerName2", "Text": "TestDialog_PlayerSpeech3"}, {"Type": "FocusTarget", "Id": "FocusLuo", "NextId": "<PERSON><PERSON><PERSON><PERSON>", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "<PERSON><PERSON><PERSON><PERSON>", "NextId": "", "TargetType": "AttendNpc", "Slot": 0, "Name": "TestDialog_SpeakerName1", "Text": "TestDialog_PlayerSpeech4"}], "Selections": []}]}, {"Id": "Luozi_UnHappy", "FirstClip": "Intro", "NpcId": [{"Slot": 0, "NpcId": "<PERSON><PERSON><PERSON><PERSON>"}], "Clips": [{"Id": "Intro", "FirstEvent": "FocusLuo", "Dialogs": [{"Type": "FocusTarget", "Id": "FocusLuo", "NextId": "LuoSpeak", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "LuoSpeak", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "TestDialog_SpeakerName1", "Text": "TestDialog_PlayerSpeech5"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "TestDialog_PlayerSelection4", "NextEventFunc": "DialogAction.DirectGoTo(Luo_Answer)", "Actions": ["DialogAction.ChangeTargetDialog(TestDialog)", "DialogAction.ShowToast(TestDialog_Toast1)"]}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "EnableChecks": ["DialogCondition.Never()"], "Text": "TestDialog_PlayerSelection5", "NextEventFunc": ""}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "TestDialog_PlayerSelection6", "NextEventFunc": ""}]}, {"Id": "Luo_Answer", "FirstEvent": "FocusLuo", "Dialogs": [{"Type": "FocusTarget", "Id": "FocusLuo", "NextId": "LuoSpeak", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "LuoSpeak", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "TestDialog_SpeakerName1", "Text": "TestDialog_PlayerSpeech6"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "EnableChecks": ["DialogCondition.Never()"], "Text": "TestDialog_PlayerSelection7", "NextEventFunc": ""}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "EnableChecks": ["DialogCondition.Never()"], "Text": "TestDialog_PlayerSelection8", "NextEventFunc": ""}]}]}]}