{"AIScript": [{"说明": "测试动作的Action", "Id": "Infantry_TestAction", "Condition": [""], "OnReady": [], "Action": ["MobAIScript.AIDoAction(NormalAttack_S1)"]}, {"说明": "基础战斗套组", "Id": "NormalInfantryBasicBattle", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1000)"], "OnReady": [], "Action": ["HumanInfantryAIScript.RandomNormalInfantryCombo(0,300,300,700,700,1000)"]}, {"说明": "拔刀", "Id": "InfantryArm", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,1000)", "MobAIScript.CheckUnarmed()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(EquipmentWeapon)"]}, {"说明": "收刀", "Id": "InfantryUnarm", "Condition": ["MobAIScript.CheckArmed()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(UnequipmentWeapon)"]}, {"说明": "转向刺激源（受击、同伴信号、声音）", "Id": "InfantryTurnToStimulate", "Condition": ["HumanInfantryAIScript.CheckHasStimulate()", "MobAIScript.CheckHasEnemyInSightRange()"], "OnReady": [], "Action": ["MobAIScript.AITurnToStimulateDoAction()"]}, {"说明": "保底Action", "Id": "Infantry_Daze", "Condition": [""], "OnReady": [], "Action": ["MobAIScript.AIDoAction(IdleDaze)"]}, {"说明": "在有NPCNoCombat的Buff的情况下，播放Buff.Params里IdleAction的Action", "Id": "NPCNoCombatIdle", "Condition": ["NPCAIScript.CheckNpcDoNoCombatIdle()"], "OnReady": [], "Action": ["NPCAIScript.DoNoCombatIdle()"]}]}