{"Bullet": [{"法器": "_________________________________________________________Roguelike法器Bullet____________________________________________________________"}, {"Id": "Rogue_CannonBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_CannonBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_ShadowKnife", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_ShadowKnife", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.3, "Life": 999, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_WindKnife", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/item/Rogue_WindKnife", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 999, "HitDelay": 0.3, "Life": 999, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHitByAttackInfo()"], "OnRemoved": []}, {"Id": "Bullet_SwordLight", "Tag": ["<PERSON><PERSON>", "Magic"], "BpPath": "Core/Item/Bullet/PlayerSkill/Bullet_SwordLight", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 99, "HitDelay": 1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Sparrow_UltHit_Red)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Character/1_Fighter/Fighter_Skill_ExpodSword_Hit_Cue)"], "OnRemoved": ["BulletScript.CreateVFXOnRemoved(ArtResource/ProjectRogue/VFX/ParagonZ/P_Sparrow_UltHitWorld_Red)"]}, {"描述": "玩家丢出来的镰刀", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Scythe", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 5, "HitDelay": 0.3, "Life": 100, "Size": 100, "Type": "Bullet", "OnCreate": ["BulletScript.AddBulletRotation(-23,-10,20)", "BulletScript.AddBulletOriginRotation(0,2,0)"], "OnRemoved": ["BulletScript.CreateAoEOnTimeOut(Rogue_Aoe_ScytheExplosion,0.3)"]}]}