{"Mob": [{"Id": "VagabondageVendor", "Tag": ["Npc"], "BpPath": "Core/Characters/NPC/Shopkeeper/Shopkeeper_New", "AI": [], "AIOrder": [], "StartFightingWillLevel": 1, "MaxFightingWillLevel": 4, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "VagabondageVendor_Name", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightHalfAngleDregee": 45, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 50, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 50, "Attack": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Shopkeeper/Move"]}, "InitAction": true}, {"Id": "Dead", "BeCancelledTags": {}, "CanUseOnFalling": true, "Priority": 101, "Anim": {"Period": false, "StateFunc": "MontageAnimPickFunc.Random(1)", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/Dead1", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/Dead2"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/HurtFromFront", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/AirHurt", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/AirHurt"]}, "InitAction": true}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/BlowFromBack", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Shopkeeper/Hurt/BlowFromBack"]}}, {"说明": "伸展1", "Id": "Shop<PERSON><PERSON>er_Body_Stretch_1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Body_Stretch_1"]}, "InitAction": true}, {"说明": "伸展2", "Id": "Shop<PERSON><PERSON>er_Body_Stretch_2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Body_Stretch_2"]}, "InitAction": true}, {"说明": "伸展3", "Id": "<PERSON><PERSON><PERSON>er_Body_Stretch_3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Body_Stretch_3"]}, "InitAction": true}, {"说明": "伸展4", "Id": "<PERSON><PERSON><PERSON>er_Body_Stretch_4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Body_Stretch_4"]}, "InitAction": true}, {"说明": "鼓掌1", "Id": "Shop<PERSON><PERSON>er_Clap_1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Clap_1"]}, "InitAction": true}, {"说明": "鼓掌2", "Id": "Shop<PERSON><PERSON><PERSON>_Clap_2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Clap_2"]}, "InitAction": true}, {"说明": "过来1", "Id": "ShopKeeper_ComeOn1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_ComeOn1"]}, "InitAction": true}, {"说明": "过来2", "Id": "ShopKeeper_ComeOn2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_ComeOn2"]}, "InitAction": true}, {"说明": "欢呼1", "Id": "<PERSON><PERSON><PERSON>er_Crowd_Cheer_1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Crowd_Cheer_1"]}, "InitAction": true}, {"说明": "Sad", "Id": "<PERSON><PERSON><PERSON>er_Deep_Breath_Sad", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Deep_Breath_Sad"]}, "InitAction": true}, {"说明": "不知道1", "Id": "<PERSON><PERSON><PERSON><PERSON>_Don_t_Know1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Don_t_Know1"]}, "InitAction": true}, {"说明": "不知道2", "Id": "<PERSON><PERSON><PERSON><PERSON>_Don_t_Know2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Don_t_Know2"]}, "InitAction": true}, {"说明": "不知道3", "Id": "<PERSON><PERSON><PERSON><PERSON>_Don_t_Know3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Don_t_Know3"]}, "InitAction": true}, {"说明": "Whatever", "Id": "Shop<PERSON>eeper_Whatever", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Whatever"]}, "InitAction": true}, {"说明": "指指点点左", "Id": "ShopKeeper_PointToLeft", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_PointToLeft"]}, "InitAction": true}, {"说明": "指指点点右", "Id": "ShopKeeper_PointToRight", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_PointToRight"]}, "InitAction": true}, {"说明": "拒绝", "Id": "ShopKeeper_Refuse", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Refuse"]}, "InitAction": true}, {"说明": "展示商品1", "Id": "ShopKeeper_Showing_Goods_1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Showing_Goods_1"]}, "InitAction": true}, {"说明": "展示商品2", "Id": "Shop<PERSON>eeper_Showing_Goods_2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Showing_Goods_2"]}, "InitAction": true}, {"说明": "Dance1", "Id": "Shop<PERSON><PERSON>er_Standing_ovation_4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Standing_ovation_4"]}, "InitAction": true}, {"说明": "Dance2", "Id": "Shop<PERSON><PERSON>er_Standing_ovation_5", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_Standing_ovation_5"]}, "InitAction": true}, {"说明": "对话1", "Id": "ShopKeeper_Dialog1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Dialog/ShopKeeper_Dialog1"]}, "InitAction": true}, {"说明": "对话2", "Id": "ShopKeeper_Dialog2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Dialog/ShopKeeper_Dialog2"]}, "InitAction": true}, {"说明": "对话3", "Id": "ShopKeeper_Dialog3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Dialog/ShopKeeper_Dialog3"]}, "InitAction": true}, {"说明": "对话4", "Id": "ShopKeeper_Dialog4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Dialog/ShopKeeper_Dialog4"]}, "InitAction": true}, {"说明": "对话5", "Id": "ShopKeeper_Dialog5", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Dialog/ShopKeeper_Dialog5"]}, "InitAction": true}, {"说明": "向左转", "Id": "ShopKeeper_TurnLeft", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_TurnLeft180"]}, "InitAction": true}, {"说明": "向右转", "Id": "ShopKeeper_TurnRight", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Shopkeeper/Action/ShopKeeper_TurnRight180"]}, "InitAction": true}]}]}