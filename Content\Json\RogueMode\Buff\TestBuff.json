{"Buff": [{"Id": "LuoZi_Intellegence", "Tag": ["Stupid", "<PERSON><PERSON>"], "Priority": 0, "TickTime": 0, "MaxStack": 1, "Property": [{"Hp": 20, "Mp": 20}, {"Hp": 10000, "Mp": 1}], "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "SpecState": {"Immortal": false, "Invincible": false, "Stop": false, "Untouchable": false}, "OnOccur": [], "OnTick": [], "OnRemoved": [], "OnHit": [], "OnBeHurt": [], "OnKill": [], "OnBeKilled": [], "OnChangeAction": []}, {"Id": "Luozi_Warning", "Tag": ["TEST"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["DialogBuffs.SpeakOnDissatisfy(0,0.4,罗咸钢,噢哟，别打了呀，再打我们朋友都没得做了,5)", "DialogBuffs.SpeakOnDissatisfy(1,0.3,罗咸钢,你小逼样子要造反了是伐，看老子嫩死你,4)"]}, {"Id": "Test_NoDamage", "Tag": ["TEST"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.TakeNoDamageAndPrint()"]}, {"Id": "Test_Attack999", "Tag": ["TEST", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.TakeDamage999999()"]}]}