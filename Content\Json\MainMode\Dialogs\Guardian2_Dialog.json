{"Dialogs": [{"________________________________________11.29__": "__新对话______________________________________________________"}, {"________________________________________主线任务__": "__2b______________________________________________________"}, {"说明": "主线任务2b对话单次", "Id": "GuardGogaros_MianQuest2b", "FirstClip": "Dialog2b1", "NpcId": [], "Clips": [{"Id": "Dialog2b1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2b1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "GuardGogaros_Speech2b2", "NextEventFunc": "DialogAction.DirectGoTo(Dialog2b2)"}]}, {"Id": "Dialog2b2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2b3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2b4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2b5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2b6"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRoleSwitchTo(RodianStory02,4)"]}, {"________________________________________主线任务__": "__2c______________________________________________________"}, {"说明": "主线任务2c对话单次", "Id": "GuardGogaros_MianQuest2c", "FirstClip": "Dialog2c1", "NpcId": [], "Clips": [{"Id": "Dialog2c1", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2c1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2c2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2c3"}], "Selections": []}]}, {"说明": "主线任务2c对话结束后常驻", "Id": "GuardGogaros_MianQuest2cNormal", "FirstClip": "Dialog2cNormal", "NpcId": [], "Clips": [{"Id": "Dialog2cNormal", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2c2"}], "Selections": []}]}, {"________________________________________主线任务__": "__2d______________________________________________________"}, {"说明": "主线任务2d对话单次", "Id": "GuardGogaros_MianQuest2d", "FirstClip": "Dialog2d", "NpcId": [], "Clips": [{"Id": "Dialog2d", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2d1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2d2"}], "Selections": []}]}, {"说明": "主线任务2d对话结束后常驻", "Id": "GuardGogaros_MianQuest2dNormal", "FirstClip": "Dialog2dNormal", "NpcId": [], "Clips": [{"Id": "Dialog2dNormal", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2d2"}], "Selections": []}]}, {"________________________________________主线任务__": "__4a______________________________________________________"}, {"Id": "GuardGogaros_MianQuest4a", "FirstClip": "Dialog4a", "NpcId": [], "Clips": [{"Id": "Dialog4a", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "GuardB_TurnLeft", "ActionId_TurnRight": "GuardB_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech4a1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech4a2"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech4a3"}], "Selections": []}]}, {"________________________________________11.23__": "__旧对话______________________________________________________"}, {"说明": "GuardGogaros_FirstDialog", "Id": "GuardGogaros_FirstDialog", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Active"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech0"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_HeadScratch"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech1"}, {"Type": "DoAction", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Show"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech2"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech19"}, {"Type": "DoAction", "Id": "Step8", "NextId": "Step9", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_LongSpeech"}, {"Type": "Speak", "Id": "Step9", "NextId": "Step10", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech3"}], "Selections": []}]}, {"说明": "GuardGogaros_SpeakOnMine200", "Id": "GuardGogaros_SpeakOnMine200", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech4"}], "Selections": []}]}, {"说明": "GuardGogaros_SpeakOnMine120", "Id": "GuardGogaros_SpeakOnMine120", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech5"}], "Selections": []}]}, {"说明": "GuardGogaros_SpeakOnMine60", "Id": "GuardGogaros_SpeakOnMine60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech6"}], "Selections": []}]}, {"说明": "GuardGogaros_SpeakOnMine0", "Id": "GuardGogaros_SpeakOnMine0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "FocusTarget", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech7"}], "Selections": []}]}, {"说明": "GuardGogaros_Goblin100", "Id": "GuardGogaros_Goblin100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Exercise1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech8"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech20"}], "Selections": []}]}, {"说明": "GuardGogaros_Goblin60", "Id": "GuardGogaros_Goblin60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Exercise2"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech9"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech21"}], "Selections": []}]}, {"说明": "GuardGogaros_Goblin30", "Id": "GuardGogaros_Goblin30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_ShortSpeech1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech10"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech11"}, {"Type": "Speak", "Id": "Step4", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech22"}], "Selections": []}]}, {"说明": "GuardGogaros_Goblin0", "Id": "GuardGogaros_Goblin0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_Cheer"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech12"}], "Selections": []}]}, {"说明": "GuardGogaros_WereRat100", "Id": "GuardGogaros_WereRat100", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_ShortSpeech1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech13"}], "Selections": []}]}, {"说明": "GuardGogaros_WereRat60", "Id": "GuardGogaros_WereRat60", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_MediumSpeech1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech14"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_WannaFight"}, {"Type": "Speak", "Id": "Step4", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech15"}], "Selections": []}]}, {"说明": "GuardGogaros_WereRat30", "Id": "GuardGogaros_WereRat30", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_LetsGo"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech16"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_LongSpeech"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech17"}, {"Type": "Speak", "Id": "Step5", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech23"}], "Selections": []}]}, {"说明": "GuardGogaros_WereRat0", "Id": "GuardGogaros_WereRat0", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "GuardB_HeadScratch"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "GuardGogaros_Name", "Text": "GuardGogaros_Speech18"}], "Selections": []}]}]}