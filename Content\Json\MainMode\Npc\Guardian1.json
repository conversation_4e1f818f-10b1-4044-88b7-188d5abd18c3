{"Mob": [{"Id": "Guardian1", "Tag": ["Npc"], "BpPath": "Core/Characters/NPC/GuardA/GuardA_New", "AI": ["InfantryArm", "NormalInfantryBasicBattle", "InfantryTurnToStimulate", "InfantryUnarm"], "AIOrder": [], "StartFightingWillLevel": 1, "MaxFightingWillLevel": 4, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "Guardian1_Name", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 500, "SightHalfAngleDregee": 135, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 50, "Attack": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [], "Equipments": [{"Id": "InfantrySword", "Rate": 1}, {"Id": "InfantryShield", "Rate": 1}], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Unarmed_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Move", "UnArmed": "Unarmed_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Guard/Move_SwordShield"]}, "InitAction": true}, {"Id": "Unarmed_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Guard/Move"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "MontageAnimPickFunc.Random(1)", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/Hurt/Dead1", "ArtResource/Anim/Montage/NPC/Guard/Hurt/Dead2"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Front_SwordShield", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Back", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Air", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromBack", "ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromBack"]}}, {"说明": "拔刀动作", "Id": "EquipmentWeapon", "Cmds": ["EquipmentWeapon"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/EquipWeapon_SwordShield"]}, "InitAction": true}, {"说明": "收刀动作", "Id": "UnequipmentWeapon", "Cmds": ["UnequipmentWeapon"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/UnequipWeapon_SwordShield"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S3"]}, "InitAction": true}, {"说明": "近距离攻击4", "Id": "NormalAttack_S4_Combo", "Cmds": ["NormalAttack_S4_Combo"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S4_Combo"]}, "InitAction": true}, {"说明": "近距离攻击5", "Id": "NormalAttack_S5_Combo", "Cmds": ["NormalAttack_S5_Combo"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S5_Combo"]}, "InitAction": true}, {"说明": "近距离攻击6", "Id": "NormalAttack_S6_Counter", "Cmds": ["NormalAttack_S6_Counter"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S6_Counter"]}, "InitAction": true}, {"说明": "近距离攻击7", "Id": "NormalAttack_S7", "Cmds": ["NormalAttack_S7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_S7"]}, "InitAction": true}, {"说明": "中距离攻击1", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_M1"]}, "InitAction": true}, {"说明": "中距离攻击2", "Id": "NormalAttack_M2", "Cmds": ["NormalAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_M2"]}, "InitAction": true}, {"说明": "中距离攻击3", "Id": "NormalAttack_M3", "Cmds": ["NormalAttack_M3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_M3"]}, "InitAction": true}, {"说明": "远距离攻击1", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "前跳", "Id": "Dodge_DashStep_Front", "Cmds": ["Dodge_DashStep_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "左跳", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "右跳", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back", "Cmds": ["Dodge_DashStep_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back_End", "Cmds": ["Dodge_DashStep_Back_End"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Back_End"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back_Tired", "Cmds": ["Dodge_DashStep_Back_Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Dodge_DashStep_Back_Tired"]}, "InitAction": true}, {"说明": "向前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Walk_Front"]}, "InitAction": true}, {"说明": "向后走", "Id": "Walk_Back", "Cmds": ["Walk_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Dodge/Walk_Back"]}, "InitAction": true}, {"说明": "疲劳发呆动作", "Id": "Tired", "Cmds": ["Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/Attack/Tired"]}, "InitAction": true}, {"说明": "一直防御", "Id": "DefenseLoop", "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/SwordShield/DefenseLoop"]}}, {"说明": "鞠躬", "Id": "GuardA_Bow", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_Bow"]}, "InitAction": true}, {"说明": "叹气", "Id": "GuardA_Deep_Breath_Sad", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_Deep_Breath_Sad"]}, "InitAction": true}, {"说明": "敬礼", "Id": "GuardA_Salute", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_Salute"]}, "InitAction": true}, {"说明": "长对话", "Id": "GuardA_LongSpeech", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_LongSpeech"]}, "InitAction": true}, {"说明": "中对话", "Id": "GuardA_MediumSpeech", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_MediumSpeech"]}, "InitAction": true}, {"说明": "否认", "Id": "GuardA_Nope", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_Nope"]}, "InitAction": true}, {"说明": "短对话1", "Id": "GuardA_ShortSpeech1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_ShortSpeech1"]}, "InitAction": true}, {"说明": "短对话2", "Id": "GuardA_ShortSpeech2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_ShortSpeech2"]}, "InitAction": true}, {"说明": "向左转", "Id": "GuardA_TurnLeft", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_TurnLeft180"]}, "InitAction": true}, {"说明": "向右转", "Id": "GuardA_TurnRight", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_TurnRight180"]}, "InitAction": true}]}]}