{"Line": "--- 弃用 请看 RougeItemAction 表 --- ", "CharacterBaseAction": [{"Line": "------------------------------ Player <PERSON><PERSON><PERSON>(Swordman) 喝药+法器 ------------------------------"}, {"Id": "<PERSON>_<PERSON><PERSON><PERSON>", "Actions": [{"说明": "喝药水的动作", "Id": "DrinkPotion_Rogue", "Cmds": [""], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/Drink_Rogue"]}}, {"说明": "旋风护体", "Id": "WindProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_WindProtect"]}}, {"说明": "炮车召唤", "Id": "CannonSummon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_SummonCannon"]}}, {"说明": "冰盾冲锋", "Id": "FrozenDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_FrozenDash"]}}, {"说明": "光盾", "Id": "AegisImpact_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_AegisImpact"]}}, {"说明": "雷锤", "Id": "LightingHammer_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_LightingHammer"]}}, {"说明": "暗影飞刀", "Id": "ShadowHiddenWeapon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ShadowHiddenWeapon"]}}, {"说明": "投掷镰刀", "Id": "ThrowScythe_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ThrowScythe"]}}, {"说明": "圆月斩", "Id": "CircleSlash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_CircleSlash"]}}, {"说明": "法器-唤龙", "Id": "Item_SummonD<PERSON>on", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_Move", "SwordShield_Jump"], "1": ["SwordShield_InitAttack", "SwordShield_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_EternalDragonflame"]}}]}, {"Line": "------------------------------ Player <PERSON><PERSON><PERSON>(Swordman) PreLoad 觉醒动作 ------------------------------"}, {"Id": "Player_<PERSON><PERSON><PERSON>_PreLoad", "Actions": [{"说明": "觉醒技能-吸血", "Id": "AwakeSkill_BloodlyThirsty", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/AwakeSkill/AS_JourneyOfLife"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-地震", "Id": "AwakeSkill_Earthquake", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/AwakeSkill/AS_GodofDestructionsPower"]}, "Cost": {"AP": 150}}, {"说明": "觉醒技能-狂化", "Id": "AwakeSkill_<PERSON><PERSON>rker", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/AwakeSkill/AS_GodofWarsMight"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-唤龙", "Id": "AwakeSkill_DragonSummon", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/AwakeSkill/AS_EternalDragonflame"]}, "Cost": {"AP": 1000}}, {"说明": "取消觉醒技能", "Id": "AwakeSkill_Cancel_1", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/AwakeSkill/AS_Action_CancelEnd"]}, "Cost": {}}]}, {"Line": "------------------------------ Player <PERSON><PERSON>(<PERSON><PERSON><PERSON>) 喝药+法器 ------------------------------"}, {"Id": "Player_Sola", "Actions": [{"说明": "喝药水的动作", "Id": "DrinkPotion_Rogue", "Cmds": [""], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Drink_Rogue"]}}, {"说明": "旋风护体", "Id": "WindProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_WindProtect"]}}, {"说明": "炮车召唤", "Id": "CannonSummon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_SummonCannon"]}}, {"说明": "冰盾冲锋", "Id": "FrozenDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_FrozenDash"]}}, {"说明": "光盾", "Id": "AegisImpact_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_AegisImpact"]}}, {"说明": "雷锤", "Id": "LightingHammer_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_LightingHammer"]}}, {"说明": "暗影飞刀", "Id": "ShadowHiddenWeapon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ShadowHiddenWeapon"]}}, {"说明": "投掷镰刀", "Id": "ThrowScythe_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ThrowScythe"]}}, {"说明": "圆月斩", "Id": "CircleSlash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"], "1": ["S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_CircleSlash"]}}]}, {"Line": "------------------------------ Player <PERSON><PERSON>(<PERSON><PERSON><PERSON>) PreLoad 觉醒动作 ------------------------------"}, {"Id": "Player_Sola_PreLoad", "Actions": [{"说明": "觉醒技能-吸血", "Id": "AwakeSkill_BloodlyThirsty", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/AwakeSkill/AS_JourneyOfLife"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-地震", "Id": "AwakeSkill_Earthquake", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Wu<PERSON><PERSON><PERSON>_<PERSON>/Battle/Attack_GAS1"]}, "Cost": {"AP": 150}}, {"说明": "觉醒技能-狂化", "Id": "AwakeSkill_<PERSON><PERSON>rker", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/AwakeSkill/AS_GodofWarsMight"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-唤龙", "Id": "AwakeSkill_DragonSummon", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/AwakeSkill/AS_EternalDragonflame"]}, "Cost": {"AP": 1000}}, {"说明": "取消觉醒技能", "Id": "AwakeSkill_Cancel_1", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/AwakeSkill/AS_Action_CancelEnd"]}, "Cost": {}}]}, {"Line": "------------------------------ Player <PERSON><PERSON><PERSON>(Warrior) 喝药+法器 ------------------------------"}, {"Id": "Player_Tierdagon", "Actions": [{"说明": "喝药水的动作", "Id": "DrinkPotion_Rogue", "Cmds": [""], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/Drink_Rogue"]}}, {"说明": "旋风护体", "Id": "WindProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_WindProtect"]}}, {"说明": "炮车召唤", "Id": "CannonSummon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_SummonCannon"]}}, {"说明": "冰盾冲锋", "Id": "FrozenDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_FrozenDash"]}}, {"说明": "光盾", "Id": "AegisImpact_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_AegisImpact"]}}, {"说明": "雷锤", "Id": "LightingHammer_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_LightingHammer"]}}, {"说明": "暗影飞刀", "Id": "ShadowHiddenWeapon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ShadowHiddenWeapon"]}}, {"说明": "投掷镰刀", "Id": "ThrowScythe_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ThrowScythe"]}}, {"说明": "圆月斩", "Id": "CircleSlash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"], "1": ["GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_CircleSlash"]}}]}, {"Line": "------------------------------ Player <PERSON><PERSON><PERSON>(Warrior) PreLoad 觉醒动作 ------------------------------"}, {"Id": "Player_Tierdagon_PreLoad", "Actions": [{"说明": "觉醒技能-吸血", "Id": "AwakeSkill_BloodlyThirsty", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/AwakeSkill/AS_JourneyOfLife"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-地震", "Id": "AwakeSkill_Earthquake", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Wu<PERSON><PERSON><PERSON>_<PERSON>/Battle/Attack_GAS1"]}, "Cost": {"AP": 150}}, {"说明": "觉醒技能-狂化", "Id": "AwakeSkill_<PERSON><PERSON>rker", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/AwakeSkill/AS_GodofWarsMight"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-唤龙", "Id": "AwakeSkill_DragonSummon", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/AwakeSkill/AS_EternalDragonflame"]}, "Cost": {"AP": 1000}}, {"说明": "取消觉醒技能", "Id": "AwakeSkill_Cancel_1", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/AwakeSkill/AS_Action_CancelEnd"]}, "Cost": {}}]}, {"Line": "------------------------------ Player <PERSON>(BladeDancer) 喝药+法器 ------------------------------"}, {"Id": "<PERSON><PERSON><PERSON>", "Actions": [{"说明": "喝药水的动作", "Id": "DrinkPotion_Rogue", "Cmds": [""], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Drink_Rogue"]}}, {"说明": "旋风护体", "Id": "WindProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_WindProtect"]}}, {"说明": "炮车召唤", "Id": "CannonSummon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_SummonCannon"]}}, {"说明": "冰盾冲锋", "Id": "FrozenDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_FrozenDash"]}}, {"说明": "光盾", "Id": "AegisImpact_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_AegisImpact"]}}, {"说明": "雷锤", "Id": "LightingHammer_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_LightingHammer"]}}, {"说明": "暗影飞刀", "Id": "ShadowHiddenWeapon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ShadowHiddenWeapon"]}}, {"说明": "投掷镰刀", "Id": "ThrowScythe_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ThrowScythe"]}}, {"说明": "圆月斩", "Id": "CircleSlash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"], "1": ["TS_InitAttack", "TS_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_CircleSlash"]}}]}, {"Line": "------------------------------ Player <PERSON>(BladeDancer) PreLoad 觉醒动作 ------------------------------"}, {"Id": "Player_<PERSON>_PreLoad", "Actions": [{"说明": "觉醒技能-吸血", "Id": "AwakeSkill_BloodlyThirsty", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/AwakeSkill/AS_JourneyOfLife"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-地震", "Id": "AwakeSkill_Earthquake", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/AwakeSkill/AS_GodofDestructionsPower"]}, "Cost": {"AP": 150}}, {"说明": "觉醒技能-狂化", "Id": "AwakeSkill_<PERSON><PERSON>rker", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/AwakeSkill/AS_GodofWarsMight"]}, "Cost": {"AP": 100}}, {"说明": "觉醒技能-唤龙", "Id": "AwakeSkill_DragonSummon", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/AwakeSkill/AS_EternalDragonflame"]}, "Cost": {"AP": 1000}}, {"说明": "取消觉醒技能", "Id": "AwakeSkill_Cancel_1", "Cmds": ["AwakeSkill"], "Tags": [{"Tag": "SwordShield_InitAttack", "From": 0}, {"Tag": "SwordShield_SkillAttack", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "AwakenSkill", "From": 0}], "BeCancelledTags": {}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/AwakeSkill/AS_Action_CancelEnd"]}, "Cost": {}}]}]}