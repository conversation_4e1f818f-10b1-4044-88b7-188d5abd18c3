{"Class": [{"Id": "Warrior", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["BladeDancer", "<PERSON><PERSON><PERSON>", "Swordsman"], "Buffs": ["Warrior_Passive", "Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "GSword_Move", "UnArmed": "GSword_Unarmed_Move"}, "Flying": {"Armed": "GSword_Move", "UnArmed": "GSword_Unarmed_Move"}, "Falling": {"Armed": "GSword_Fall", "UnArmed": "GSword_Unarmed_Fall"}, "Attached": {"Armed": "GSword_Ride", "UnArmed": "GSword_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "GSword_Hurt", "UnArmed": "GSword_Hurt"}, "Blow": {"Armed": "GSword_Blow", "UnArmed": "GSword_Blow"}, "Frozen": {"Armed": "GSword_Frozen", "UnArmed": "GSword_Frozen"}, "Bounced": {"Armed": "Warrior_Bounced", "UnArmed": "Warrior_Bounced"}, "Dead": {"Armed": "GSword_Dead", "UnArmed": "GSword_Dead"}, "Landing": {"Armed": "GSword_JustFall", "UnArmed": "GS<PERSON>_Unarmed_JustFall"}, "SecondWind": {"Armed": "GSword_SecWind", "UnArmed": "GSword_SecWind"}, "GetUp": {"Armed": "GSword_RevivedOnSecWind", "UnArmed": "GSword_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "BigSword", "DefaultWeapons": ["Iron_GreatSword"], "ActionOnChangeTo": "ChangeToGreatSworder", "ClassBuff": [{"Id": "Test_DoT", "Stack": 1, "Time": 10, "Infinity": true}], "Actions": [{"Line": "_______________________________大剑徒手基础动作________________________________"}, {"说明": "大剑徒手走路站立", "Id": "GSword_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive"], "1": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive"]}, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "大剑徒手起跳", "Id": "GSword_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "GSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["GSword_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "大剑徒手翻滚", "Id": "GS<PERSON>_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GS<PERSON>_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["GSword_Unarmed_Move", "GSword_Unarmed_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Swordsman_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"说明": "大剑徒手下落", "Id": "GSword_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "GSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/UnarmedFall"]}, "Priority": 1}, {"说明": "大剑徒手下落着地", "Id": "GS<PERSON>_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "GSword_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "大剑收刀", "Id": "SheathGreatSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "GSword_SheathWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/SheathWeapon"]}}, {"说明": "大剑拔刀", "Id": "DrawGreatSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "GSword_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/DrawWeapon"]}}, {"Line": "_______________________________大剑(LevelSquencer)动作________________________________"}, {"说明": "大剑_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________大剑(持武器)基础动作________________________________"}, {"Id": "GSword_Move", "Cmds": ["Move"], "Tags": [{"Tag": "GSword_Move", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"], "1": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/Move_AimState"]}}, {"Id": "GSword_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["GSword_Dodge"], "2": ["GSword_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Hurt_Air"]}}, {"Id": "GSword_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_QS_B"], "1": ["GSword_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/Blow_Front"]}}, {"Id": "GSword_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "GSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "GSword_Jump", "From": 0}, {"Tag": "GSword_Dodge", "From": 0}, {"Tag": "GSword_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "GSword_Fall", "Cmds": [], "Tags": [{"Tag": "GSword_Jump", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack"], "1": ["GSword_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/Fall"]}, "Priority": 1}, {"Id": "GSword_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/AttachOnTarget"]}}, {"Id": "GSword_JustFall", "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump", "GSword_Dodge"]}, "CanUseOnFalling": true, "Priority": 1, "Anim": {"Period": true, "StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Jump/JustFallMoving"]}, "CanStopSprint": false}, {"Id": "GSword_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 800}, "InitAction": true}, {"Id": "GSword_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 1200}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "GSword_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "GSword_QS_B", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge", "GSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 1600}}, {"说明": "受身动作前翻", "Id": "GSword_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "GSword_QS_F", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack", "GSword_Dodge", "GSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 1600}}, {"说明": "倒地动作", "Id": "GSword_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "GSword_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________大剑受击(特殊)动作________________________________"}, {"Id": "GSword_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________大剑基础(附加)动作________________________________"}, {"说明": "切换到大剑，朴实无华的大剑", "Id": "ChangeToGreatSworder", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/ChangeToWarrior1"]}}, {"说明": "弹刀动作", "Id": "Warrior_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_Unarmed_Jump", "GS<PERSON>_Unarmed_Dodge", "GSword_Aim", "GSword_DrawWeapon", "Unarm_UseItem", "GSword_DrawAttack", "Interactive"]}, "Priority": 30, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Bounced"]}}, {"说明": "瞄准动作", "Id": "GSword_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "GSword_Aim", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge", "GSword_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Warrior_Aim"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/PickUp"]}}, {"Line": "_______________________________大剑命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "GSword_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "GSword_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/OrderBuddy/OrderBuddyMoveToTarget"]}}, {"Line": "_______________________________大剑战斗动作_______________________________"}, {"Line": "_______________________________大剑_普攻_地面Action1________________________________"}, {"Id": "Warrior_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_LAttack2", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack"], "3": ["GSword_BranchAttack2"], "4": ["GSword_SpcSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_Slash0"]}, "InitAction": true}, {"Id": "Warrior_LAttack02", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack3", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_Slash1_Extended"]}, "InitAction": true}, {"Id": "Warrior_LAttack03", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_SkillAttack", "GSword_InitAttack"], "1": ["GSword_Dodge_Step"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_Slash2Extended"]}, "InitAction": true}, {"说明": "大剑分支普通攻击2", "Id": "Warrior_LAttackB2", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_BranchAttack3"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_SlashB1"]}, "InitAction": true}, {"说明": "大剑分支普通攻击3", "Id": "Warrior_LAttackB3", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_BranchAttack4"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_SlashB2"]}, "InitAction": true}, {"说明": "大剑分支普通攻击4", "Id": "Warrior_LAttackB4", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_InitAttack"], "1": ["GSword_Dodge_Step"], "2": ["GSword_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_SlashB3"]}, "InitAction": true}, {"Line": "_______________________________大剑_普攻_空中Action1________________________________"}, {"Id": "Warrior_AirLAttack0", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack1"], "1": ["GSword_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_Slash0"]}, "InitAction": true}, {"Id": "Warrior_AirLAttack1", "Cmds": ["Action1"], "Tags": [{"Tag": "AirLAttack1", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirSkillAttack"], "1": ["GSword_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能A(蓄力)_地面Action2________________________________"}, {"Id": "Warrior_ChargeAttack", "Cmds": ["Action2"], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_PowerSlash"]}, "Cost": {"MP": 200}}, {"Id": "Warrior_ChargeJumpAttack", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_PowerSlash_Jump"]}}, {"Line": "_______________________________大剑_技能A(蓄力)_空中Action2________________________________"}, {"Id": "Warrior_AirChargeAttack", "Cmds": ["Action2"], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge_Step", "GSword_InitAttack", "GSword_SkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_PowerSlash1"]}, "InitAction": true, "Cost": {"MP": 200}}, {"Line": "_______________________________大剑_技能B(位移)_地面Action3________________________________"}, {"说明": "提剑前戳突刺，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "<PERSON>_<PERSON><PERSON>ash<PERSON><PERSON><PERSON>", "Cmds": ["Action3"], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_StingDashAttack"]}, "Cost": {"MP": 2000}}, {"说明": "连招后续:命中时的升飞斩", "Id": "Warrior_StingUpperSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_AirInitAttack", "GSword_AirSkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_StingUpperCut"]}}, {"说明": "连招后续:冲刺斩命中时的连续斩", "Id": "Warrior_StingComboSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_StingComboSlash"]}}, {"说明": "向前冲刺横扫，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "Warrior_DashSlashAttack1", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "Warrior_DashSlashAttack2", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_DashSlash0"]}}, {"说明": "向前冲刺横扫第二下", "Id": "Warrior_DashSlashAttack2", "Cmds": ["Action3"], "Tags": [{"Tag": "Warrior_DashSlashAttack2", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_DashSlash1"]}}, {"Line": "_______________________________大剑_技能B(位移)_空中Action3________________________________"}, {"Id": "Warrior_AirDoubleStrike0", "Cmds": ["Action3"], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Warrior_AirDoubleStrike1", "GSword_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_Strike0"]}, "InitAction": true, "Cost": {"MP": 500}}, {"Id": "Warrior_AirDoubleStrike1", "Cmds": ["Action3"], "Tags": [{"Tag": "Warrior_AirDoubleStrike1", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_Strike1"]}, "InitAction": true, "Cost": {"MP": 500}}, {"Line": "_______________________________大剑_技能C(特殊)_地面Action4________________________________"}, {"说明": "防御动作，成功后进入反击动作", "Id": "<PERSON><PERSON><PERSON>", "Cmds": ["Action4"], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}, {"Tag": "GSword_SpcSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_Guard"]}, "Cost": {"MP": 500}}, {"说明": "防御成功了就自动变成这个了(仅用于justblock)", "Id": "Warrior_CounterDashAttack_JustBlock", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_CounterDashAttack_JustBlock"]}}, {"说明": "防御成功了就自动变成这个了", "Id": "Warrior_CounterDashAttack", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_CounterDashAttack"]}}, {"说明": "防御动作B，成功后进入反击动作", "Id": "Warrior_ParryB", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}, {"Tag": "GSword_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_Guard_B"]}}, {"说明": "防御B成功了就自动变成这个动作了", "Id": "Warrior_CounterStep_Back", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_CounterStep_Back"]}}, {"Line": "_______________________________大剑_技能C(特殊)_地面Action4________________________________"}, {"说明": "猎魔突袭斩", "Id": "Warrior_AirRush_DemonSlayer", "Cmds": ["Action4"], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "DemonSlayer", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_Rush"]}, "InitAction": true, "Cost": {"MP": 3000}}, {"说明": "下落斩", "Id": "Warrior_Air_FallSlash", "Cmds": [], "Tags": [{"Tag": "GSword_AirInitAttack", "From": 0}, {"Tag": "GSword_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_SkillAttack"], "1": ["GSword_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/A_FallSlash"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Line": "_______________________________大剑(狂战士型态)________________________________"}, {"Line": "_______________________________大剑(狂战士型态)基础动作________________________________"}, {"Id": "GSword_Berserk_Move", "Cmds": ["Move"], "Tags": [{"Tag": "GSword_Move_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_Jump_Bsk", "GSword_Dodge_Bsk", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"], "1": ["GSword_InitAttack_Bsk", "GSword_Jump_Bsk", "GSword_Dodge_Bsk", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/Berserk_Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Warrior_<Type>/Move_AimState"]}}, {"Line": "_______________________________大剑(狂战士型态)基础动作________________________________"}, {"Id": "GSword_Berserk_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_Move", "GSword_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/Dodge_F_Berserk"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"Id": "GS<PERSON>_<PERSON><PERSON><PERSON>_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "GSword_Dodge_Step_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack", "GSword_Jump", "GSword_Dodge", "GSword_Aim", "GSword_SheathWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Dodge/Step_F_Berserk"]}, "Cost": {"SP": 1400}, "InitAction": true}, {"Line": "_______________________________大剑(狂战士型态)战斗动作________________________________"}, {"Line": "_______________________________大剑_普攻(狂战士型态)_地面Action1________________________________"}, {"Id": "<PERSON>_<PERSON><PERSON>rk_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_LAttack2", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"], "3": ["GSword_BranchAttack2_Bsk"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_Slash0"]}, "InitAction": true}, {"Id": "Warrior_<PERSON><PERSON>rk_LAttack02", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_LAttack2_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_LAttack3_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_Slash1"]}, "InitAction": true}, {"Id": "<PERSON>_<PERSON><PERSON><PERSON>_LAttack03", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_LAttack3_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Berserk_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["-"], "3": ["-"], "4": ["GSword_SpcSkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_Slash2"]}, "InitAction": true}, {"说明": "大剑分支普通攻击2", "Id": "Warrior_Be<PERSON>rk_LAttackB2", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack2_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_BranchAttack3_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_WolfJumpSlash"]}, "InitAction": true}, {"说明": "大剑分支普通攻击3", "Id": "Warrior_<PERSON><PERSON>rk_LAttackB3", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack3_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_BranchAttack4_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_Dash2CombpSlash"]}, "InitAction": true}, {"说明": "大剑分支普通攻击4", "Id": "Warrior_<PERSON><PERSON>rk_LAttackB4", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_BranchAttack4_Bsk", "From": 0.0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_SlashB3"]}, "InitAction": true}, {"Line": "_______________________________大剑_普攻(狂战士型态)_空中Action1________________________________"}, {"Id": "Warrior_Berserk_AirLAttack0", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack1_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"CheckOnTick": false, "StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_Slash0"]}, "InitAction": true}, {"Id": "Warrior_Berserk_AirLAttack1", "Cmds": ["Action1"], "Tags": [{"Tag": "AirLAttack1_Bsk", "From": 0}], "BeCancelledTags": {"0": ["AirLAttack2_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_Slash1"]}, "InitAction": true}, {"Id": "Warrior_Berserk_AirLAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "AirLAttack2_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_SkillAttack_Bsk", "GSword_InitAttack_Bsk"], "3": ["GSword_AirSkillAttack_Bsk"]}, "CanUseOnFalling": true, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_Slash2"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能A(狂战士型态、飞升)_地面Action2________________________________"}, {"Id": "<PERSON>_<PERSON><PERSON>rk_SkillA", "Cmds": ["Action2"], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"], "2": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_RiseSlash2"]}}, {"Line": "_______________________________大剑_技能A(狂战士型态、下落)_空中Action2________________________________"}, {"Id": "Warrior_Berserk_AirSkillA", "Cmds": ["Action2"], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_DownSlash"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能B(狂战士型态、位移)_地面Action3________________________________"}, {"说明": "提剑前戳突刺，冲出去的那段，如果命中合理，会自动跳转到下面的连招后续", "Id": "<PERSON>_<PERSON><PERSON><PERSON>_SkillB", "Cmds": ["Action3"], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_DashUpSlash_Slow"]}}, {"Line": "_______________________________大剑_技能B(狂战士型态、位移)_空中Action3________________________________"}, {"Id": "Warrior_Berserk_AirSkillB", "Cmds": ["Action3"], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_CircleDownSlash"]}, "InitAction": true}, {"Line": "_______________________________大剑_技能C(狂战士型态、开启/关闭)_地面Action4________________________________"}, {"说明": "进入狂战士型态", "Id": "Warrior_Berserk_BerserkState", "Cmds": ["Action4"], "Tags": [{"Tag": "GSword_InitAttack_Bsk", "From": 0}, {"Tag": "GSword_SkillAttack_Bsk", "From": 0}, {"Tag": "GSword_SpcSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_InitAttack_Bsk", "GSword_SkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/G_Berserk_State"]}, "Cost": {"MP": 2500}}, {"Line": "_______________________________大剑_技能C(狂战士型态、开启)_空中Action4________________________________"}, {"说明": "进入狂战士型态", "Id": "Warrior_Berserk_AirBerserkState", "Cmds": ["Action4"], "Tags": [{"Tag": "GSword_AirInitAttack_Bsk", "From": 0}, {"Tag": "GSword_AirSkillAttack_Bsk", "From": 0}, {"Tag": "GSword_SpcSkillAttack_Bsk", "From": 0}], "BeCancelledTags": {"0": ["GSword_AirInitAttack_Bsk", "GSword_AirSkillAttack_Bsk"], "1": ["GSword_Dodge_Step_Bsk"]}, "Priority": 5, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/Berserk/A_Berserk_State"]}, "Cost": {"MP": 2500}}, {"Line": "_______________________________大剑_普攻_攀爬Action1________________________________"}, {"Id": "Warrior_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "GSword_ClimbInitAttack1", "From": 0}], "BeCancelledTags": {"0": ["GSword_ClimbInitAttack1"], "1": ["Climb_Dodge"], "2": ["GSword_ClimbSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Mount/ClimbAttack"]}, "InitAction": true}, {"Line": "_______________________________大剑_普攻_疾跑Action1________________________________"}, {"Id": "Warrior_SprintAttack", "Cmds": ["Action1"], "Tags": [{"Tag": "SprintAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge"]}, "Balance": 2, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_SprintAttack"]}, "InitAction": true}, {"Id": "Warrior_SprintCharge<PERSON><PERSON>ck", "Cmds": ["Action1"], "Tags": [{"Tag": "SprintAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge"]}, "Balance": 6, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/GreatSword/G_SprintPowerSlash"]}, "InitAction": true}, {"Line": "_______________________________？________________________________"}, {"Id": "Wa_<PERSON>rab", "Cmds": [], "Tags": [{"Tag": "GSword_InitAttack", "From": 0}], "BeCancelledTags": {"0": ["GSword_Dodge"]}, "Balance": 2, "CanUseOnFalling": false, "Priority": 30, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/Attack/Grab"]}, "InitAction": true}]}], "Buff": [], "Aoe": []}