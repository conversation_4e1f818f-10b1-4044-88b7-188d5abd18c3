{"WidgetInfo": [{"Id": "Title", "ZOrder": "99", "Path": "Core/UI/WBP_Title"}, {"Id": "CreateCharacter", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_CreateCharacter"}, {"Id": "Dungeon", "ZOrder": "0", "Path": "Core/UI/WBP_Dungeon"}, {"Id": "GameMain", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_GameMain"}, {"Id": "<PERSON><PERSON>", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_GameMenu"}, {"Id": "MultiPlayer", "ZOrder": "0", "Path": "Core/UI/WBP_MultiPlayer"}, {"Id": "Score", "ZOrder": "99", "Path": "Core/UI/WBP_Score"}, {"Id": "SelectData", "ZOrder": "99", "Path": "Core/UI/WBP_SelectData"}, {"Id": "Shop", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_ShopItem"}, {"Id": "ShopPrice", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_ShopItemPrice"}, {"Id": "Loading", "ZOrder": "99", "Path": "Core/UI/WBP_Loading"}, {"Id": "PopText", "ZOrder": "0", "Path": "Core/UI/PopUpText/WBP_PopText"}, {"Id": "RouletteItem", "ZOrder": "10", "Path": "Core/UI/GameMain/WBP_RouletteItem"}, {"Id": "DialogUI", "ZOrder": "10", "Path": "Core/UI/GameMain/WBP_DialogUI"}, {"Id": "Aim", "ZOrder": "9", "Path": "Core/UI/WBP_CrossHair"}, {"Id": "BattleTest", "ZOrder": "999", "Path": "Core/UI/Debug/WBP_BattleTest"}, {"Id": "PreviewLevel", "ZOrder": "999", "Path": "Core/UI/Debug/WBP_PreviewLevel"}, {"Id": "Equipment", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_Equipment"}, {"Id": "ChangeSkill", "ZOrder": "99", "Path": "Core/UI/GameMain/ChangeSkill/WBP_ChangeSkill_New"}, {"Id": "ButtonIndication", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_ButtonIndication"}, {"Id": "ButtonUI", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_ButtonUi"}, {"Id": "ChangeClass", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_ChangeClass"}, {"Id": "ItemShortcutBar", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_ItemShortcutBar"}, {"Id": "BackToVillage", "ZOrder": "99", "Path": "Core/UI/WBP_BackToVillage"}, {"Id": "PowerMap", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_PowerMap"}, {"Id": "Defeated", "ZOrder": "99", "Path": "Core/UI/WBP_DefeatedUI"}, {"Id": "MediaPlayer", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_MediaPlayer"}, {"Id": "BossHP", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_BossHealth"}, {"Id": "MapName", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_MapName"}, {"Id": "SystemSettings", "ZOrder": "99", "Path": "Core/UI/SystemSettings/WBP_SystemSettings"}, {"Id": "TaskContent", "ZOrder": "0", "Path": "Core/UI/Task/WBP_TaskContent"}, {"Id": "GetEquippment", "ZOrder": "10", "Path": "Core/UI/GameMain/WBP_GetEquippment"}, {"Id": "ToBeContinue", "ZOrder": "99", "Path": "Core/UI/GameMain/WBP_ToBeContinue"}, {"Id": "GetBlackSmithWeapon", "ZOrder": "10", "Path": "Core/UI/Newbie/<PERSON><PERSON>ie_GetNewWeapon"}, {"Id": "Newbie_Main", "ZOrder": "11", "Path": "Core/UI/Newbie/Newbie_Main"}, {"Id": "NewbieRetrospect", "ZOrder": "99", "Path": "Core/UI/Newbie/WBP_NewbieRetrospect"}, {"Id": "<PERSON>bie_DrawWeapon", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_DrawWeapon"}, {"Id": "<PERSON><PERSON>_Attack", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_Attack"}, {"Id": "Newbie_DamageAvoidance", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_DamageAvoidance"}, {"Id": "Newbie_ChangeClass", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_ChangeClass"}, {"Id": "<PERSON><PERSON>_BreakDoodad", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_BreakDoodad"}, {"Id": "Newbie_GatherKeyItem", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_GatherKeyItem"}, {"Id": "Newbie_UseItem", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_UseItem"}, {"Id": "Newbie_ChangeEquipment", "ZOrder": "0", "Path": "Core/UI/Newbie/Newbie_ChangeEquipment"}, {"Id": "MosaicGroove", "ZOrder": "0", "Path": "Core/UI/GameMain/WBP_MosaicGroove"}, {"Id": "Subtitle", "ZOrder": "9", "Path": "Core/UI/Subtitle/WBP_Subtitle"}, {"Id": "SubtitleContent", "ZOrder": "0", "Path": "Core/UI/Subtitle/WBP_RogueSubtitleContent"}, {"Id": "BigMap", "ZOrder": "0", "Path": "Core/UI/Map/WBP_BigMap"}, {"Id": "BigMapIcon", "ZOrder": "0", "Path": "Core/UI/Map/WBP_MapItemPoint"}, {"Id": "CutsceneBlackEdge", "ZOrder": "99", "Path": "Core/UI/Subtitle/WBP_CutsceneblackEdge"}, {"Id": "BigMapShade", "ZOrder": "0", "Path": "Core/UI/Map/WBP_BigMapShade"}, {"Id": "GetPlotProps", "ZOrder": "9", "Path": "Core/UI/GameMain/WBP_GetPlotProps"}, {"Id": "TaskTips", "ZOrder": "10", "Path": "Core/UI/Task/WBP_TaskTips"}, {"Id": "TaskProgress", "ZOrder": "0", "Path": "Core/UI/Task/WBP_TaskProgress"}, {"Id": "UIBubblesContent", "ZOrder": "0", "Path": "Core/UI/UIBubbles/WBP_UIBubblesContent"}, {"--------------------------RougelikeUI": "--------------------------------RougelikeUI------------------------------------------------------"}, {"Id": "Rogue_Title", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/WBP_Rogue_Title"}, {"Id": "RogueSystemSettings", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/SystemSetting/WBP_RogueSystemSettings"}, {"Id": "AwakSkill_Main", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/AwakeSkill/WBP_AwakeSkill"}, {"Id": "AwakSkill_Options", "ZOrder": "1", "Path": "Core/UI/Roguelike/AwakSkill/WBP_AwakSkill_Options"}, {"Id": "RogueFighting_Main", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_RogueFighting_Main"}, {"Id": "RogueBossHPBar", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_HPBar_Mob"}, {"Id": "RogueChallengeTip", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/WBP_ChallengeTip"}, {"Id": "RogueDead", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/WBP_RogueDead"}, {"Id": "RogueTalent", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Talent/WBP_RogueTalent"}, {"Id": "RogueShop_Main", "ZOrder": "1", "Path": "Core/UI/Roguelike/00-Formal/RogueShop/WBP_RogueShop_Formal"}, {"Id": "RogueSetStakePawn", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/StakePawn/WBP_Rogue_SetStakePawn"}, {"Id": "RogueBattleUpgradePreview", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/ChangeSkill/WBP_Rogue_AbilityPreview"}, {"Id": "RogueUpgradeHealingPotion", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/UpgradeHealingPotion/WBP_RogueUpgradeHealingPotion_Main"}, {"Id": "RoguePrayerTargetSelection_Main", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_RoguePrayerTargetSelection_Main"}, {"Id": "RoguePrayerRelicSelection_Main", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_RoguePrayerRelicSelection_Main"}, {"Id": "RogueGamePaused", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RogueGamePaused/WBP_RogueGamePaused"}, {"Id": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Rewards/WBP_Rogue_RewardMain"}, {"Id": "ShiftDotMain", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Rogue_ShiftDot_Main"}, {"Id": "ShiftDot", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Rogue_ShiftDot"}, {"Id": "TargetSigns", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Sign/WBP_ScreenTargetSign"}, {"Id": "RogueSettlment", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Settlement/WBP_RogueSettlement"}, {"Id": "RogueLoading", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/Loading/WBP_RogueLoading"}, {"Id": "RogueDialogUI", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_Rogue_DialogUI"}, {"Id": "RogueManual", "ZOrder": "11", "Path": "Core/UI/Roguelike/00-Formal/RogueManual/WBP_RogueManual"}, {"Id": "RogueConfirmStart", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/DifficultySelection/WBP_ConfirmStart"}, {"Id": "RogueDifficultySelect", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/DifficultySelection/WBP_DifficultySelect"}, {"Id": "Sequence", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/WBP_Sequence"}, {"Id": "Prompt", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Prompt"}, {"Id": "RoguePlayerChaProp", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/CharacterPanel/WBP_RoguePlayerProp_Formal"}, {"Id": "RogueUnlockCareer", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/UnlockCareer/WBP_RogueUnlockCareer"}, {"Id": "RogueGiveCurrencyToNPC", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_RogueGiveCurrencyToNPC"}, {"Id": "RogueGetCurrency", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_GetCurrency"}, {"Id": "CmdPopUp", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_CmdPopUp"}, {"Id": "<PERSON>_<PERSON><PERSON>_Move", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieMoveTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_FirstBattle", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieFirstBattleTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_SecondBattle", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSecondBattleTip"}, {"Id": "Rogue_<PERSON>bie_Burning", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieBurningTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_FireBrust", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieFireBrustTip"}, {"Id": "Rogue_<PERSON>bie_ChangeClass", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieChangeClassTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_Talent", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieTalentTip"}, {"Id": "Rogue_<PERSON><PERSON>_AwakeSkill", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieAwakeSkillTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_ChangeSkill", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSkillTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_TrainField", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieTrainFieldTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_Rogue_Newbie_GetRelic"}, {"Id": "Rogue_<PERSON>bie_GetItem", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_Rogue_Newbie_GetAction"}, {"Id": "<PERSON>_<PERSON><PERSON>_Swordsman", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSwordsmanTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_BladeDancer", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieBladeDancerTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>man", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSpearmanTip"}, {"Id": "Rogue_BattleManual", "ZOrder": "11", "Path": "Core/UI/Roguelike/00-Formal/CharacterPanel/WBP_RogueBattleManual_Formal"}, {"Id": "Rogue_CurrencyExchange", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/CurrencyShop/WBP_CurrencyExchange"}, {"Id": "Rogue_ChangeWeapon", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/ChangeWeapon/WBP_ChangeWeapon"}, {"Id": "Rogue_ChangeSkin", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/ChangeSkin/WBP_ChangeSkin"}, {"Id": "Rogue_TrialPlayInstructions", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/UserAgreementsAndBasicSettings/WBP_TrialPlayInstructions_Main"}, {"Id": "WBP_ChangeLevelTransitionScreen_Black", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_ChangeLevelTransitionScreen_Black"}, {"Id": "RogueShowPlaceName", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/WBP_ShowPlaceName"}, {"Id": "Rogue_Subtitle", "ZOrder": "9", "Path": "Core/UI/Roguelike/00-Formal/Subtitle/WBP_RogueSubtitle"}, {"Id": "Rogue_SubtitleContent", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Subtitle/WBP_RogueSubtitleContent"}, {"Id": "Rogue_Opening", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Opening"}, {"Id": "Rogue_Ending", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Ending"}, {"Id": "Rogue_Credits", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Credits"}, {"Id": "RogueCareerRecord", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RogueManual/WBP_RogueCareerRecord"}, {"Id": "RoguePreaverRelic", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_Rogue_Re<PERSON>_PreaverRelic"}, {"--------------------------RougelikeUI_Svl": "--------------------------------RougelikeUI_Svl------------------------------------------------------"}, {"Id": "Rogue_Title_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike_Svl/00-Formal/WBP_RogueSvl_Title"}, {"Id": "RogueSystemSettings_Svl", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/SystemSetting/WBP_RogueSystemSettings"}, {"Id": "AwakSkill_Main_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/AwakeSkill/WBP_AwakeSkill"}, {"Id": "AwakSkill_Options_Svl", "ZOrder": "1", "Path": "Core/UI/Roguelike/AwakSkill/WBP_AwakSkill_Options"}, {"Id": "RogueFighting_Main_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_RogueFighting_Main"}, {"Id": "RogueFighting_MainOverlay_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike_Svl/00-Formal/WBP_RogueSvl_Overlay"}, {"Id": "RogueBossHPBar_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_HPBar_Mob"}, {"Id": "RogueChallengeTip_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/WBP_ChallengeTip"}, {"Id": "RogueDead_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike_Svl/00-Formal/WBP_RogueDead_Svl"}, {"Id": "RogueTalent_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Talent/WBP_RogueTalent"}, {"Id": "RogueShop_Main_Svl", "ZOrder": "1", "Path": "Core/UI/Roguelike/00-Formal/RogueShop/WBP_RogueShop_Formal"}, {"Id": "Survivor_LevelUp", "ZOrder": "1", "Path": "Core/UI/Roguelike_Svl/00-Formal/Rewards/WBP_SurvivorReward_Formal"}, {"Id": "RogueSetStakePawn_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/StakePawn/WBP_Rogue_SetStakePawn"}, {"Id": "RogueBattleUpgradePreview_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/ChangeSkill/WBP_Rogue_AbilityPreview"}, {"Id": "RogueUpgradeHealingPotion_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/UpgradeHealingPotion/WBP_RogueUpgradeHealingPotion_Main"}, {"Id": "RoguePrayerTargetSelection_Main_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_RoguePrayerTargetSelection_Main"}, {"Id": "RoguePrayerRelicSelection_Main_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_RoguePrayerRelicSelection_Main"}, {"Id": "RogueGamePaused_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RogueGamePaused/WBP_RogueGamePaused"}, {"Id": "Rogue_Re<PERSON><PERSON>ain_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Rewards/WBP_Rogue_RewardMain"}, {"Id": "ShiftDotMain_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Rogue_ShiftDot_Main"}, {"Id": "ShiftDot_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Rogue_ShiftDot"}, {"Id": "TargetSigns_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Sign/WBP_ScreenTargetSign"}, {"Id": "RogueSettlment_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike_Svl/00-Formal/Settlement/WBP_RogueSettlement"}, {"Id": "RogueLoading_Svl", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/Loading/WBP_RogueLoading"}, {"Id": "RogueDialogUI_Svl", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_Rogue_DialogUI"}, {"Id": "RogueManual_Svl", "ZOrder": "11", "Path": "Core/UI/Roguelike/00-Formal/RogueManual/WBP_RogueManual"}, {"Id": "RogueConfirmStart_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Common/WBP_ConfirmStart_Svl"}, {"Id": "RogueDifficultySelect_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/DifficultySelection/WBP_DifficultySelect"}, {"Id": "Sequence_Svl", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/WBP_Sequence"}, {"Id": "Prompt_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_Prompt"}, {"Id": "RoguePlayerChaProp_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/CharacterPanel/WBP_RoguePlayerProp_Formal"}, {"Id": "RogueUnlockCareer_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/UnlockCareer/WBP_RogueUnlockCareer"}, {"Id": "RogueGiveCurrencyToNPC_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_RogueGiveCurrencyToNPC"}, {"Id": "RogueGetCurrency_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Dialog/WBP_GetCurrency"}, {"Id": "CmdPopUp_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Fighting/WBP_CmdPopUp"}, {"Id": "Rogue_<PERSON><PERSON>_Move_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieMoveTip"}, {"Id": "Rogue_<PERSON><PERSON>_FirstBattle_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieFirstBattleTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_SecondBattle_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSecondBattleTip"}, {"Id": "Rogue_<PERSON><PERSON>_Burning_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieBurningTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_FireBrust_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieFireBrustTip"}, {"Id": "Rogue_<PERSON><PERSON>_ChangeClass_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieChangeClassTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_Talent_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieTalentTip"}, {"Id": "Rogue_<PERSON><PERSON>_AwakeSkill_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieAwakeSkillTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_ChangeSkill_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSkillTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_TrainField_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieTrainFieldTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_GetRelic_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_Rogue_Newbie_GetRelic"}, {"Id": "<PERSON>_<PERSON><PERSON>_Swordsman_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSwordsmanTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_BladeDancer_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieBladeDancerTip"}, {"Id": "<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/Newbie/WBP_NewbieSpearmanTip"}, {"Id": "Rogue_BattleManual_Svl", "ZOrder": "11", "Path": "Core/UI/Roguelike/00-Formal/CharacterPanel/WBP_RogueBattleManual_Formal"}, {"Id": "Rogue_CurrencyExchange_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/CurrencyShop/WBP_CurrencyExchange"}, {"Id": "Rogue_ChangeWeapon_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/ChangeWeapon/WBP_ChangeWeapon"}, {"Id": "Rogue_ChangeSkin_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/ChangeSkin/WBP_ChangeSkin"}, {"Id": "Rogue_TrialPlayInstructions_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/UserAgreementsAndBasicSettings/WBP_TrialPlayInstructions_Main"}, {"Id": "WBP_ChangeLevelTransitionScreen_Black_Svl", "ZOrder": "99", "Path": "Core/UI/Roguelike/00-Formal/Common/WBP_ChangeLevelTransitionScreen_Black"}, {"Id": "RogueShowPlaceName_Svl", "ZOrder": "10", "Path": "Core/UI/Roguelike/00-Formal/WBP_ShowPlaceName"}, {"Id": "Rogue_Subtitle_Svl", "ZOrder": "9", "Path": "Core/UI/Roguelike/00-Formal/Subtitle/WBP_RogueSubtitle"}, {"Id": "Rogue_SubtitleContent_Svl", "ZOrder": "0", "Path": "Core/UI/Roguelike/00-Formal/Subtitle/WBP_RogueSubtitleContent"}, {"Id": "Rogue_Opening_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Opening"}, {"Id": "Rogue_Ending_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Ending"}, {"Id": "Rogue_Credits_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/WBP_Credits"}, {"Id": "RogueCareerRecord_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RogueManual/WBP_RogueCareerRecord"}, {"Id": "RoguePreaverRelic_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike/00-Formal/RoguePray/WBP_Rogue_Re<PERSON>_PreaverRelic"}, {"Id": "LevelList_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Common/WBP_LevelSelect"}, {"Id": "Settlement_Svl_Demo_Origin", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Settlement/WBP_RogueSettlement_Svl_Demo_Origin"}, {"Id": "LevelGuide01C", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Guide/WBP_LevelTip"}, {"Id": "Rogue_Opening_Svl", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/WBP_Opening_Svl"}, {"Id": "MissionPreparation", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Common/WBP_MissionPreparation"}, {"Id": "BattleTip_KillingStorm", "ZOrder": "20", "Path": "Core/UI/Roguelike_Svl/00-Formal/Guide/WBP_BattleTip_KillingStorm"}]}