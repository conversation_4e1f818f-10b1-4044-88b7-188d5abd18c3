{"Buff": [{"分割": "-------------------------------------------道具相关Buff-----------------------------------------"}, {"说明": "减伤+霸体", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>er", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 100, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(<PERSON><PERSON><PERSON>_<PERSON>,Temp/Effect/ParagonZ/FX_Countess/Particles/Abilities/BlinkStrike/FX/P_Countess_TeleportArrive_R_Ash_Decal_Loop,Body,false,1)", "BuffUtils.AddSubBuffObj(Test_Endure,1,1,true)"], "OnBeHurt": ["BuffUtils.HurtDamageUp(-0.01)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Awake_Berserker)", "BuffUtils.RemoveSubBuffObjStack(Test_Endure,1)"]}, {"说明": "霸体", "Id": "Test_Endure", "Priority": 0, "MaxStack": 1}, {"说明": "减伤", "Id": "Rogue_ItemHurtDamageDown", "Tag": [], "Priority": 0, "MaxStack": 100, "OnBeHurt": ["BuffUtils.HurtDamageUp(-0.01)"]}, {"说明": "暴击率提升", "Id": "Rogue_ItemCritUp", "Tag": [], "Priority": 0, "MaxStack": 100000, "Property": [{"CriticalChance": 0.0001}, {}], "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(ItemCritUp,Temp/Effect/Project/AwakenSkill/P_HolyStateActive_Loop_Yellow,Body,false,1)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(ItemCritUp)"]}, {"说明": "加速", "Id": "Rogue_ItemSpeedUp", "Tag": [], "Priority": 0, "MaxStack": 5000, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}], "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Rogue_ItemSpeedUp,Temp/Effect/Project/SkillEffect/p_Ult_Windzone_Shield_LGreen,Body,false,1)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Rogue_ItemSpeedUp)"]}, {"说明": "造成伤害产生吸血", "Id": "Rogue_BloodlyThirsty", "Tag": ["Awake", "NotSave"], "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Awake_BloodlyThirsty,Temp/Effect/Project/AwakenSkill/P_HolyStateActive_Loop_Green,Body,false,1)"], "OnHit": ["AwakeBuff.BloodlyThirsty(0.15)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Awake_BloodlyThirsty)"]}, {"说明": "对命中的敌人造成冰冻 每层该buff造成 0.1s冰冻", "Id": "Rogue_FrozenHit", "Tag": ["Frozen"], "Priority": 0, "MaxStack": 100, "OnHit": ["RogueBuff.AddBuffToHitTargetStackToTime(Standard_PlayFrozen,1,0.1,true)"]}, {"说明": "反弹伤害", "Id": "Rogue_ReflectBody", "Tag": [], "Priority": 0, "MaxStack": 1000, "OnBeHurt": ["RogueBuff.CreateAoeOnEnemySocketByCoolDown(Rogue_Aoe_ReflectThunder,1,root,0,true)"]}, {"说明": "攻击时产生一个剑气", "Id": "RogueItem_Sword_Effect", "Tag": ["CreateBullet", "Rogue_Slash", "NotSave", "NotShow"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Bullet_SwordLight,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"], "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Sword_Effect,Temp/Effect/Project/AwakenSkill/P_HolyStateActive_Loop_Red,Body,false,1)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Sword_Effect)"]}, {"说明": "突刺攻击时刻产生额外距离Aoe", "Id": "RogueItem_Pierce_Effect", "Tag": ["Hit", "NotSave", "<PERSON><PERSON><PERSON>", "NotShow"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(<PERSON>_<PERSON><PERSON>_<PERSON>,1,<PERSON>_<PERSON>et)"]}, {"说明": "钝击攻击时刻产生额外Aoe", "Id": "RogueItem_Bludgeon_Effect", "Tag": ["Hit", "NotSave", "Rogue_Bludgeon", "NotShow"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Blu<PERSON>on,1,Root)"]}]}