{"Dialogs": [{"说明": "PostiveAdventurer初次对话", "Id": "Rogue_PostiveAdventurer_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Shop_Start"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_VeryFirstDialog1"}], "Selections": [{"说明": "Rogue_PostiveAdventurer故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RoguePostiveAdventurerStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_PostiveAdventurer初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog6"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog7"}, {"Type": "Speak", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog8"}, {"Type": "Speak", "Id": "Step8", "NextId": "Step9", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog9"}, {"Type": "Speak", "Id": "Step9", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialog10"}], "EndClipScript": ["DialogAction.SetRogueSwitch(PostiveAdventurer_First_StoryDialog,1)"]}, {"说明": "Rogue_PostiveAdventurer初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialogRepeat1"}], "Selections": []}], "EndDialogScript": ["DialogAction.SetRogueSwitch(PostiveAdventurer_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_PostiveAdventurer 默认开始对话", "Id": "Rogue_PostiveAdventurer_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step0_1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Hall_Start"}, {"Type": "Speak", "Id": "Step0_1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_FirstDialog1"}, {"Type": "DoAction", "Id": "Step1", "NextId": "Step1_1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Hall_Start"}, {"Type": "Speak", "Id": "Step1_1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_FirstDialog2"}], "Selections": [{"说明": "Rogue_PostiveAdventurer故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RoguePostiveAdventurerStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_PostiveAdventurer结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "Rogue_PostiveAdventurer初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_StoryFirstDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_PostiveAdventurer 第二次的对话", "Id": "StorySecondDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_InHall1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_InHall2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_InHall3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_InHall4"}], "EndClipScript": ["DialogAction.GiveRogueCurrency(Rogue_Soul,100)", "DialogAction.SetRogueSwitch(PostiveAdventurer_Second_StoryDialog,1)"]}, {"说明": "Rogue_PostiveAdventurer 第二次的重复对话", "Id": "StorySecondDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_InHallRepeat1"}], "Selections": []}, {"说明": "Rogue_PostiveAdventurer 二周目初次的对话", "Id": "WeeklyRoundDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_ObtainSecondGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(PostiveAdventurer_WeeklyRound_StoryDialog,1)"]}, {"说明": "Rogue_PostiveAdventurer二周目重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_PostiveAdventurer 最终BOSS后的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_DefeatDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_DefeatDeathLord2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(PostiveAdventurer_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_PostiveAdventurer 最终BOSS后重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "PostiveAdventurer", "Text": "OptimisticAdventurer_DefeatDeathLord1Repeat1"}], "Selections": []}], "EndDialogScript": []}]}