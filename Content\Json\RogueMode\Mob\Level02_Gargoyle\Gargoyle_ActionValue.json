{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "<String>BUFF的ID", "BuffStack": "<int>需要的BUFF层数", "Weight": "<float>在该BUFF大于等于指定层数后的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "OnGround": "<float>敌人在地面上时的权重加成", "InAir": "<float>敌人在空中时的权重加成", "InDodge": "<float>敌人正在翻滚时的权重加成", "InHurt": "<float>敌人正在受击时的权重加成", "MinActionCD": "<float>最小CD时间", "MaxActionCD": "<float>最大CD时间"}]}}, {"Id": "Rogue_Gargoyle", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 5}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 6}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 2, "MaxActionCD": 4}]}]}