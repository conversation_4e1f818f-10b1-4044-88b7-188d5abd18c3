{"Mob": [{"Id": "GoblinChief", "Tag": ["Goblin"], "AI": ["Goblin_ClearRoaredBuff", "Go<PERSON>_CelebrateVictory", "GoblinChief_Roar", "Goblin_DodgeBack", "Goblin_DodgeLeftOrRight", "Goblin_LeftJump", "Goblin_RightJump", "GoblinChief_NearAttack", "GoblinChief_MiddleAttack", "GoblinChief_FarAttack", "GoblinChief_Pace", "Goblin_Turn", "MoveToClosetViewedEnemy", "Goblin_MoveToNextPathNode"], "BpPath": "Core/Characters/Goblin/Goblin_Champion/Goblin_Champion_New", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "Goblin", "ModValue": -1}], "Flyable": false, "LootPackageId": "Goblin", "MountsTypeRotate": false, "Name": "哥布林酋长", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 500, "SightHalfAngleDregee": 90, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 100, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 1, "Attack": 5, "Balance": 10, "MoveSpeed": [325, 655, 980], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1, "Infinity": false}], "Equipments": [{"Id": "GoblinDoubleAxe", "Rate": 1}], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 0.5}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/Goblin/GoblinChampion/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/Hit_Light_Front", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/Hit_Light_Back", "ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/Hit_Light_Front", "ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/Hit_Light_Back"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp", "ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp", "ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp", "ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp", "ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp", "ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/HurtUp"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Hurt/Dead02"]}, "InitAction": true}, {"说明": "庆祝胜利的吼叫", "Id": "CelebrateVictory", "Cmds": ["CelebrateVictory"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Roar"]}, "InitAction": true}, {"说明": "发现敌人的吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Roar"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "AttackS1", "Cmds": ["AttackS1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Attack/WeaponAxeAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "AttackS2", "Cmds": ["AttackS2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Attack/WeaponAxeAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "AttackS1_2", "Cmds": ["AttackS1_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Attack/WeaponAxeAttack_S1_2"]}, "InitAction": true}, {"说明": "中距离攻击", "Id": "AttackM1", "Cmds": ["AttackM1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NoramlAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Attack/WeaponAxeAttack_M1"]}, "InitAction": true}, {"说明": "远距离攻击", "Id": "AttackL1", "Cmds": ["AttackL1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NoramlAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/Goblin/Goblin_Champion/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "向左跳", "Id": "LeftJump", "Cmds": ["LeftJump"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Dodge_MoveStep_Left"]}, "InitAction": true}, {"说明": "向右跳", "Id": "RightJump", "Cmds": ["RightJump"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Dodge_MoveStep_Right"]}, "InitAction": true}, {"说明": "左踱步", "Id": "LeftPace", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Walk_Left"]}, "InitAction": true}, {"说明": "右踱步", "Id": "RightPace", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Walk_Right"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Dodge_MoveStep_Back"]}, "InitAction": true}, {"说明": "向左侧滚闪避", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Dodge_MoveStep_Left"]}, "InitAction": true}, {"说明": "向右侧滚闪避", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Dodge_MoveStep_Right"]}, "InitAction": true}, {"说明": "向左180度转身", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Turn_L_180"]}}, {"说明": "向右180度转身", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Goblin/Goblin_Champion/Dodge/Turn_R_180"]}}]}]}