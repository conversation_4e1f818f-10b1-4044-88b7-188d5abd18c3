{"说明": "标准的，常用的一些Buff", "Buff": [{"说明": "角色攻击力下降，每层0.01%", "Id": "Standard_AttackReduce", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"Attack": -1}]}, {"说明": "角色造成伤害提高，每层0.01%", "Id": "Standard_DamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnHit": ["BuffUtils.DamageTimesUp(0.0001)"]}, {"说明": "角色造成伤害减少，每层0.01%", "Id": "Standard_DamageDown", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnHit": ["BuffUtils.DamageTimesDown(0.0001)"]}, {"说明": "角色受到的伤害提高，每层0.01%", "Id": "Standard_HurtUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnBeHurt": ["BuffUtils.HurtDamageUp(0.0001)"]}, {"说明": "角色受到的Break伤害提高，每层0.01%", "Id": "Standard_HurtBreakUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnBeHurt": ["BuffUtils.BreakDamageUp(0.0001)"]}, {"说明": "角色非地面造成伤害提高，每层0.01%", "Id": "Standard_SkyDamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnHit": ["BuffUtils.DamageTimesUpInSky(0.0001)"]}, {"说明": "角色移动速度提高，每层0.01%", "Id": "Standard_MoveSpeedUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 100000, "Property": [{}, {"MoveSpeed": [1, 1, 1]}]}, {"说明": "角色移动速度降低，每层0.01%", "Id": "Standard_MoveSpeedDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 100000, "Property": [{}, {"MoveSpeed": [-1, -1, -1]}]}, {"说明": "角色行动速度提高，每层0.01%", "Id": "Standard_ActionSpeedUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 100000, "Property": [{}, {"ActionSpeed": 1}]}, {"说明": "角色行动速度减少，每层0.01%", "Id": "Standard_ActionSpeedDown", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 10000, "Property": [{}, {"ActionSpeed": -1}]}, {"说明": "免疫一次伤害", "Id": "Standard_IgnoreDamageOnce", "Tag": ["Help", "<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 9999, "OnBeHurt": ["BuffUtils.IgnoreDamage()"]}, {"说明": "空中保护Buff,在收到攻击后增加层数，层数>=3时，免疫伤害", "Id": "Standard_AirHurtProtect", "Tag": ["Help", "<PERSON><PERSON><PERSON>", "Air"], "Priority": 0, "MaxStack": 9999, "OnBeHurt": ["BuffUtils.AddStackOnBeHurt()", "BuffUtils.IgnoreDamageWhenStackGreater(3)"]}, {"说明": "免疫致死伤害", "Id": "Standard_IgnoreDeathDamage", "Tag": ["Help", "<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.IgnoreDeathDamage()"]}, {"说明": "角色被冻结", "Id": "Standard_PlayFrozen", "Tag": ["Control"], "TickTime": 0.1, "Priority": 0, "MaxStack": 1, "ControlState": {"CanMove": "OutOfControl", "CanRotate": "OutOfControl"}, "OnOccur": ["BuffUtils.PlayFrozen()", "BuffUtils.PlayVFXOnCha_Occur(Frozen,ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Ultimate_Frozen_10Sec,Mesh,false,1)", "RogueBuff.BuffTimeCaluTargetResistance(ControlBuffResistance)"], "OnRemoved": ["BuffUtils.StopFrozen()", "BuffUtils.StopVFXOnCha_Remove(Frozen)"]}, {"说明": "角色被暂停", "Id": "Standard_Pause", "Tag": [], "TickTime": 0, "Priority": 0, "MaxStack": 1, "ControlState": {"CanMove": "OutOfControl", "CanRotate": "OutOfControl"}, "OnOccur": ["BuffUtils.PlayFrozen()"], "OnRemoved": ["BuffUtils.StopFrozen()"]}, {"说明": "角色自己 TNND 燃起来了", "Id": "Standard_Burning", "Tag": ["<PERSON>rm", "Dot", "Fire", "ShowUI"], "TickTime": 1, "Priority": 0, "MaxStack": 1000, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Burn,ArtResource/ProjectRogue/VFX/Project/ps_Fire_01_01_BoneSocket,Mesh,false,1)"], "OnTick": ["BuffUtils.DamageOverTime(Fire,3,0.5)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Burn)"]}, {"说明": "AI 确定“最近的”攻击目标 ", "Id": "AIFindClosestTarget", "Tag": ["UpdateEnemy"], "Priority": 0, "TickTime": 0.1, "MaxStack": 1, "OnTick": ["BuffUtils.AIFindClosestTarget(300)"]}, {"说明": "免疫熔岩伤害 ", "Id": "ImmuneLava", "Tag": [""], "Priority": 0, "TickTime": 0, "MaxStack": 1}, {"说明": "抵抗控制Buff,1层=0.01%时间抵抗", "Id": "ControlBuffResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000}, {"说明": "物理伤害抵抗 一层= 0.01%", "Id": "PhysicalDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Physical,0.0001)"]}, {"说明": "火焰伤害抵抗 一层= 0.01%", "Id": "FireDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Fire,0.0001)"]}, {"说明": "风伤害抵抗 一层= 0.01%", "Id": "WindDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Wind,0.0001)"]}, {"说明": "雷伤害抵抗 一层= 0.01%", "Id": "ThunderDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Thunder,0.0001)"]}, {"说明": "冰伤害抵抗 一层= 0.01%", "Id": "IceDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Ice,0.0001)"]}, {"说明": "光伤害抵抗 一层= 0.01%", "Id": "LightDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Light,0.0001)"]}, {"说明": "暗伤害抵抗 一层= 0.01%", "Id": "DarknessDamageResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000, "OnBeHurt": ["RogueBuff.DamageResistanceByDamageType(Darkness,0.0001)"]}, {"说明": "减速抵抗 一层= 0.01%", "Id": "SpeedDownResistance", "Tag": ["Resistance"], "Priority": 0, "TickTime": 0, "MaxStack": 10000}, {"说明": "被召唤出来的Mob身上挂的标记BUFF，在移除时会减去召唤者身上一层SummonNum的Buff", "Id": "SummonMobTag", "Tag": ["<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnBeKilled": ["BuffUtils.AddBuffToBuffCaster(SummonNum,-1,0,false,true)"]}, {"说明": "能召唤别的小怪的Mob身上挂的，用来记录还有几个召唤兽存活", "Id": "SummonNum", "Tag": ["<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 1000}]}