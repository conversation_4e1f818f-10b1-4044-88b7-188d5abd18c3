{"Mob": [{"Id": "OrcNormal", "Tag": ["Orc"], "AI": ["StopAI", "NormalOrcBasicBattle", "OrcTurnToStimulate"], "BpPath": "Core/Characters/Orc/OrcNormal/Orc_Normal", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -10}], "Flyable": false, "LootPackageId": "OrcNormal", "MountsTypeRotate": false, "Name": "普通兽人", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 500, "SightHalfAngleDregee": 135, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 1, "Attack": 5, "Balance": 10, "MoveSpeed": [265, 525, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [{"Id": "OrcWeapeon", "Rate": 1}, {"Id": "Orc_Bread", "Rate": 0.5}, {"Id": "Orc_BaseBody", "Rate": 1}, {"Id": "Orc_BodyDecoration01", "Rate": 0.5}, {"Id": "Orc_BodyDecoration02", "Rate": 0.5}, {"Id": "Orc_BaseArm", "Rate": 1}, {"Id": "Orc_ArmDecoration01", "Rate": 0.7}, {"Id": "Orc_ArmDecoration02", "Rate": 0.7}, {"Id": "<PERSON><PERSON>_<PERSON><PERSON>", "Rate": 1}], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 0.5}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/Orc/Orc_Fighter/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Back", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Front", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Back", "ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Action/Roar"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar_C2", "Cmds": ["Roar_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Action/Roar_c2"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar_C3", "Cmds": ["Roar_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Action/Roar_c3"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_S3"]}, "InitAction": true}, {"说明": "近距离攻击4", "Id": "NormalAttack_S4", "Cmds": ["NormalAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_S4"]}, "InitAction": true}, {"说明": "近距离攻击4", "Id": "RageAttack_S4", "Cmds": ["RageAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/RageAttack_S4"]}, "InitAction": true}, {"说明": "近距离攻击5", "Id": "NormalAttack_S5", "Cmds": ["NormalAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_S5_Combo"]}, "InitAction": true}, {"说明": "中距离攻击1", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_M1"]}, "InitAction": true}, {"说明": "中距离攻击2", "Id": "NormalAttack_M2", "Cmds": ["NormalAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_M2"]}, "InitAction": true}, {"说明": "中距离攻击3", "Id": "NormalAttack_M3", "Cmds": ["NormalAttack_M3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_M3"]}, "InitAction": true}, {"说明": "中距离攻击4", "Id": "NormalAttack_M4", "Cmds": ["NormalAttack_M4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_M4"]}, "InitAction": true}, {"说明": "中距离攻击4", "Id": "RageAttack_M4", "Cmds": ["RageAttack_M4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/RageAttack_M4"]}, "InitAction": true}, {"说明": "中距离攻击5", "Id": "NormalAttack_M5", "Cmds": ["NormalAttack_M5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_M5"]}, "InitAction": true}, {"说明": "远距离攻击1", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "远距离攻击1", "Id": "RageAttack_L1", "Cmds": ["RageAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/RageAttack_L1"]}, "InitAction": true}, {"说明": "远距离攻击2", "Id": "NormalAttack_L2", "Cmds": ["NormalAttack_L2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Attack/NormalAttack_L2"]}, "InitAction": true}, {"说明": "前跳", "Id": "Dodge_Front", "Cmds": ["Dodge_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Dodge/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "左跳", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Dodge/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "右跳", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Dodge/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "后垫步跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Dodge/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_JumpBack", "Cmds": ["Dodge_JumpBack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Dodge/Dodge_JumpStep_Back"]}, "InitAction": true}, {"说明": "向前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Front"]}, "InitAction": true}, {"说明": "向后走", "Id": "Walk_Back", "Cmds": ["Walk_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Back"]}, "InitAction": true}, {"说明": "向左走", "Id": "Walk_Left", "Cmds": ["Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Left"]}, "InitAction": true}, {"说明": "向右走", "Id": "Walk_Right", "Cmds": ["Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Right"]}, "InitAction": true}]}]}