{"AOE": [{"Id": "Test_Fireball_Explosion", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/FireballScroll_Explosion", "OnCreate": ["AOEScript.PlayVFXOnCreate(Temp/Effect/ParagonZ/FX_IggyScorch/Particles/IggyScorch/Abilities/Turret/FX/P_IggyScorch_Turret_Explosion)"], "OnTick": [], "OnRemoved": ["AOEScript.DealDamageOnRemoved(200)"], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Cheese", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/Cheese", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.SetCanGetCheeseBuffOnCharacterEnter()"], "OnCharacterLeave": ["AOEScript.RemoveCanGetCheeseBuffOnCharacterLeave()"], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MineCarExpode", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/MineCar_Explosion", "OnCreate": ["AOEScript.PlayVFXOnCreate(Temp/Effect/ParagonZ/FX_IggyScorch/Particles/IggyScorch/Abilities/Turret/FX/P_IggyScorch_Turret_Explosion)"], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByItem(50)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DestroySceneItem()"], "OnActorLeave": []}, {"Id": "FireAoeByItem", "Tag": ["Fire", "<PERSON><PERSON>", "FlameScroll"], "TickTime": 0, "BpPath": "Core/Item/AOE/FireAoeByItem", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByItem(50)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(50)"], "OnActorLeave": []}, {"Id": "ExplosionAoeByCannon_Center", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/ExplosionAoeByCannon_Center", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByTeam(0,1000)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(1000)"], "OnActorLeave": []}, {"Id": "ExplosionAoeByCannon_Range", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/ExplosionAoeByCannon_Range", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByTeam(0,50)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(50)"], "OnActorLeave": []}, {"Id": "A<PERSON>_Awake_EarthQuakeWave", "Tag": ["Awake", "EarthQuake"], "TickTime": 0, "BpPath": "Core/Item/AOE/AOE_EarthQuakeWave", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByTeam(0,100)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(100)"], "OnActorLeave": []}, {"Id": "MouseTrap", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/MouseTrap", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "KillRatAOE", "Tag": ["SceneItem"], "TickTime": 0, "BpPath": "Core/Item/AOE/MineCar_Explosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.DestroyRat()"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "FireAoeInScene", "Tag": ["SceneItem", "Fire"], "TickTime": 0.3, "BpPath": "Core/Item/AOE/FireAOE", "OnCreate": [], "OnTick": ["AOEScript.DealDamageOnTick(10)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}]}