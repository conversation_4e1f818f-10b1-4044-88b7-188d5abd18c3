{"Class": [{"说明": "游侠", "Id": "<PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["_"], "Buffs": ["Warrior_Passive", "Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "ShortBow_Move", "UnArmed": "ShortBow_Unarmed_Move"}, "Flying": {"Armed": "ShortBow_Move", "UnArmed": "ShortBow_Unarmed_Move"}, "Falling": {"Armed": "ShortBow_Fall", "UnArmed": "ShortBow_Unarmed_Fall"}, "Attached": {"Armed": "ShortBow_Ride", "UnArmed": "ShortBow_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "ShortBow_Hurt", "UnArmed": "ShortBow_Hurt"}, "Blow": {"Armed": "ShortBow_Blow", "UnArmed": "ShortBow_Blow"}, "Frozen": {"Armed": "ShortBow_Frozen", "UnArmed": "ShortBow_Frozen"}, "Bounced": {"Armed": "ShortBow_Bounced", "UnArmed": "ShortBow_Bounced"}, "Dead": {"Armed": "ShortBow_Dead", "UnArmed": "ShortBow_Dead"}, "Landing": {"Armed": "ShortBow_JustFall", "UnArmed": "ShortBow_<PERSON>rmed_JustFall"}, "SecondWind": {"Armed": "ShortBow_SecWind", "UnArmed": "ShortBow_SecWind"}, "GetUp": {"Armed": "ShortBow_RevivedOnSecWind", "UnArmed": "ShortBow_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "ShortBow", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "ChangeToRanger", "ClassBuff": [], "Actions": [{"Line": "_______________________________短弓徒手基础动作________________________________"}, {"说明": "短弓徒手走路站立", "Id": "ShortBow_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Unarmed_Jump", "ShortBow_<PERSON>rmed_Dodge", "ShortBow_Aim", "ShortBow_DrawWeapon", "Unarm_UseItem", "ShortBow_DrawAttack", "Interactive"], "1": ["ShortBow_Unarmed_Jump", "ShortBow_<PERSON>rmed_Dodge", "ShortBow_Aim", "ShortBow_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/UnarmedMove", "ArtResource/Anim/BlendSpace/Player/Fighter/Normal/BS_Throw_Aim"]}}, {"说明": "短弓徒手起跳", "Id": "ShortBow_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "ShortBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "短弓徒手翻滚", "Id": "ShortBow_<PERSON>rmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_<PERSON>rmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"说明": "短弓徒手下落", "Id": "ShortBow_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "ShortBow_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/UnarmedFall"]}, "Priority": 1}, {"说明": "短弓徒手下落着地", "Id": "ShortBow_<PERSON>rmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "ShortBow_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "短弓收刀", "Id": "SheathShortBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/SheathWeapon"]}}, {"说明": "短弓拔刀", "Id": "DrawShortBow", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "ShortBow_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/DrawWeapon"]}}, {"Line": "_______________________________短弓(LevelSquencer)动作________________________________"}, {"说明": "短弓趴地上_LevelSquencer用", "Id": "FallDown_Loop", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_Loop"]}}, {"说明": "短弓趴地上起来_LevelSquencer用", "Id": "FallDown_End", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_End"]}}, {"说明": "短弓_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________短弓(持武器)基础动作________________________________"}, {"Id": "ShortBow_Move", "Cmds": ["ShortBow_Move"], "Tags": [{"Tag": "ShortBow_Move", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/Move", "ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/Defense", "ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/Defense"]}}, {"Id": "ShortBow_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_HurtCounter"], "1": ["ShortBow_Dodge"], "2": ["ShortBow_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Hurt_Air"]}}, {"Id": "ShortBow_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_QS_B"], "1": ["ShortBow_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/Blow_Front"]}}, {"Id": "ShortBow_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "ShortBow_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "ShortBow_Jump", "From": 0}, {"Tag": "ShortBow_Dodge", "From": 0}, {"Tag": "ShortBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "ShortBow_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": ["ShortBow_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ranger_<Type>/Fall"]}, "Priority": 1}, {"Id": "ShortBow_JustFall", "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump", "ShortBow_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/JustFall", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "ShortBow_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_Dodge", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/Dodge_F"]}, "Cost": {"SP": 1000}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON><PERSON>_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Move", "ShortBow_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 800}, "InitAction": true}, {"Id": "ShortBow_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "ShortBow_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/Step_F"]}, "Cost": {"SP": 1200}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "ShortBow_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "ShortBow_QS_B", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Dodge", "ShortBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/QuickStanding_B"]}, "Cost": {"SP": 1600}}, {"说明": "受身动作前翻", "Id": "ShortBow_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "ShortBow_QS_F", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Dodge", "ShortBow_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Dodge/QuickStanding_F"]}, "Cost": {"SP": 1600}}, {"说明": "倒地动作", "Id": "ShortBow_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "ShortBow_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________短弓特殊动作________________________________"}, {"Id": "ShortBow_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "ShortBow_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Jump/AttachOnTarget"]}}, {"Id": "ShortBow_Ride", "Cmds": [], "Tags": [{"Tag": "ShortBow_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["ShortBow_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________短弓受击(特殊)动作________________________________"}, {"Id": "ShortBow_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________短弓基础(附加)动作________________________________"}, {"说明": "剑与盾盾", "Id": "ChangeToRanger", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/ChangeToRanger1"]}}, {"说明": "短弓弹刀动作", "Id": "ShortBow_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_Jump", "ShortBow_Dodge", "ShortBow_Aim", "ShortBow_Shea<PERSON><PERSON>ea<PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/Bounced"]}}, {"说明": "瞄准动作", "Id": "ShortBow_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}, {"Tag": "ShortBow_Defense", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge"], "2": ["ShortBow_Defense_Attack1", "ShortBow_Defense_Attack2", "ShortBow_Defense_Attack3"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_<Type>/PickUp"]}}, {"Line": "_______________________________短弓命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "ShortBow_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "ShortBow_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/OrderBuddy/OrderBuddyMoveToTarget"]}}, {"Line": "_______________________________短弓战斗动作_______________________________"}, {"Line": "_______________________________短弓_普攻_地面Action1_______________________________"}, {"Id": "Ranger_LAttack01", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_LAttack1", "From": 0}, {"Tag": "ShortBow_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_LAttack2"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"], "3": ["ShortBow_BranchAttack2"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Slash0"]}, "InitAction": true}, {"Id": "Ranger_LAttack02", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_LAttack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Slash1"]}, "InitAction": true}, {"Id": "Ranger_LAttack03", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_LAttack1"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Slash2"]}, "InitAction": true}, {"说明": "短弓分支普通攻击2", "Id": "Ranger_BranchAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_SlashB1"]}, "InitAction": true}, {"说明": "短弓分支普通攻击3", "Id": "Ranger_BranchAttack3", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack4"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_SlashB2"]}, "InitAction": true}, {"说明": "短弓分支普通攻击4", "Id": "Ranger_BranchAttack4", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_BranchAttack5"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_SlashB3"]}, "InitAction": true}, {"说明": "短弓分支普通攻击5", "Id": "Ranger_BranchAttack5", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_BranchAttack5", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_SlashB4"]}, "InitAction": true}, {"Line": "_______________________________短弓_普攻_空中Action1_______________________________"}, {"Id": "Ranger_AirAttack1", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirAttack1", "From": 0}, {"Tag": "ShortBow_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirAttack2", "ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/A_Slash0"]}, "InitAction": true}, {"Id": "Ranger_AirAttack2", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirAttack1"], "1": ["_"], "2": ["ShortBow_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________短弓_技能A_地面Action2_______________________________"}, {"Id": "Ranger_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_RiseSlash"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Id": "Ranger_RiseSlash", "Cmds": ["Action2"], "Tags": [{"Tag": "ShortBow_HurtCounter", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_RiseSlash_Counter"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"Id": "Ranger_RiseComboSlash", "Cmds": ["_"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_AirSkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Rise2Slash"]}, "InitAction": true, "Cost": {"MP": 1200}}, {"Line": "_______________________________短弓_技能A_空中Action2_______________________________"}, {"Id": "Ranger_AirDownSlashAttack1", "Cmds": ["Action2"], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/A_DashSlash_FallDown"]}, "InitAction": true, "Cost": {"MP": 300}}, {"Line": "_______________________________短弓_技能B_地面Action3_______________________________"}, {"Id": "Ranger_DashSlash", "Cmds": ["Action3"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_DashAttack2", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_DashSlash"]}, "Cost": {"MP": 1200}}, {"Id": "Ranger_DashSlash2", "Cmds": ["Action3"], "Tags": [{"Tag": "ShortBow_DashAttack2", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_DashSlash1"]}, "Cost": {"MP": 1200}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "Ranger_DashSpike_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_DashSpike_Combo"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON>_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_AirInitAttack", "ShortBow_AirSkillAttack"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_RiseSlash"]}}, {"Line": "_______________________________短弓_技能B_空中Action3_______________________________"}, {"Id": "Ranger_AirDashSting", "Cmds": ["Action3"], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/A_DashSting_Forward"]}, "InitAction": true, "Cost": {"MP": 1500}}, {"Line": "_______________________________短弓_技能C(防御)_地面Action4_______________________________"}, {"Id": "Ranger_Defense", "Cmds": ["Action4"], "Tags": [{"Tag": "ShortBow_InitAttack", "From": 0}, {"Tag": "ShortBow_SkillAttack", "From": 0}, {"Tag": "ShortBow_Defense", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_Defense_Attack1", "ShortBow_Defense_Attack2", "ShortBow_Defense_Attack3"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense"]}}, {"Line": "_______________________________短弓_防御_动作_______________________________"}, {"说明": "防御成功", "Id": "Ranger_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "MontageAnimPickFunc.GetActionByPriorityDistance(4)", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_Success_Big", "ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_Success_Small"]}, "Cost": {"SP": 1800, "ReplaceAction": "Ranger_Defense_Success_Broken"}}, {"说明": "防御成功_但体力不足_破防", "Id": "Ranger_Defense_Success_Broken", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_Dodge"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_Success_Broken"]}}, {"说明": "完美防御_成功(justblock)", "Id": "Ranger_Defense_JustBlock", "Cmds": [], "Tags": ["ShortBow_JustBlock"], "BeCancelledTags": {"0": ["ShortBow_JustBlock_Success_Attack"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_JustBlock_Success"]}}, {"说明": "*完美防御_成功的后续动作1", "Id": "Ranger_Defense_JustBlock_Success_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Defense_Attack1x", "ShortBow_Defense_Attack2x", "ShortBow_Defense_Attack3x"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_JustBlock_ShieldStrike"]}}, {"说明": "*完美防御_成功的后续动作2", "Id": "Ranger_Defense_JustBlock_Success_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "ShortBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Defense_Attack1x", "ShortBow_Defense_Attack2x", "ShortBow_Defense_Attack3x"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_JustBlock_ShieldUpperStrike"]}}, {"说明": "*完美防御_成功的后续动作3", "Id": "Ranger_Defense_JustBlock_Success_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "ShortBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Defense_Attack1x", "ShortBow_Defense_Attack2x", "ShortBow_Defense_Attack3x"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_JustBlock_DashSlash"]}}, {"说明": "*完美防御_成功的后续动作4", "Id": "Ranger_Defense_JustBlock_Success_Attack4", "Cmds": ["Action4"], "Tags": [{"Tag": "ShortBow_JustBlock_Success_Attack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack", "ShortBow_Defense_Attack1x", "ShortBow_Defense_Attack2x", "ShortBow_Defense_Attack3x"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_JustBlock_GroundSmash"]}}, {"说明": "防御时Action1的动作", "Id": "Ranger_Defense_Attack1", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_Defense_Attack1", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_Defense_Attack1_2", "ShortBow_Defense_Attack2", "ShortBow_Defense_Attack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_InitAttack", "ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_ShieldSweapSmash0"]}, "InitAction": true}, {"说明": "防御时Action1的连招动作", "Id": "Ranger_Defense_Attack1_2", "Cmds": ["Action1"], "Tags": [{"Tag": "ShortBow_Defense_Attack1_2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_Defense", "Ranger_Defense_Attack1", "ShortBow_Defense_Attack2", "ShortBow_Defense_Attack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_InitAttack", "ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_ShieldSweapSmash1"]}, "InitAction": true}, {"说明": "防御时Action2的动作", "Id": "Ranger_Defense_Attack2", "Cmds": ["Action2"], "Tags": [{"Tag": "ShortBow_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_Defense", "ShortBow_Defense_Attack1", "ShortBow_Defense_Attack3"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_InitAttack", "ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_ShieldUpperSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action3的动作", "Id": "Ranger_Defense_Attack3", "Cmds": ["Action3"], "Tags": [{"Tag": "ShortBow_Defense_Attack3", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_Defense", "ShortBow_Defense_Attack1", "ShortBow_Defense_Attack2"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_InitAttack", "ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_ShieldChargeSmash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时Action2的动作", "Id": "Ranger_Defense_Attack2B", "Cmds": [], "Tags": [{"Tag": "ShortBow_Defense_Attack2", "From": 0.0}], "BeCancelledTags": {"0": ["ShortBow_Defense"], "1": ["ShortBow_Dodge_Step"], "2": ["ShortBow_InitAttack", "ShortBow_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_CounterSlash"]}, "InitAction": true, "Cost": {"MP": 1000}}, {"说明": "防御时，Acion2的完美防御成功了就自动变成这个了(仅用于justblock)", "Id": "Ranger_Defense_CounterSlash_JustBlock", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/G_Defense_CounterSlash_JustBlock3"]}}, {"Line": "_______________________________短弓_技能C_空中Action4_______________________________"}, {"Id": "Ranger_AirDownShieldSmash", "Cmds": ["Action4"], "Tags": [{"Tag": "ShortBow_AirInitAttack", "From": 0}, {"Tag": "ShortBow_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["ShortBow_InitAttack", "ShortBow_SkillAttack"], "1": ["ShortBow_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Ranger_<Type>/Attack/ShortBow/A_ShieldSmash_FallDown"]}, "InitAction": true, "Cost": {"MP": 1500}}]}], "Buff": [], "Aoe": []}