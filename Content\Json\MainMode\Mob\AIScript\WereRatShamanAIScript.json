{"AIScript": [{"说明": "视野内没有敌人的情况下+不在族人边上,待机clip", "Id": "CheckShamanNotNearTribe", "Condition": ["MobAIScript.CheckNotStimulateByView()", "WereRatShamamAIScript.CheckNotNearAlly(1000)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(LookAround,Sniff)"]}, {"说明": "视野内没有敌人的情况下+不在族人边上+有目标族人，向族人移动clip", "Id": "CheckShamanWithAim", "Condition": ["MobAIScript.CheckNotStimulateByView()", "WereRatShamamAIScript.CheckNotNearAlly(1000)", "WereRatShamamAIScript.CheckHasViewedAlly()"], "OnReady": [], "Action": ["WereRatShamamAIScript.MoveToClosetViewedAlly()"]}, {"说明": "视野内没有敌人的情况下+在族人边上+目标族人采矿buff层数＜=【2】，播放对话动画", "Id": "CheckShamanNearTribeWithWorkBuff", "Condition": ["MobAIScript.CheckNotStimulateByView()", "WereRatShamamAIScript.CheckNearAllyHasBuff(300,<PERSON><PERSON><PERSON>,0,2)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(Talk)"]}, {"说明": "视野内没有敌人的情况下+在族人边上+目标族人采矿buff层数>【2】，播放戳醒动画", "Id": "CheckShamanNearTribeWithLazyBuff", "Condition": ["MobAIScript.CheckNotStimulateByView()", "WereRatShamamAIScript.CheckNearAllyHasBuff(300,<PERSON><PERSON><PERSON>,2,10)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(StampAlly)"]}, {"说明": "视野内有敌人，播放摇铃进战动画", "Id": "CheckShamanInBattle", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(3000)", "MobAIScript.CheckNotHasBuff(WereRat_HasRung)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(0,3000,BellRinging)"]}, {"说明": "敌人在半径为10米的圆内，播放摇铃进战动画", "Id": "CheckShamanInCircle", "Condition": ["MobAIScript.CheckHasEnemyInRange(1000)", "MobAIScript.CheckNotHasBuff(WereRat_HasRung)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetEnemyInRangeDoAction(0,1000,BellRinging)"]}, {"说明": "敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0+施放法术动画，给半径【30】米内的鼠人加上【狂热】buff，表现为鼠人身上有红光特效，眼睛变红", "Id": "CheckShamanExcited", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,3000)", "MobAIScript.CheckFightingWillLevelGreater(0)", "MobAIScript.CheckNotHasBuff(WereRat_Berserk_CD)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(CrazyBuff)"]}, {"说明": "敌人在视野内 + 敌人在【30】米范围内 + FightingWill_Level>0+战场上鼠人存活率＜【30%】,施放复活动画，读条【10】秒，结束后复在自己身后【3】米【60】度召唤出一左一右两只突击兵鼠人", "Id": "CheckShamanCalling", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,3000)", "MobAIScript.CheckFightingWillLevelGreater(0)", "WereRatShamamAIScript.CheckWereRatSurvivalRate(0.3)", "MobAIScript.CheckNotHasBuff(WereRat_ReBirth_CD)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(ReBirth)"]}, {"说明": "敌人在视野内 + 敌人在【30】米范围内，转向敌人，发射火球", "Id": "CheckShamanThrowFireBall", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(500,3000)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInRangeDoAction(500,3000,ThrowFireBall)"]}, {"说明": "敌人在视野范围内+距离敌人【10】米目以外，【30】米以内，向敌人移动", "Id": "CheckShamanMove", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1000,3000)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatAIScript.RunToViewedClosetEnemy(1000,3000,1500,Crawling_Short,Crawling_Long)"]}, {"说明": "", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,5000)"], "OnReady": [], "Action": ["WereRatShamamAIScript.BasicBattle(0,300,300,2000,1500,5000)"]}, {"说明": "趴下四肢着地向敌人跑过来", "Id": "WereRatShaman_Crawling", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(4000,8000)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatAIScript.RunToViewedClosetEnemy(4000,8000,6000,Crawling_Short,Crawling_Long)"]}]}