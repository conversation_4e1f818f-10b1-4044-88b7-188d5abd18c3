{"Bullet": [{"Id": "Rogue_3_IgniBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_3_IgniBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_17_IgniBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_17_IgniBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 99, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1.2,0,<PERSON><PERSON><PERSON><PERSON><PERSON>,Fire)", "BulletScript.AddBuffOnHit(Rogue_Burning,1,8)"], "OnRemoved": []}, {"Id": "Rogue_Bullet_Ilm4", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Ilm4", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_Bullet_Ilm17", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Ilm17", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 99, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1.2,0,<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON>)", "BulletScript.AddBuffOnHit(Rogue_Ice,1,8)"], "OnRemoved": []}, {"Id": "Rogue_Bullet_Poltick2", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Poltick2", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 5, "HitDelay": 0.0, "Life": 5, "LifeSpan": 10, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_Bullet_Poltick4", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Poltick2", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 5, "HitDelay": 0.0, "Life": 5, "LifeSpan": 10, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_Bullet_Zantia6", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_Bullet_Zantia6", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 99, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(3,0,<PERSON><PERSON><PERSON><PERSON>,<PERSON>)"], "OnRemoved": []}, {"Id": "Rogue_3_AzemBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_3_AzemBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_4_AzemBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_4_AzemBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(4,0,<PERSON><PERSON><PERSON><PERSON>,Light)"], "OnRemoved": []}, {"Id": "Rogue_6_AzemBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_6_AzemBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(3,0,<PERSON><PERSON><PERSON><PERSON>,Light)"], "OnRemoved": []}, {"Id": "Rogue_11_AzemBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_11_AzemBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1,0,<PERSON><PERSON><PERSON><PERSON>,Light)"], "OnRemoved": []}, {"Id": "Rogue_17_AzemBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_17_AzemBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 99, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1.2,0,<PERSON><PERSON><PERSON><PERSON>,<PERSON>)", "BulletScript.AddBuffOnHit(Rogue_Light,1,8)"], "OnRemoved": []}, {"Id": "Rogue_4_EminendanisBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_4_EminendanisBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Rogue_17_EminendanisBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_17_EminendanisBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 99, "HitDelay": 0.5, "Life": 99, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(0.2,0,<PERSON><PERSON><PERSON><PERSON><PERSON>,Darkness)", "BulletScript.AddBuffOnHit(Rogue_Corruption,1,8)"], "OnRemoved": []}, {"Id": "Rogue_18_EminendanisBullet", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Rogue_18_EminendanisBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1,0,<PERSON><PERSON><PERSON><PERSON>,Darkness)", "BulletScript.AddBuffOnHit(Rogue_Corruption,1,8)"], "OnRemoved": []}]}