{"Dialogs": [{"________________________________________11.29__": "__新对话______________________________________________________"}, {"________________________________________支线任务__": "__铁匠的烦恼a______________________________________________________"}, {"说明": "铁匠的烦恼A对话", "Id": "Blacksmith_SideQuestA", "FirstClip": "DialogA0", "NpcId": [], "Clips": [{"Id": "DialogA0", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "Dwarf_TurnLeft", "ActionId_TurnRight": "Dwarf_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speecha1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speecha1"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Speecha2", "NextEventFunc": "DialogAction.DirectGoTo(DialogA1)"}]}, {"Id": "DialogA1", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speecha3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speecha3"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Speecha4", "NextEventFunc": "DialogAction.DirectGoTo(DialogA2)"}]}, {"Id": "DialogA2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speecha5"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speecha5"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Speecha6", "NextEventFunc": "DialogAction.DirectGoTo(DialogA3)"}]}, {"Id": "DialogA3", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speecha7"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speecha7"}], "Selections": [{"Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Speecha8", "NextEventFunc": "DialogAction.DirectGoTo()", "Actions": ["DialogAction.SetRoleSwitchTo(RodianBlackSmithMission01,1)", "DesignerScript/TriggerScript_Country.ShowTaskTips( ,HelpBlacksmith)"]}]}], "EndDialogScript": []}, {"________________________________________支线任务__": "__铁匠的烦恼d______________________________________________________"}, {"说明": "铁匠的烦恼D1对话", "Id": "Blacksmith_SideQuestD", "FirstClip": "DialogD0", "NpcId": [], "Clips": [{"Id": "DialogD0", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "Dwarf_TurnLeft", "ActionId_TurnRight": "Dwarf_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speechd1"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speechd1"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_ReturnTool", "NextEventFunc": "DialogAction.DirectGoTo(DialogD1)", "Actions": ["DialogAction.GivePlayerPackage(Warrior02_Glovexxxx)", "DialogAction.SetRoleSwitchTo(RodianBlackSmithMission01,3)", "DialogAction.GiveBackBlacksmithTool()", "DesignerScript/TriggerScript_Country.ShowTaskTips(HelpBlacksmith)"]}]}, {"说明": "为了匹配铁匠铺动画，FocusTarget时间强行拉长对上动画时间", "Id": "DialogD1", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 2.5}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speechd2"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speechd2"}], "Selections": [{"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond1", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond2", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond3", "NextEventFunc": "DialogAction.DirectGoTo(DialogD2)"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond4", "NextEventFunc": "DialogAction.DirectGoTo()"}]}, {"说明": "正常的FocusTarget时间", "Id": "DialogD2", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speechd2"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speechd2"}], "Selections": [{"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond1", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond2", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond3", "NextEventFunc": "DialogAction.DirectGoTo(DialogD2)"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond4", "NextEventFunc": "DialogAction.DirectGoTo()"}]}], "EndDialogScript": []}, {"说明": "铁匠的烦恼D2对话", "Id": "Blacksmith_SideQuestD2", "FirstClip": "DialogD", "NpcId": [], "Clips": [{"Id": "DialogD", "FirstEvent": "TurnToPlayer", "Dialogs": [{"Type": "TurnToPlayer", "Id": "TurnToPlayer", "NextId": "Step0", "TargetType": "Target", "ActionId_TurnLeft": "Dwarf_TurnLeft", "ActionId_TurnRight": "Dwarf_TurnRight", "WaitSec": 1}, {"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "PlayAudio", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "AudioId": "MineTeamBlacksmith_Speechd2"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speechd2"}], "Selections": [{"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond1", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond2", "NextEventFunc": "DialogAction.DirectGoTo()"}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selectiond4", "NextEventFunc": "DialogAction.DirectGoTo()"}]}]}, {"________________________________________11.29__": "__旧对话______________________________________________________"}, {"说明": "MineTeamBlacksmith_WithoutTool", "Id": "MineTeamBlacksmith_WithoutTool", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech0"}, {"Type": "DoAction", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech1"}, {"Type": "DoAction", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "<PERSON><PERSON>"}, {"Type": "Speak", "Id": "Step6", "NextId": "Step7", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech2"}, {"Type": "FocusTarget", "Id": "Step7", "NextId": "Step8", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}], "Selections": [{"Conditions": [], "EnableChecks": ["DialogCondition.RoleSwitchRange(MineTeamBlacksmith_Tool,1)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection0", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": ["DialogAction.ChangeTargetDialog(MineTeamBlacksmith_Normal)", "DialogAction.ShowToast(MineTeamBlacksmith_Hint0)", "DialogAction.SetRoleSwitchTo(MineTeamBlacksmith_Tool,2)", "DialogAction.GiveBackBlacksmithTool()"]}, {"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection1", "NextEventFunc": ""}]}, {"Id": "MineTeamBlacksmith_FindBackTool", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Dwarf_Happy"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech3"}], "Selections": []}], "EndDialogScript": ["DialogAction.GivePlayerPackage()"]}, {"说明": "MineTeamBlacksmith_Normal", "Id": "MineTeamBlacksmith_Normal", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Dwarf_Hit1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech4"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "Actions": ["DialogAction.RandomChangeTargetDialog(MineTeamBlacksmith_Normal,MineTeamBlacksmith_Normal1,MineTeamBlacksmith_Normal2)"]}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection3", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection4", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}]}]}, {"说明": "MineTeamBlacksmith_Normal1", "Id": "MineTeamBlacksmith_Normal1", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Dwarf_Hit1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech5"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "Actions": ["DialogAction.RandomChangeTargetDialog(MineTeamBlacksmith_Normal,MineTeamBlacksmith_Normal1,MineTeamBlacksmith_Normal2)"]}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection3", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection4", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}]}]}, {"说明": "MineTeamBlacksmith_Normal2", "Id": "MineTeamBlacksmith_Normal2", "FirstClip": "Intro", "NpcId": [], "Clips": [{"Id": "Intro", "FirstEvent": "Step0", "Dialogs": [{"Type": "DoAction", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "ChaSlot": 0, "TodoActionId": "Dwarf_Hit1"}, {"Type": "FocusTarget", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "ChaSlot": 0, "CameraSlot": 0, "InSec": 0.3}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "MineTeamBlacksmith_Name", "Text": "MineTeamBlacksmith_Speech6"}], "Selections": [{"Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Text_Continue", "Actions": ["DialogAction.RandomChangeTargetDialog(MineTeamBlacksmith_Normal,MineTeamBlacksmith_Normal1,MineTeamBlacksmith_Normal2)"]}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection3", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}, {"Conditions": [], "说明": "此功能本版本不做", "EnableChecks": ["DialogCondition.Never()"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "MineTeamBlacksmith_Selection4", "NextEventFunc": "DialogAction.DirectGoTo(MineTeamBlacksmith_FindBackTool)", "Actions": []}]}]}]}