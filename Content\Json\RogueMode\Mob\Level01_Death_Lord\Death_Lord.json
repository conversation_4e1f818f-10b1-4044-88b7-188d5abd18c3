{"Mob": [{"Id": "Rogue_Death_Lord", "MobRank": "Boss", "Tag": [""], "AI": ["StopAIBuff", "Death_Lord_ChangeStage", "RogueMob_BasicBattle"], "BpPath": "Core/Characters/Rogue_Mob/Death_Lord/Death_Lord", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Death_Lord", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 5000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 6000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 20, "PAtk": 19, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0.5, "Infinity": false}, {"Id": "Rogue_Boss_FirstStage", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "Rogue_Boss_CheckSecondStage", "Stack": 50, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 10000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 10000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Standard_Move", "UnArmed": "Standard_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Death_Lord/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Hit/Break_Down"]}, "InitAction": true}, {"说明": "冰剑连斩", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "冷冽突袭", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "跳跃冰晶弹", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "荆棘冰牢", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_M1"]}, "InitAction": true}, {"说明": "冰晶连射", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/NormalAttack_L1"]}, "InitAction": true}, {"说明": "破霜之剑", "Id": "RageState", "Cmds": ["RageState"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageState"]}, "InitAction": true}, {"说明": "寒冰结界", "Id": "RageAttack_X1", "Cmds": ["RageAttack_X1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_X1"]}, "InitAction": true}, {"说明": "冰剑连斩EX", "Id": "RageAttack_S1", "Cmds": ["RageAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S1"]}, "InitAction": true}, {"说明": "冷冽突袭EX", "Id": "RageAttack_S2", "Cmds": ["RageAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S2"]}, "InitAction": true}, {"说明": "冷冽突袭EX2", "Id": "RageAttack_S2_2", "Cmds": ["RageAttack_S2_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S2_2"]}, "InitAction": true}, {"说明": "永冻之枪", "Id": "RageAttack_S3", "Cmds": ["RageAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_S3"]}, "InitAction": true}, {"说明": "荆棘冰牢EX", "Id": "RageAttack_M1", "Cmds": ["RageAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M1"]}, "InitAction": true}, {"说明": "冷冽剑", "Id": "RageAttack_M2", "Cmds": ["RageAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_M2"]}, "InitAction": true}, {"说明": "冰晶连射EX", "Id": "RageAttack_L1", "Cmds": ["RageAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/RageAttack_L1"]}, "InitAction": true}, {"说明": "闪避-后", "Id": "Dodge_DashStep_Back", "Cmds": ["Dodge_DashStep_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Death_Lord/Battle/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "闪避-左", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "闪避-右", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 20, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "发呆", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/Stare01"]}, "InitAction": true}, {"说明": "向前走(发呆)", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/Walk_Front"]}, "InitAction": true}, {"说明": "左转90度", "Id": "TurnLeft_90", "Cmds": ["TurnLeft_90"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnLeft_90"]}, "InitAction": true}, {"说明": "左转180度", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnLeft_180"]}, "InitAction": true}, {"说明": "右转90度", "Id": "TurnRight_90", "Cmds": ["TurnRight_90"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnRight_90"]}, "InitAction": true}, {"说明": "右转180度", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Action/TurnRight_180"]}, "InitAction": true}, {"说明": "前冲", "Id": "Dodge_DashStep_Forward", "Cmds": ["Dodge_DashStep_Forward"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Death_Lord/Battle/Dodge_DashStep_Forward"]}, "InitAction": true}]}], "Buff": [{"Id": "Rogue_Death_Lord_Walk_Front", "Tag": ["Death_Lord"], "Priority": 0, "MaxStack": 1}, {"说明": "造成伤害时给对方一个冰霜Debuff", "Id": "Death_Lord_IceHit", "Tag": ["Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(<PERSON>_Death_Lord_Ice,1,5,true,false)"]}, {"说明": "死骸骑士攻击造成的寒霜:角色速度减少5%", "Id": "Rogue_Death_Lord_Ice", "Tag": ["<PERSON>_Harm", "IceCream", "Rogue_Element_Harm"], "IgnoreDifferentCaster": true, "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.AddSubBuffObj(Rogue_ActionSpeedDown,500,0,true)", "BuffUtils.AddSubBuffObj(Rogue_MoveSpeedDown,500,0,true)", "BuffUtils.PlayVFXOnCha_Occur(Ice,ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Freeze_Rooted_BoneSocket,Mesh,false,1)"], "OnRemoved": ["BuffUtils.RemoveSubBuffObjStack(Rogue_ActionSpeedDown,500,0,true)", "BuffUtils.RemoveSubBuffObjStack(Rogue_MoveSpeedDown,500,0,true)", "BuffUtils.StopVFXOnCha_Remove(Ice)"]}], "Bullet": [{"Id": "Death_Lord_<PERSON><PERSON>", "Tag": [], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceBall", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(0.8,DirectDamage,Ice)", "BulletScript.AddBuffOnHit(Standard_PlayFrozen,1,1)"], "OnRemoved": []}, {"Id": "Death_Lord_IceBall_EX", "Tag": [], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceBall_EX", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(0.3,DirectDamage,Ice)", "BulletScript.AddBuffOnHit(Standard_PlayFrozen,1,1)"], "OnRemoved": []}], "AOE": [{"Id": "Death_Lord_RageAttack_S1_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_RageAttack_S1_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(0.8,0,Ice,DirectDamage,Keep,5)", "AOEScript.AddBuffToEnemyOnHit(Rogue_Death_Lord_Ice,1,5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Lord_RageA<PERSON>ck_S3_FrozenAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_RageAttack_S3_FrozenAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.AddBuffToEnemyOnHit(Standard_PlayFrozen,1,0.7)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Lord_RageAttack_S3_ExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_RageAttack_S3_ExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCasterProp(2.5,2.5,<PERSON>,ExtraDamage,<PERSON><PERSON>,310,0,100,5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Lord_RageAttack_X1_AOE", "Tag": [], "TickTime": 0.1, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_RageAttack_X1_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Death_Lord_IceExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCasterProp(0.3,1,<PERSON>,ExtraDamage,Hurt,0,0,0,5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}], "SceneItem": [{"Id": "Death_Lord_<PERSON><PERSON>", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IceWall", "LifeSpan": 12, "Part": [], "Tween": ""}, {"Id": "Death_Lord_<PERSON><PERSON>on", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IcePrison", "LifeSpan": 6, "Part": [], "Tween": ""}, {"Id": "Death_Lord_IcePrison_EX", "Tag": ["IceSceneItem"], "BpPath": "Core/Item/Monster/Death_Lord/Death_Lord_IcePrison_EX", "LifeSpan": 6, "Part": [], "Tween": ""}]}