{"说明": "这是所有Dungeon Talents相关的Buff，也就是地城等级提升后升级的东西", "Buff": [{"说明": ["玩家只要一进入副本，就会获得这个Buff，作为一个基础的效果。", "在每次击杀后都有一定概率掉落回血球，回血球掉率被Dungeon_HealthOrbRate所影响", "在每次击杀后都会增加提升血量和攻击力的Buff（Dungeon_Property）1层，Dungeon_VictoryPersuit会有概率增加更多层", "玩家每次被击杀，会损失3层Dungeon_Property"], "Id": "Dungeon_Base", "Tag": ["Dungeon", "Passive", "Mechanism"], "Priority": 0, "MaxStack": 1, "OnKill": ["DungeonBuff.DungeonChanceToCreateHealthOrb()", "BuffUtils.SelfAddBuff_InDamage(Dungeon_Property,1,1,true)"], "OnBeKilled": ["BuffUtils.AddBuff_InDamage(Dungeon_Property,-3,1,true)"], "OnChangeAction": []}, {"说明": "玩家连续进入下一层会获得的战斗勇气buff，比原计划增加了属性奖励", "Id": "Dunge<PERSON>_Courage", "Tag": ["Dungeon", "Passive", "Mechanism", "DungeonRewardUp"], "Priority": 0, "MaxStack": 999, "Property": [{}, {"说明": "每层增加5%血上限和2%攻击力", "Hp": 500, "Mp": 200}], "OnOccur": [], "OnTick": [], "OnRemoved": [], "OnHit": [], "OnBeHurt": [], "OnKill": [], "OnBeKilled": [], "OnChangeAction": []}, {"说明": "【天赋点】击杀怪物有掉落HealthOrb概率改变，默认为20%，每点增加5%", "Id": "Dungeon_HealthOrbRate", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5}, {"说明": "击杀怪物后增加血攻上限", "Id": "Dungeon_Property", "Tag": ["Dungeon", "Passive"], "Priority": 0, "MaxStack": 999, "Property": [{}, {"说明": "每层增加1%血上限和1%攻击力", "Hp": 100, "Mp": 100}]}, {"说明": "【天赋点】增加添加DungenProperty的概率，增加的概率为（1%，5%，10%，20%，50%）", "Id": "Dungeon_VictoryPersuit", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5}, {"说明": "【天赋点】仅当自己拾取HealthOrb的时候，溢出的部分的10%/20%/50%/100%/150%将转化为护盾", "Id": "Dungeon_BloodShield", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5}, {"说明": "HealthOrb的溢出的转化为护盾", "Id": "Dungeon_HealthOrbShield", "Tag": ["Dungeon", "Passive", "Shield"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.ShieldProtect()"]}, {"说明": "【天赋点】格挡反弹伤害", "Id": "Dungeon_ThornsReflect", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnBeHurt": ["DungeonBuff.ThronsReflect()"]}, {"说明": "【天赋点】闪避时受到伤害下降", "Id": "Dungeon_MasterDodge", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnBeHurt": ["DungeonBuff.MasterDodge()"]}, {"说明": "【天赋点】总伤害达到阈值产生一个血球", "Id": "Dungeon_BloodSeeker", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.BloodSeeker()"]}, {"说明": "【天赋点】身边一定范围内敌人攻击力下降（攻击力下降做标准buff）", "Id": "Dungeon_HorrorFace", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnOccur": ["BuffUtils.SelfAura(Dungeon_HorrorFace,9999)"]}, {"说明": "【天赋点】连续攻击伤害提高（伤害提高做标准buff，因为层数问题，需要在一个函数实现，所以OnHit不是直接加buff）", "Id": "Dungeon_OffensiveTide", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.OffensiveTide()"]}, {"说明": "【天赋点】抵御伤害时，提高攻击力", "Id": "Dungeon_TurnbackStrength", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.TurnbackStrength()"], "OnBeHurt": ["DungeonBuff.TurnbackStrength_Absorb()"]}, {"说明": "【天赋点】有几率不被弹刀和霸体一次伤害", "Id": "Dungeon_SteadyAsMountain", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.SteadyAsMountain_NotBounced()"], "OnBeHurt": ["DungeonBuff.SteadyAsMountain_NotBreakAction()"]}, {"说明": "【天赋点】免死金牌", "Id": "Dungeon_FirmSprite", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnBeKilled": ["DungeonBuff.FirmSprite()"]}, {"说明": "【天赋点】造成一定量的伤害之后，免疫一次伤害（免疫一次伤害做成通用buff）", "Id": "Dungeon_DeathProof", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.DeathProofHitRecord()"]}, {"说明": "【天赋点】命中多个敌人的时候造成额外伤害", "Id": "Dungeon_SweepAllBeforeMe", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.SweepAllBeforeMe()"]}, {"说明": "【天赋点】与敌人保持近距离就会不断提高伤害（提高伤害做成通用buff，检查范围内敌人Aoe_RangeChecker做通用aoe）", "Id": "<PERSON><PERSON><PERSON>_BraveFearless", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnOccur": ["BuffUtils.SelfAura(<PERSON><PERSON>_<PERSON>,9999)"]}, {"说明": "【天赋点】第n次命中敌人的时候，会无视2点平衡", "Id": "Dungeon_CarryAll", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.Carry<PERSON>ll()"]}, {"说明": "【天赋点】一个房间结束后，下次攻击n倍伤害，并提高n点破坏力", "Id": "Dungeon_SeparatesHeaven", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.SepratesHeaven_HitEffect()"], "OnKill": ["DungeonBuff.SepratesHeaven_FoeChecker()"], "OnActionHit": ["DungeonBuff.SepratesHeaven_ActionHitEffect()"]}, {"说明": "【天赋点】对于满血敌人造成n倍伤害，若命中头部造成更多伤害", "Id": "Dungeon_HeadOnBlow", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.HeadOnBlow()"]}, {"说明": "【天赋点】对于生命值低于30%的敌人造成伤害提高", "Id": "Dungeon_MaltreatInjury", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnHit": ["DungeonBuff.MaltreatInjury()"]}, {"说明": "【天赋点】击杀敌人移动速度提高（特定Buff1-5级）", "Id": "Dungeon_LikeTheWind", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnKill": ["DungeonBuff.LikeTheWind()"]}, {"说明": "【天赋点】副本中金币提高并且每n次击杀一定掉金币", "Id": "Dungeon_BattleFieldCleaner", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnKill": ["DungeonBuff.BattleFieldCleaner()"]}, {"说明": "【天赋点】获得作战英勇的量提升，并且获得作战英勇等级会回复生命值", "Id": "Dungeon_HighMorale", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnKill": ["DungeonBuff.HighMorale()"]}, {"说明": "【天赋点】自身满血受到的伤害减半，并且反噬攻击者", "Id": "Dungeon_ThornsGuardian", "Tag": ["Dungeon", "Passive", "DungeonTalent"], "Priority": 0, "MaxStack": 5, "OnBeHurt": ["DungeonBuff<PERSON>Guard<PERSON>()"]}]}