{"Mob说明": [{"Id": "NPC的Id", "Tag": ["NPC的Tag"], "AI": ["AI片段，这里的顺序将决定了执行的顺序的"], "BpPath": "Core/Characters/StandardHuman/Player_Human_Man", "SoundBase": "Audio/Sound_Cue/Character/Female/Chara_Voice/默认音效，比如挨打叫声所在文件夹，注意最后一个字符必须是/", "SoundSocket": "发出默认叫声的骨骼或者socket的名称", "Flyable": "<bool>是否是一个飞行怪，飞行怪在天上是fly不是fall", "LootPackageId": "掉落Id", "MoveProp": {"StopRangeWhenMoveFollow": 100, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "LoseSightRadius": 3000, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": "<int>生命值属性的潜能", "PAtk": "<int>物理攻击力的潜能", "MAtk": "<int>魔法攻击力的潜能", "PDef": "<int>物理防御力潜能", "MDef": "<int>魔法防御力去潜能", "ActionSpeed": "<int>行动速度潜能", "MoveSpeed": "<int>移动速度 [0]-lv1, [1]-lv2, [2]-lv3", "BeStrikeRate": "<float>被击飞的倍率，这个值越大，被击飞越远，浮空时间越长，1=正常倍率"}, "Buff": [{"Id": "要添加的buffId", "Stack": "<int>要添加的层数", "Time": "<float>持续多少秒", "Infinity": "<bool>是否无限时间，如果是，上面的Time就没意义了"}], "Equipments": [{"Id": "装备的id，对应Equipment表的id", "Rate": "<float>这个装备的出现率，就是每个这个model产生的Character有多少概率会装备这个装备"}, {"Id": "TestShield", "Rate": 0.7}], "HideSightParts": ["对应的尸块将在创建后被隐藏，用于一些部位被破坏后才显示"], "Part": [{"Meat": {"Physical": "<float>物理的肉质，1代表受到100%伤害"}, "Breakable": {"Physical": "<float>物理的破坏力，如果没有或者是0，代表斩击无法破坏这个部位，1代表受到伤害值100%的破坏值"}, "Part": "这个部位的类型：Head,Body,Arm,Leg,Wing,Tail,Horn,Back,Shield,Other，应该是个标准string而非枚举，后面会重构掉", "Durability": ["<int>每个等级的耐久度，被破坏一次之后会去掉数组的[0]，取最新的"], "CanBeDestroy": "<bool>部位是否最终可以被破坏，如果不可以，Durability的最后一个值会被保留反复当做当前耐久度用", "Type": "部位肉质类型：Meat,Metal,Skeleton,Void", "HideSightPart": ["在这个部位被彻底破坏之后，将会被隐藏的“尸块”的骨骼GetName()"], "ShowSightPart": ["在这个部位彻底被破坏之后，将会被显示的“尸块”的骨骼GetName()"]}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "武装时地面ActionId", "Unarmed": "未持械地面ActionId"}, "Flying": {"Armed": "武装时飞行ActionId", "Unarmed": "未持械飞行ActionId"}, "Falling": {"Armed": "武装时下落ActionId", "Unarmed": "未持械下落ActionId"}, "Attached": {"Armed": "武装时攀附ActionId", "Unarmed": "未持械攀附ActionId"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": ["参见ActionExample.json"], "Name": "罗子", "Portrait": "icon_smelling_luozi", "AiActionDetails": ["参见AiActionDetailsExample"]}]}