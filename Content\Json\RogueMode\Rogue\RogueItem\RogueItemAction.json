{"RogueItemAction": [{"Line": "---------- 喝药水的动作 ----------"}, {"说明": "喝药水的动作", "Id": "DrinkPotion_Rogue", "Cmds": [""], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/Drink_Rogue", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Drink_Rogue", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Drink_Rogue", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/Drink_Rogue", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/Drink_Rogue"]}}, {"说明": "旋风护体", "Id": "WindProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_WindProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_WindProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_WindProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_WindProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_WindProtect"]}}, {"说明": "炮车召唤", "Id": "CannonSummon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_SummonCannon", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_SummonCannon", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_SummonCannon", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_SummonCannon", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_SummonCannon"]}}, {"说明": "冰盾冲锋", "Id": "FrozenDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_FrozenDash", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_FrozenDash", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_FrozenDash", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_FrozenDash", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_FrozenDash"]}}, {"说明": "火焰冲锋", "Id": "FlameDash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_FlameDash", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_FlameDash", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_FlameDash", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_FlameDash", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_FlameDash"]}}, {"说明": "冰剑反击", "Id": "FrozenParry_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"], "2": ["_"], "3": ["SwordShield_InitAttack", "SwordShield_Dodge_Step", "TS_InitAttack", "TS_Dodge_Step", "S<PERSON>_InitAttack", "S<PERSON>_Dodge_Step", "GSword_InitAttack", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_Parry", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_Parry", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_Parry", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_Parry", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_Parry"]}}, {"说明": "光盾", "Id": "AegisImpact_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_AegisImpact", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_AegisImpact", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_AegisImpact", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_AegisImpact", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_AegisImpact"]}}, {"说明": "雷锤", "Id": "LightingHammer_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_LightingHammer", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_LightingHammer", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_LightingHammer", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_LightingHammer", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_LightingHammer"]}}, {"说明": "暗影飞刀", "Id": "ShadowHiddenWeapon_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ShadowHiddenWeapon", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ShadowHiddenWeapon", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ShadowHiddenWeapon", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ShadowHiddenWeapon", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_ShadowHiddenWeapon"]}}, {"说明": "投掷镰刀", "Id": "ThrowScythe_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ThrowScythe", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ThrowScythe", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ThrowScythe", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ThrowScythe", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_ThrowScythe"]}}, {"说明": "圆月斩", "Id": "CircleSlash_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_CircleSlash", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_CircleSlash", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_CircleSlash", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_CircleSlash", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_CircleSlash"]}}, {"说明": "法器-唤龙", "Id": "Item_SummonD<PERSON>on", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_EternalDragonflame", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_EternalDragonflame", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_EternalDragonflame", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_EternalDragonflame", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_EternalDragonflame"]}}, {"说明": "剑气附魔", "Id": "Range_Enchant", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_Enchant_SwordLight", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_Enchant_SwordLight", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_Enchant_SwordLight", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_Enchant_SwordLight", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_Enchant_SwordLight"]}}, {"说明": "狂怒祝福", "Id": "BerserkMode_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_Enchant_BerserkMode", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_Enchant_BerserkMode", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_Enchant_BerserkMode", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_Enchant_BerserkMode", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_Enchant_BerserkMode"]}}, {"说明": "精准祝福", "Id": "CritUpItem_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_Enchant_Blessing", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_Enchant_Blessing", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_Enchant_Blessing", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_Enchant_Blessing", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_Enchant_Blessing"]}}, {"说明": "雷流体", "Id": "ThunderProtect_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ThunderProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ThunderProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ThunderProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ThunderProtect", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_ThunderProtect"]}}, {"说明": "落雷之咒", "Id": "RangeThunder_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ThunderAttack", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ThunderAttack", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ThunderAttack", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ThunderAttack", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_ThunderAttack"]}}, {"说明": "黑洞", "Id": "DarkHole_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_BlackHole", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_BlackHole", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_BlackHole", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_BlackHole", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_BlackHole"]}}, {"说明": "风洞", "Id": "WindHole_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_WindHole", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_WindHole", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_WindHole", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_WindHole", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_WindHole"]}}, {"说明": "地震", "Id": "EarthQuake_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_EarthQuake", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_EarthQuake", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_EarthQuake", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_EarthQuake", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_EarthQuake"]}}, {"说明": "冰霜飞镰", "Id": "Throw<PERSON><PERSON>the_Flame_Rogue", "Cmds": [], "Tags": [{"Tag": "Unarm_UseItem", "From": 0}, {"Tag": "UseArtifact", "From": 0}], "BeCancelledTags": {"0": ["SwordShield_InitAttack", "TS_InitAttack", "S<PERSON>_InitAttack", "GSword_InitAttack"], "1": ["SwordShield_Move", "SwordShield_Dodge_Step", "TS_Move", "TS_Dodge_Step", "Spear_Move", "S<PERSON>_Dodge_Step", "GSword_Move", "GSword_Dodge_Step"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.ReturnPawnClassIndex()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Fighter/Action/MagicItem/MagicItem_ThrowScythe_Flame", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_ThrowScythe_Flame", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_ThrowScythe_Flame", "ArtResource/ProjectRogue/Anim/Montage/Player/Warrior/Action/MagicItem/MagicItem_ThrowScythe_Flame", "ArtResource/ProjectRogue/Anim/Montage/Player/WarriorElf/Action/MagicItem/MagicItem_ThrowScythe_Flame"]}}]}