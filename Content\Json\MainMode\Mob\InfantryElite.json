{"Mob": [{"Id": "InfantryElite", "Tag": ["HumanInfantry"], "AI": ["InfantryTurnToStimulate"], "BpPath": "Core/Characters/Infantry/InfantryElite/Infantry_Elite", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "Flyable": false, "LootPackageId": "InfantryNormal", "MountsTypeRotate": false, "Name": "精英人类步兵", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 500, "SightHalfAngleDregee": 135, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 1, "Attack": 5, "Balance": 10, "MoveSpeed": [265, 525, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [{"Id": "InfantrySpear", "Rate": 1}], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 0.5}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Unarmed_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Standard_Move", "UnArmed": "Unarmed_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/NPC/Guard/Move_Spear"]}}, {"Id": "Unarmed_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/NPC/Guard/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/Hurt_Front_Spear", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/HurtFromFront"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Hurt/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_S3"]}, "InitAction": true}, {"说明": "近距离攻击4", "Id": "NormalAttack_S4", "Cmds": ["NormalAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_S4"]}, "InitAction": true}, {"说明": "近距离攻击5", "Id": "NormalAttack_S5", "Cmds": ["NormalAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_S5"]}, "InitAction": true}, {"说明": "中距离攻击1", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_M1"]}, "InitAction": true}, {"说明": "中距离攻击2", "Id": "NormalAttack_M2", "Cmds": ["NormalAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_M2"]}, "InitAction": true}, {"说明": "中距离攻击3", "Id": "NormalAttack_M3", "Cmds": ["NormalAttack_M3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_M3"]}, "InitAction": true}, {"说明": "远距离攻击1", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "前跳", "Id": "Dodge_Front", "Cmds": ["Dodge_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Dodge/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "左跳", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Dodge/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "右跳", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Dodge/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Dodge/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "向前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Front"]}, "InitAction": true}, {"说明": "向后走", "Id": "Walk_Back", "Cmds": ["Walk_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Back"]}, "InitAction": true}, {"说明": "向左走", "Id": "Walk_Left", "Cmds": ["Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Left"]}, "InitAction": true}, {"说明": "向右走", "Id": "Walk_Right", "Cmds": ["Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Orc/Orc_Fighter/Hurt/Walk_Right"]}, "InitAction": true}, {"说明": "原地发呆动作", "Id": "IdleDaze", "Cmds": ["IdleDaze"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Spear/Idle_<PERSON>ze"]}, "InitAction": true}, {"说明": "敬礼（可以的话最好能玩家经过他前面的交互盒子的时候，做一次这个动作）", "Id": "Guard_Salute", "Cmds": ["Guard_Salute"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Guard_Salute"]}, "InitAction": true}, {"说明": "坐在篝火旁", "Id": "Guard_IN_CF_sit_loop", "Cmds": ["Guard_IN_CF_sit_loop"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Guard_IN_CF_sit_loop"]}, "InitAction": true}, {"说明": " 蹲在篝火旁", "Id": "Anim_CR_inspect", "Cmds": ["Anim_CR_inspect"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_CR_inspect"]}, "InitAction": true}, {"说明": "挥手招人", "Id": "An<PERSON>_<PERSON><PERSON>_CS_call_01", "Cmds": ["An<PERSON>_<PERSON><PERSON>_CS_call_01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_EM_CS_call_01"]}, "InitAction": true}, {"说明": "指向某处", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_CS_PT_01", "Cmds": ["<PERSON><PERSON>_<PERSON><PERSON>_CS_PT_01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_EM_CS_PT_01"]}, "InitAction": true}, {"说明": "打扫武器库", "Id": "Cleaner_2", "Cmds": ["Cleaner_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Cleaner_2"]}, "InitAction": true}, {"说明": "炮兵动作", "Id": "UE4M_Push_Idle_inPlace_Anim", "Cmds": ["UE4M_Push_Idle_inPlace_Anim"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/UE4M_Push_Idle_inPlace_Anim"]}, "InitAction": true}, {"说明": "拿着炮弹的士兵", "Id": "Anim_IN_SAST", "Cmds": ["Anim_IN_SAST"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_IN_SAST"]}, "InitAction": true}, {"说明": "指着方向（在小路门口，玩家接近时循环做这个动作）", "Id": "Grenade_Crouch_Rc_Go", "Cmds": ["Grenade_Crouch_Rc_Go"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Grenade_Crouch_Rc_Go"]}, "InitAction": true}, {"说明": "指着方向（在小路门口，玩家接近时循环做这个动作）", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_CS_PT_01", "Cmds": ["<PERSON><PERSON>_<PERSON><PERSON>_CS_PT_01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_EM_CS_PT_01"]}, "InitAction": true}, {"说明": "攀在木头上", "Id": "UE4M_WallPoints_Aim_Bwd_Anim", "Cmds": ["UE4M_WallPoints_Aim_Bwd_Anim"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/UE4M_WallPoints_Aim_Bwd_Anim"]}, "InitAction": true}, {"说明": "放松站姿（不拿武器、循环播放）", "Id": "Anim_Idle_3", "Cmds": ["Anim_Idle_3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_Idle_3"]}, "InitAction": true}, {"说明": "放松站姿（不拿武器、循环播放）", "Id": "Anim_<PERSON>dle_4", "Cmds": ["Anim_<PERSON>dle_4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_Idle_4"]}, "InitAction": true}, {"说明": "放松站姿（拿长枪、循环播放）", "Id": "<PERSON><PERSON>_Relaxed", "Cmds": ["<PERSON><PERSON>_Relaxed"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Spear_Relaxed"]}, "InitAction": true}, {"说明": "放松站姿（拿剑盾、循环播放）", "Id": "Sword1h_Relaxed_Idle", "Cmds": ["Sword1h_Relaxed_Idle"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Spear_Relaxed"]}, "InitAction": true}, {"说明": "蹲姿动作（拿剑盾、拿长枪）", "Id": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_Idle", "Cmds": ["<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON>_Idle"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/TwinDaggers_Crouch_Idle"]}, "InitAction": true}, {"说明": "看某个地方四处张望", "Id": "Anim_Watching_Somewhere", "Cmds": ["Anim_Watching_Somewhere"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_Watching_Somewhere"]}, "InitAction": true}, {"说明": "看某个地方四处张望2", "Id": "Anim_Watching_Somewhere_1_2", "Cmds": ["Anim_Watching_Somewhere_1_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_Watching_Somewhere_1_2"]}, "InitAction": true}, {"说明": "看某个地方四处张望3", "Id": "Anim_Watching_Somewhere_1_3", "Cmds": ["Anim_Watching_Somewhere_1_3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_Watching_Somewhere_1_3"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作1", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01", "Cmds": ["<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作2", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01_2", "Cmds": ["<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01_2"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作3", "Id": "<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01_3", "Cmds": ["<PERSON><PERSON>_<PERSON><PERSON>_CS_victory_01_3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01_3"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作4", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_4", "Cmds": ["<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01_4"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作5", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_5", "Cmds": ["<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01_5"]}, "InitAction": true}, {"说明": "炸破大门时的庆祝动作6", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_6", "Cmds": ["<PERSON><PERSON><PERSON><PERSON><PERSON>_CS_victory_01_6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/NPC/Guard/Action/Anim_E<PERSON>_CS_victory_01_6"]}, "InitAction": true}]}]}