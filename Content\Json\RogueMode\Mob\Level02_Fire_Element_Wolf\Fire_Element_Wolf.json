{"Mob": [{"Id": "Rogue_Fire_Element_Wolf", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "MoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Wolf/Fire_Element_Wolf/Fire_Element_Wolf", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "狱原狼", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 4, "PAtk": 10, "Balance": 10, "MoveSpeed": [320, 500, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "ImmuneLava", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Wolf/Fire_Element_Wolf/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Wolf/Fire_Element_Wolf/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "发呆2", "Id": "Action_Stare02", "Cmds": ["Action_Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Action_Stare02"]}, "InitAction": true}, {"说明": "发呆4", "Id": "Action_Stare04", "Cmds": ["Action_Stare04"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Action_Stare04"]}, "InitAction": true}, {"说明": "发呆5", "Id": "Action_Stare05", "Cmds": ["Action_Stare05"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Action_Stare05"]}, "InitAction": true}, {"说明": "跳跃左", "Id": "Action_Jump_F", "Cmds": ["Action_Jump_F"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Action_Jump_F"]}, "InitAction": true}, {"说明": "dodge左", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "跳跃右", "Id": "Jump_F", "Cmds": ["Jump_F"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Jump_F"]}, "InitAction": true}, {"说明": "dodge右", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Wolf/Fire_Element_Wolf/Battle/Dodge_DashStep_Right"]}, "InitAction": true}]}], "Bullet": [{"Id": "<PERSON>_FireBall", "Tag": [], "BpPath": "Core/Item/Bullet/FireBall", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(1)", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Grux_Magma_Ultimate_Cast)"], "OnRemoved": []}]}