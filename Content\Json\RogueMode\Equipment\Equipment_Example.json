{"Equipment_范例": [{"Id": "装备的id，只有防具都是装备", "Meat": {"伤害类型": "<float> 对应类型的减伤率"}, "EquipmentPart": "装备类型：Head,Body,Arm,Leg,Weapon", "Property": {"属性key": "<int> 属性值"}, "CharacterPart": "防具部位，武器就用空字符串。Head,Body,Arm,Leg,Wing,Tail,Horn,Back,Shield,Other", "MeatType": "Meat(肉),Metal(金属),Void(虚灵),Skeleton(骷髅)", "Durability": "<int> 耐久度，会影响到装备部位的显示", "FXKey": ["播放视觉和听觉特效的关键字，动画的AnimationNotify（AnimationFX等）中会依赖到此项，有点对暗号的意思，详细请参阅公司微盘的《FX命名规范》"], "Clip": {"0": "<int>每个Clip的等级值，0或者更低就代表这个Clip无货，一个装备必须至少有一个Clip>0，并且Clip必须为4个(0-3)", "1": 0, "2": 1, "3": 0}, "Appearance": [{"说明": "每个都是一个这件装备的视觉部位，一个装备是由多个视觉部位组成的，至少……你得有一个吧，没有也不是不行，比如戒指", "BluePrintPath": "Core/Characters/Equipment/BigSword 对应装备部位蓝图的位置", "Priority": "<int> 显示优先级", "PartSlot": "这是一个表达这个视觉部位应该存在哪个slot的东西，这个slot是可以自定义的（任意String）", "ConflictSlots": ["与这个外观冲突的PartSlot，只要包含就会挨个检查优先级（Priority），如果其中一个高于(>)自己就不会显示自己，否则就隐藏掉那些"], "BindPointId": "RightWeapon 绑点id，RightWeapon LeftWeapon EquipmentRoot Feather", "AppearanceType": "外观类别：Normal：普通类型组件，比如武器一般都是，没有动画，不用蒙皮；SkinnedMesh,动画类型组件，比如角色装备、弓箭等会跟着角色动画播放的，属于蒙皮组件；Physical 物理组件，需要计算物理的，比如衣服上的飘带、披风、狼皮等", "PhysicalBoneName": "PhysicalBone 从这个名称对应的骨骼开始，往下（包括这根骨骼）都会开始接受物理的影响，前提是AppearanceType==Physical", "HidePart": ["角色身体上要隐藏的部件，Head Body LeftArm RightArm LeftLeg RightLeg"], "Show": {"Min": "最小多少耐久度显示，包含", "Max": "最大多少耐久度显示，包含"}, "ShowOnType": ["CharacterObj.TypeId包含在这个列表里，或者这个列表是空，就会显示这个部位"]}]}], "Weapon_范例": [{"Id": "装备的id，武器防具都是装备", "Attack": {"伤害类型": "<float> 伤害值"}, "WeaponType": "武器精确类别： UnArmed,BigSword,OneHandSword,PoleArm", "Elemental": "武器的元素属性，默认Physical，可选Physical Fire Water Wind Thunder Light Darkness Earth", "Property": {"属性key": "<int> 属性值"}, "FXKey": ["播放视觉和听觉特效的关键字，动画的AnimationNotify（AnimationFX等）中会依赖到此项，有点对暗号的意思，详细请参阅公司微盘的《FX命名规范》"], "Clip": {"0": "<int>每个Clip的等级值，0或者更低就代表这个Clip无货，一个装备必须至少有一个Clip>0，并且Clip必须为4个(0-3)", "1": 0, "2": 1, "3": 0}, "RightIKBindPointId": ["右手IK的点跟随哪个绑点，不同武装状态在不同的挂点"], "LeftIKBindPointId": ["左手IK的点跟随哪个绑点，不同武装状态在不同的挂点"], "Appearance": [{"说明": "每个都是一个这件装备的视觉部位，一个装备是由多个视觉部位组成的，至少……你得有一个吧，没有也不是不行，比如戒指", "BluePrintPath": "Core/Characters/Equipment/BigSword 对应装备部位蓝图的位置", "Priority": "<int> 显示优先级", "PartSlot": "这是一个表达这个视觉部位应该存在哪个slot的东西，这个slot是可以自定义的（任意String）", "ConflictSlots": ["与这个外观冲突的PartSlot，只要包含就会挨个检查优先级（Priority），如果其中一个高于(>)自己就不会显示自己，否则就隐藏掉那些"], "BindPointId": "RightWeapon 绑点id，RightWeapon LeftWeapon EquipmentRoot Feather", "AppearanceType": "外观类别：Normal：普通类型组件，比如武器一般都是，没有动画，不用蒙皮；SkinnedMesh,动画类型组件，比如角色装备、弓箭等会跟着角色动画播放的，属于蒙皮组件；Physical 物理组件，需要计算物理的，比如衣服上的飘带、披风、狼皮等", "PhysicalBoneName": "PhysicalBone 从这个名称对应的骨骼开始，往下（包括这根骨骼）都会开始接受物理的影响，前提是AppearanceType==Physical", "HidePart": ["角色身体上要隐藏的部件，Head Body LeftArm RightArm LeftLeg RightLeg"], "Show": {"Min": "最小多少耐久度显示，包含", "Max": "最大多少耐久度显示，包含"}, "ShowOnType": ["CharacterObj.TypeId包含在这个列表里，或者这个列表是空，就会显示这个部位"]}], "OffAppearance": [{"说明": "格式和Appearance一样的，区别在于，这是作为副武器时候用的数据，当然一些没有作为副武器可能性的武器可以不填写，比如大剑"}]}]}