{"AIScript": [{"Id": "DoUltAttack", "Condition": ["IceDevilAIScript.CheckHateBuffStack(100)", "IceDevilAIScript.CheckNoFreezingSceneItem()"], "OnReady": [], "Action": ["IceDevilAIScript.TeleportToCenterDoUlt(Dodge_Teleport_Forward_Ult)"], "Tag": "Attack"}, {"Id": "DoChangeState", "Condition": ["IceDevilAIScript.CheckAIHpPercentLess(0.4)", "IceDevilAIScript.CheckNotHasSecondStageBuff()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(ChangeState)"], "Tag": "Attack"}, {"Id": "DoRageStateAction", "Condition": ["IceDevilAIScript.CheckDoRangeStageAction()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(RageState)"], "Tag": "Attack"}, {"Id": "AttackFreezingEnemy", "Condition": ["IceDevilAIScript.CheckAIExcited()", "IceDevilAIScript.CheckHasFreezingEnemy()", "IceDevilAIScript.CheckCanThrowIceSycthe()", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackFreezingTarget(1300,S1_Attack_M1,S2_Attack_M1,S1_Attack_XL1,S2_Attack_XL1)"], "Tag": "Attack"}, {"Id": "AttackIceSceneItem", "Condition": ["IceDevilAIScript.CheckAIExcited()", "IceDevilAIScript.CheckHasFreezingSceneItemInView(700)", "IceDevilAIScript.CheckCanThrowIceSycthe()", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackIceSceneItem(S2_Attack_XL1,S2_Attack_XL1,S1_Attack_L1,S2_Attack_XL1)"], "Tag": "Attack"}, {"Id": "AttackBunchEnemies", "Condition": ["IceDevilAIScript.CheckCanBunchEnemies(400)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackViewedBunchEnemies(S1_Attack_M2,S2_Attack_M2)"], "Tag": "Attack"}, {"Id": "AttackNearTwoViewedEnemies", "Condition": ["IceDevilAIScript.CheckViewedEnemysDistanceBetween(300)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackNearTwoEnemies(200,S1_Attack_S3,S2_Attack_S1_3,500,S1_Attack_M1,S2_Attack_M1,S1_Attack_L1,S2_Attack_L1)"], "Tag": "Attack"}, {"Id": "AttackFarTwoViewedEnemies", "Condition": ["IceDevilAIScript.CheckViewedEnemysDistanceBetween(700)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackFarTwoEnemies(500,S1_Attack_M1,S2_Attack_M1,S1_Attack_L1,S2_Attack_L1)"], "Tag": "Attack"}, {"Id": "AttackNearOneViewedEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(0,600)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackNearOneViewedEnemy(S1_Attack_S1_1,S2_Attack_S1_1)"], "Tag": "Attack"}, {"Id": "AttackMiddleDisOneViewedEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1100,1400)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackMiddleDisOneViewedEnemy(S1_Attack_M1,S2_Attack_M1)"], "Tag": "Attack"}, {"Id": "AttackFarOneViewedEnemy", "Condition": ["MobAIScript.CheckHasViewedEnemyInRange(1600,2000)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackFarOneViewedEnemy(S1_Attack_L1,S2_Attack_L1)"], "Tag": "Attack"}, {"Id": "MoveToFarOneViewedEnemy", "Condition": ["IceDevilAIScript.CheckHasEnemyInView()", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.MoveToOneViewedEnemy(1300,DashStep_Forward,Dodge_Forward,Dodge_Teleport_Forward)"], "Tag": "Attack"}, {"Id": "AttackAroundEnemies", "Condition": ["IceDevilAIScript.CheckNearEnemyNum(500,3)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackAroundEnemies(S1_Attack_S5,Dodge_Teleport_Back,Dodge_Back)"], "Tag": "Attack"}, {"Id": "AttackHigherEnemy", "Condition": ["IceDevilAIScript.CheckViewedEnemyHeight(50)", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackHigherEnemy(50,300,Dodge_Teleport_Up,S1_Attack_XL1)"], "Tag": "Attack"}, {"Id": "TiredAction", "Condition": ["IceDevilAIScript.CheckAITired()", "IceDevilAIScript.CheckHasEnemy()"], "OnReady": [], "Action": ["IceDevilAIScript.DoTiredAction(500,Dodge_Back,800,Walk_Back,1500,Walk_Forward,Dodge_Forward)"], "Tag": "Attack"}, {"Id": "AttackRandomTarget", "Condition": ["IceDevilAIScript.CheckNoViewedEnemy()", "IceDevilAIScript.CheckAINotTired()"], "OnReady": [], "Action": ["IceDevilAIScript.AttackRandomTarget(200,DashStep_Back,800,DashStep_Left,DashStep_Right,2000,DashStep_Forward,Dodge_Teleport_Forward,Dodge_Forward)"], "Tag": "Attack"}]}