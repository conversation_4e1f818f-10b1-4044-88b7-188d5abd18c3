{"说明": "物品显示在游戏的UI上所需要的数据", "范例": {"ThingType": "东西的类型，暂时只支持Character、Buff、Currency、Equipment、Item、Switch、WeaponModel、WeaponObj、Pointer", "ThingId": "对应东西的id，有些玩法也会对此有歧义，比如武器（WeaponObj）在这里就是按照WeaponType来的", "Icon": "使用的图标", "Name": "使用的名称", "Description": "一些描述文字，描述文字针对不同类型的物品，显示方式不同（有些甚至不显示）", "Rank": "<int>档次值，可以理解为0-4对应白绿蓝紫橙"}, "ThingUIInfo": [{"i": "_____________________________________Item____________________________________________"}, {"ThingType": "<PERSON><PERSON>", "ThingId": "HealingPotion", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Name": "HealingPotion", "Description": "回复30%的生命值", "Rank": 0}, {"ThingType": "<PERSON><PERSON>", "ThingId": "FireBallScroll", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/Fireball", "Name": "FireBallScroll", "Description": "火焰卷轴，撕开能释放基础火球术", "Rank": 0}, {"i": "_____________________________________Currency____________________________________________"}, {"ThingType": "<PERSON><PERSON><PERSON><PERSON>", "ThingId": "Gold", "Icon": "ArtResource/UI/Icon/Item/Temp/Gold", "Name": "Gold", "Description": "Shiny Clink Gold", "Rank": 0}, {"i": "_____________________________________Weapon___Spear______________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Iron_Spear", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "Iron_Spear", "Description": "", "Rank": 4, "SceneCameraLocation": "X=-430,Y=0,Z=-10", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Golden<PERSON><PERSON>_Spear", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "Golden<PERSON><PERSON>_Spear", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-500,Y=0,Z=-13", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "DarkSpear", "Icon": "ArtResource/UI/Icon/EquipPart/Spear", "Name": "DarkSpear", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-500,Y=3,Z=-2", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Weapon___GreatSwords_________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Iron_GreatSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "Iron_GreatSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-400,Y=0,Z=-12", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=-0.268858,Y=19.669182,Z=65.779297"}, {"ThingType": "WeaponObj", "ThingId": "Elven_GreatSword", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "Elven_GreatSword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Legolas_Claymore", "Icon": "ArtResource/UI/Icon/EquipPart/BigSword", "Name": "Legolas_Claymore", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-400,Y=0,Z=-15", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Weapon___Shields_____________________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Skeleton_Buckler", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Skeleton_Buckler", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-210,Y=0,Z=0", "ModelRotator": "P=-10.220591,Y=37.888573,R=14.136207", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "HolyTree_Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "HolyTree_Shield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-9.084525,Y=24.405729,R=95.997337", "ModelLocation": "X=0,Y=0,Z=0"}, {"ThingType": "WeaponObj", "ThingId": "Wooden_Shield", "Icon": "ArtResource/UI/Icon/EquipPart/Shield", "Name": "Wooden_Shield", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-280,Y=0,Z=0", "ModelRotator": "P=-9.084525,Y=24.405729,R=95.997337", "ModelLocation": "X=0,Y=0,Z=0"}, {"i": "_____________________________________Weapon___OneHandSwords_______________________________________"}, {"ThingType": "WeaponObj", "ThingId": "Skeleton_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Skeleton_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=0,Z=5", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Legolas_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Legolas_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=1,Z=4", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"ThingType": "WeaponObj", "ThingId": "Iron_Sword", "Icon": "ArtResource/UI/Icon/EquipPart/Sword", "Name": "Iron_Sword", "Description": "只有名字牛逼？测试用的你还想咋的？", "Rank": 4, "SceneCameraLocation": "X=-230,Y=1,Z=4", "ModelRotator": "P=-15.318192,Y=121.658379,R=-8.311812", "ModelLocation": "X=0.000000,Y=14.000000,Z=47.646797"}, {"i": "_____________________________________Equipment____________________________________________"}, {"ThingType": "Equipment", "ThingId": "Warrior<PERSON>_Helmet", "Icon": "ArtResource/UI/Icon/EquipPart/Head", "Name": "流浪骑士的羽饰头盔", "Description": "初始装备", "Rank": 0, "SceneCameraLocation": "X=-150,Y=0,Z=80", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior01_Armor", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "流浪骑士的胸铠", "Description": "初始装备", "Rank": 0, "SceneCameraLocation": "X=-300,Y=2,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior01_Glove", "Icon": "ArtResource/UI/Icon/EquipPart/Arm", "Name": "北境骑兵手甲", "Description": "新版战士护手，新装备哟", "Rank": 0, "SceneCameraLocation": "X=-300,Y=0,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior<PERSON>_Boot", "Icon": "ArtResource/UI/Icon/EquipPart/Leg", "Name": "北境骑兵重靴", "Description": "新版战士皮裤，新装备哟", "Rank": 0, "SceneCameraLocation": "X=-310,Y=-3,Z=-25", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior02_Helmet", "Icon": "ArtResource/UI/Icon/EquipPart/Head", "Image": "", "Name": "神殿精灵卫士的镶金重盔", "Description": "第二阶段提升部件", "Rank": 0, "SceneCameraLocation": "X=-150,Y=0,Z=80", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior02_Armor", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "神殿精灵卫士的镶金胸铠", "Description": "第二阶段提升部件", "Rank": 0, "SceneCameraLocation": "X=-300,Y=2,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior02_Glove", "Icon": "ArtResource/UI/Icon/EquipPart/Arm", "Name": "流浪骑士的臂铠", "Description": "第一阶段提升部件,当前版本最终装备", "Rank": 0, "SceneCameraLocation": "X=-300,Y=0,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior02_Boot", "Icon": "ArtResource/UI/Icon/EquipPart/Leg", "Name": "流浪骑士的腿铠", "Description": "第一阶段提升部件,", "Rank": 0, "SceneCameraLocation": "X=-310,Y=-3,Z=-25", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior03_Helmet", "Icon": "ArtResource/UI/Icon/EquipPart/Head", "Name": "原初头盔", "Description": "最初版本战士头盔", "Rank": 0, "SceneCameraLocation": "X=-150,Y=0,Z=80", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior03_Armor", "Icon": "ArtResource/UI/Icon/EquipPart/Body", "Name": "冰魄收割者的爪痕", "Description": "当前版本最终装备", "Rank": 0, "SceneCameraLocation": "X=-300,Y=2,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior03_<PERSON>love", "Icon": "ArtResource/UI/Icon/EquipPart/Arm", "Name": "原初护手", "Description": "最初版本战士护手", "Rank": 0, "SceneCameraLocation": "X=-300,Y=0,Z=40", "ModelRotator": "R=0,P=0,Y=180"}, {"ThingType": "Equipment", "ThingId": "Warrior03_Boot", "Icon": "ArtResource/UI/Icon/EquipPart/Leg", "Name": "原初皮裤", "Description": "最初版本战士皮裤", "Rank": 0, "SceneCameraLocation": "X=-310,Y=-3,Z=-25", "ModelRotator": "R=0,P=0,Y=180"}]}