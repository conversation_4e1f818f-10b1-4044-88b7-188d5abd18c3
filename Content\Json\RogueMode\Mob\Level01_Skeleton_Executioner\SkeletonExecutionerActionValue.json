{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Rogue_Skeleton_Executioner", "Actions": [{"Id": "NormalAttack_L1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 100, "MaxRange": 500, "Weight": 5}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 100, "MaxRange": 300, "Weight": 5}, {"MinRange": 300, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_S3", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 100, "MaxRange": 200, "Weight": 5}, {"MinRange": 200, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 200, "Weight": 3}, {"MinRange": 200, "MaxRange": 99999, "Weight": -99}], "OutofCamera": -4, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": -99}, {"MinRange": 1000, "MaxRange": 9999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 50}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 4, "MaxActionCD": 8}, {"Id": "Stare02", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": -99}, {"MinRange": 1000, "MaxRange": 9999, "Weight": -99}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 50}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 6}]}]}