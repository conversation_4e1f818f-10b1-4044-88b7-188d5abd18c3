{"AOE": [{"Id": "FallingPillar", "Tag": ["FallingPillar"], "TickTime": 0, "BpPath": "Core/Item/AOE/PillarAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "PillarExplosion", "Tag": ["PillarExplosion"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/PillarExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.DealDamageByFallingPillar(250, 0.04)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "PillarAutoExplosion", "Tag": ["PillarAutoExplosion"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/PillarExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.DealDamageByFallingPillar(20, 0)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "<PERSON><PERSON>", "Tag": ["<PERSON><PERSON>"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/PillarCheckAOE", "OnCreate": [], "OnTick": ["Ogre_AOEScript.ConnectToPillar()"], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.SetCanPickPillarBuffOnCharacterEnter()"], "OnCharacterLeave": ["Ogre_AOEScript.RemoveCanPickPillarBuffOnCharacterLeave()"], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Roar_AOE", "Tag": ["PillarAOE"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/AOE_Troll_Roar", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageAndBlowOnTouch(10,0.15,500,0,150,0.7,6)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}]}