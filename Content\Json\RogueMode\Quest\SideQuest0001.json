{"QuestInfo": [{"id": "SideQuest0001", "Desc": "支线任务1", "State": "InActive", "Type": "Main", "StartStage": "Stage1", "StageList": [{"id": "Stage1", "Desc": "测试任务阶段1", "StageLevel": "", "InStageAction": ["进入该Stage会调用的行为"], "TargetList": ["Target1"], "NextStagePolicy": ""}, {"id": "Stage2", "Desc": "测试任务阶段2", "StageLevel": "", "InStageAction": ["进入该Stage会调用的行为"], "TargetList": ["Target2", "Target3"], "NextStagePolicy": ""}], "LootList": [], "ActiveConditions": ["TriggerScript.CheckSwitchInRoleGreater(RodianStory01,1)"], "AcceptConditions": ["TriggerScript.CheckSwitchInRoleLess(RodianStory06,1)"], "ActiveActions": ["TriggerScript.SetSwitchValueInRole(RodianStory02,3)"], "AcceptActions": ["TriggerScript.SetSwitchValueInRole(DarkSpear)"], "FailedActions": ["TriggerScript.SetSwitchValueInRole(DarkSpear)"], "CompleteActions": ["TriggerScript.PlayerGetItemByPackageIds(Elven_GreatSword)"]}], "QuestTargetInfo": [{"id": "SideQuest0001Target01", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Kill", "UIType": "Text", "FailedCondition": [], "StartProgress": "0", "MaxProgress": "1"}, {"id": "SideQuest0001Target02", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Collect", "UIType": "Text", "FailedCondition": [], "StartProgress": "1", "MaxProgress": "2"}, {"id": "SideQuest0001Target03", "Desc": "击杀<MonsterName>,到<AreaName>获取<ItemName>,", "Type": "Cost", "UIType": "Text", "FailedCondition": [], "StartProgress": "2", "MaxProgress": "3"}]}