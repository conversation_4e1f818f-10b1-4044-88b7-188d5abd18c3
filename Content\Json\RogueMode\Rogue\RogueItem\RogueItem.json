{"RogueItem": [{"ItemId": "Global_Setting", "Tags": ["Rogue", "NotInPool"], "MaxEnergy": 1000}, {"Line": "________________________上面的是2.1版本道具全局属性____________________________"}, {"ItemId": "HealingPotion_Rogue", "Tags": ["Rogue", "Heal", "Potion", "NotInPool"], "MaxEnergy": 400, "CostEnergy": 100, "OrgEffectLevel": 35, "MaxEffectLevel": 100, "OrgEffectValue": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 999, "Effect": "RogueItemUseEffect.UseRogueHealingPotion()"}]}, {"Line": "________________________上面的是固定血瓶 下面才是池子的主动道具____________________________"}, {"ItemId": "Cannon_Rogue", "Tags": ["Rogue", "Fire", "Word_RogueBurning"], "CreaterMaxNum": 4, "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/Cannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "2", "DamagePower": "1", "BreakPower": "1.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/Cannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "2", "DamagePower": "1.5", "BreakPower": "2.25"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/Cannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "2", "DamagePower": "2.5", "BreakPower": "3.75"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/Cannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "2", "DamagePower": "4", "BreakPower": "6"}}]}, {"ItemId": "LightCannon_Rogue", "Tags": ["Rogue", "Word_RogueBurning"], "CreaterMaxNum": 4, "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 250, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/LightCannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "0.2", "DamagePower": "0.5", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/LightCannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "0.2", "DamagePower": "0.75", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/LightCannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "0.2", "DamagePower": "1.25", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateTargetOnUserPoint(Core/Item/Rouge/LightCannon_Pack_Rogue,0,0,0)", "SpecialParams": {"LifeTime": "6", "CoolDown": "0.2", "DamagePower": "2", "BreakPower": "0"}}]}, {"ItemId": "FrozenDash_Rogue", "Tags": ["Rogue", "Ice", "Word_RogueIce"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenDash,1)", "SpecialParams": {"DamagePower": "1.5", "BreakPower": "1", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "4"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenDash,1)", "SpecialParams": {"DamagePower": "2.25", "BreakPower": "1.5", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenDash,1)", "SpecialParams": {"DamagePower": "3.75", "BreakPower": "2.5", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "6"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenDash,1)", "SpecialParams": {"DamagePower": "6", "BreakPower": "4", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "7"}}], "OnUsingEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,20,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,20)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,30,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,30)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,50,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,50)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,80,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,80)"}]}, {"ItemId": "FireDash_Rogue", "Tags": ["Rogue", "Word_RogueIce"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FireDash,1)", "SpecialParams": {"LifeTime": "1", "DamagePower": "2.25", "BreakPower": "0.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FireDash,1)", "SpecialParams": {"LifeTime": "1", "DamagePower": "3.75", "BreakPower": "0.75"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FireDash,1)", "SpecialParams": {"LifeTime": "1", "DamagePower": "5.6", "BreakPower": "1.25"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FireDash,1)", "SpecialParams": {"LifeTime": "1", "DamagePower": "9", "BreakPower": "1.25"}}], "OnUsingEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,20,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,20)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,30,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,30)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,50,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,50)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,80,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,80)"}]}, {"ItemId": "WindProtect_Rogue", "Tags": ["Rogue", "Wind", "Word_RogueWind"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemSpeedUp,1000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemSpeedUp,2000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemSpeedUp,3000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemSpeedUp,5000,15,false)"}]}, {"ItemId": "WindAroundKnife_Rogue", "Tags": ["Rogue", "Wind", "Word_RogueWind"], "MaxEffectLevel": 4, "CreaterMaxNum": 10, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_WindAroundKnife,0.1)", "SpecialParams": {"LifeTime": "10", "DamagePower": "0.5", "BreakPower": "0", "Num": "2"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_WindAroundKnife,0.1)", "SpecialParams": {"LifeTime": "10", "DamagePower": "0.75", "BreakPower": "0", "Num": "3"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_WindAroundKnife,0.1)", "SpecialParams": {"LifeTime": "10", "DamagePower": "1.25", "BreakPower": "0", "Num": "4"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_WindAroundKnife,0.1)", "SpecialParams": {"LifeTime": "10", "DamagePower": "2", "BreakPower": "0", "Num": "5"}}]}, {"ItemId": "LightingHammer_Rogue", "Tags": ["Rogue", "Thunder", "Word_RogueElectric"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_LightningHammerController)", "SpecialParams": {"DamagePower": "10", "BreakPower": "20", "Num": "1"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_LightningHammerController)", "SpecialParams": {"DamagePower": "15", "BreakPower": "30", "Num": "1"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_LightningHammerController)", "SpecialParams": {"DamagePower": "25", "BreakPower": "50", "Num": "1"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_LightningHammerController)", "SpecialParams": {"DamagePower": "40", "BreakPower": "80", "Num": "2"}}], "OnUsingEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,20,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,20)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,50,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,50)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,70,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,70)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemHurtDamageDown,90,1,false,true)", "LoseEffect": "RogueItemUseEffect.RemoveSelfItemBuff(Rogue_ItemHurtDamageDown,90)"}]}, {"ItemId": "ShadowHiddenWeapon_Rogue", "Tags": ["Rogue", "Darkness", "Word_RogueCorrupte"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_ShadowKnifeLauncher,0.1)", "SpecialParams": {"LifeTime": "1", "Num": "5", "DamagePower": "0.5", "BreakPower": "0.5", "AddBuff": "Rogue_Corruption", "AddBuffStack": "6", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_ShadowKnifeLauncher,0.1)", "SpecialParams": {"LifeTime": "1", "Num": "5", "DamagePower": "0.75", "BreakPower": "0.75", "AddBuff": "Rogue_Corruption", "AddBuffStack": "10", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_ShadowKnifeLauncher,0.1)", "SpecialParams": {"LifeTime": "1", "Num": "5", "DamagePower": "1.25", "BreakPower": "1.25", "AddBuff": "Rogue_Corruption", "AddBuffStack": "16", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_ShadowKnifeLauncher,0.1)", "SpecialParams": {"LifeTime": "1", "Num": "5", "DamagePower": "2", "BreakPower": "2", "AddBuff": "Rogue_Corruption", "AddBuffStack": "25", "AddBuffTime": "10"}}]}, {"ItemId": "ThrowScythe_Rogue", "Tags": ["Rogue", "Weapon", "Word_RogueBlood"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.FireBullet(<PERSON><PERSON>,RightHandAttackBox,1500,2,true)", "SpecialParams": {"DamagePower": "8", "BreakPower": "8", "AddBuff": "Standard_PlayFrozen", "AddBuffStack": "1", "AddBuffTime": "4"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.FireBullet(<PERSON><PERSON>,RightHandAttackBox,1500,2,true)", "SpecialParams": {"DamagePower": "12", "BreakPower": "12", "AddBuff": "Standard_PlayFrozen", "AddBuffStack": "1", "AddBuffTime": "5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.FireBullet(<PERSON><PERSON>,RightHandAttackBox,1500,2,true)", "SpecialParams": {"DamagePower": "20", "BreakPower": "20", "AddBuff": "Standard_PlayFrozen", "AddBuffStack": "1", "AddBuffTime": "6"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.FireBullet(<PERSON><PERSON>,RightHandAttackBox,1500,2,true)", "SpecialParams": {"DamagePower": "32", "BreakPower": "32", "AddBuff": "Standard_PlayFrozen", "AddBuffStack": "1", "AddBuffTime": "7"}}]}, {"ItemId": "CircleSlash_Rogue", "Tags": ["Rogue", "Weapon"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_CircleSlashCenter,0.3)", "SpecialParams": {"DamagePower": "10", "BreakPower": "10", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_CircleSlashCenter,0.3)", "SpecialParams": {"DamagePower": "15", "BreakPower": "15", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_CircleSlashCenter,0.3)", "SpecialParams": {"DamagePower": "25", "BreakPower": "25", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_CircleSlashCenter,0.3)", "SpecialParams": {"DamagePower": "40", "BreakPower": "40", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}]}, {"ItemId": "SummonDragon_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "CreaterMaxNum": 1, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "AwakeDragon", "Num": "1", "LifeTime": 10, "Scale": "0.5", "Attack": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "AwakeDragon", "Num": "1", "LifeTime": 12, "Scale": "0.75", "Attack": "5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "AwakeDragon", "Num": "1", "LifeTime": 15, "Scale": "1", "Attack": "15"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "AwakeDragon", "Num": "1", "LifeTime": 20, "Scale": "1.5", "Attack": "30"}}]}, {"ItemId": "LandDragon_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "CreaterMaxNum": 1, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "LandDragon", "Num": "1", "LifeTime": 10, "Scale": "0.5", "Attack": "30"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "LandDragon", "Num": "1", "LifeTime": 12, "Scale": "0.75", "Attack": "40"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "LandDragon", "Num": "1", "LifeTime": 15, "Scale": "1", "Attack": "50"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateControllersOnUserPoint(Core/Item/CreaterController/BP_ItemMobCreater)", "SpecialParams": {"Mob": "LandDragon", "Num": "1", "LifeTime": 20, "Scale": "1.5", "Attack": "70"}}]}, {"ItemId": "DarkHole_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_DarkHole,1)", "SpecialParams": {"DamagePower": "0.5", "BreakPower": "2", "AddBuff": "Rogue_Corruption", "AddBuffStack": "10", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_DarkHole,1)", "SpecialParams": {"DamagePower": "0.75", "BreakPower": "3", "AddBuff": "Rogue_Corruption", "AddBuffStack": "15", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_DarkHole,1)", "SpecialParams": {"DamagePower": "1.25", "BreakPower": "5", "AddBuff": "Rogue_Corruption", "AddBuffStack": "25", "AddBuffTime": "10"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_DarkHole,1)", "SpecialParams": {"DamagePower": "2", "BreakPower": "8", "AddBuff": "Rogue_Corruption", "AddBuffStack": "35", "AddBuffTime": "10"}}]}, {"ItemId": "WindHole_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_WindHole,5)", "SpecialParams": {"DamagePower": "0.5", "BreakPower": "0.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_WindHole,6)", "SpecialParams": {"DamagePower": "0.75", "BreakPower": "0.75"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_WindHole,7)", "SpecialParams": {"DamagePower": "1.25", "BreakPower": "1.25"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_WindHole,8)", "SpecialParams": {"DamagePower": "2", "BreakPower": "2"}}]}, {"ItemId": "EarthQuake_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_EarthQuakeWave,0.5)", "SpecialParams": {"DamagePower": "3", "BreakPower": "5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_EarthQuakeWave,0.5)", "SpecialParams": {"DamagePower": "4.5", "BreakPower": "7.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_EarthQuakeWave,0.5)", "SpecialParams": {"DamagePower": "7.5", "BreakPower": "12.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_EarthQuakeWave,0.5)", "SpecialParams": {"DamagePower": "12", "BreakPower": "20"}}]}, {"ItemId": "Berserker_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Berserker,40,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Berserker,60,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Berserker,70,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Berserker,80,15,false)"}]}, {"ItemId": "CritUpItem_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemCritUp,5000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemCritUp,6000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemCritUp,7000,15,false)"}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ItemCritUp,8000,15,false)"}]}, {"ItemId": "RangeThunder_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_RangeThunder,2)", "SpecialParams": {"Num": 2, "CoolDown": "0.2", "DamagePower": "0.5", "BreakPower": "0.2"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_RangeThunder,2)", "SpecialParams": {"Num": 3, "CoolDown": "0.2", "DamagePower": "0.75", "BreakPower": "0.3"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_RangeThunder,2)", "SpecialParams": {"Num": 4, "CoolDown": "0.2", "DamagePower": "1.25", "BreakPower": "0.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.CreateAoeOnRoot(Rogue_Aoe_RangeThunder,2)", "SpecialParams": {"Num": 5, "CoolDown": "0.2", "DamagePower": "2", "BreakPower": "0.8"}}]}, {"ItemId": "Range_Enchant", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(RogueItem_Sword_Effect,1,15,false)", "SpecialParams": {"Scale": "0.75", "DamagePower": "1", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(RogueItem_Sword_Effect,1,15,false)", "SpecialParams": {"Scale": "1", "DamagePower": "1.5", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(RogueItem_Sword_Effect,1,15,false)", "SpecialParams": {"Scale": "1.5", "DamagePower": "2.5", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(RogueItem_Sword_Effect,1,15,false)", "SpecialParams": {"Scale": "2", "DamagePower": "4", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Pierce1_Effect,1,15,false)", "SpecialParams": {"Scale": "0.75", "DamagePower": "1", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Pierce1_Effect,1,15,false)", "SpecialParams": {"Scale": "0.75", "DamagePower": "1.5", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Pierce1_Effect,1,15,false)", "SpecialParams": {"Scale": "0.75", "DamagePower": "2.5", "BreakPower": "0"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_Pierce1_Effect,1,15,false)", "SpecialParams": {"Scale": "0.75", "DamagePower": "4", "BreakPower": "0"}}]}, {"ItemId": "FrozenParry_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 4, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["Counter"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "注释": "触发三次", "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenParry,1,Weapon_rSocket)", "SpecialParams": {"DamagePower": "6", "BreakPower": "6", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["Counter"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "注释": "触发三次", "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenParry,1,Weapon_rSocket)", "SpecialParams": {"DamagePower": "9", "BreakPower": "9", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["Counter"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "注释": "触发三次", "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenParry,1,Weapon_rSocket)", "SpecialParams": {"DamagePower": "15", "BreakPower": "15", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}, {"EffectTags": ["Counter"], "EffectMinLevel": 4, "EffectMaxLevel": 4, "注释": "触发三次", "Effect": "RogueItemUseEffect.CreateAoeAttach(Rogue_Aoe_FrozenParry,1,Weapon_rSocket)", "SpecialParams": {"DamagePower": "24", "BreakPower": "24", "AddBuff": "Standard_PlayFrozen", "AddBuffTime": "2"}}]}], "RogueItem1": [{"ItemId": "ReflectBody_Rogue", "Tags": ["Rogue"], "MaxEffectLevel": 3, "MaxEnergy": 500, "CostEnergy": 500, "Value": 200, "EnergyRecovery": 0, "OnUseEffects": [{"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 1, "EffectMaxLevel": 1, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ReflectBody,1,10,false)", "SpecialParams": {"DamagePower": "5", "BreakPower": "5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 2, "EffectMaxLevel": 2, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ReflectBody,2,10,false)", "SpecialParams": {"DamagePower": "7.5", "BreakPower": "7.5"}}, {"EffectTags": ["<PERSON><PERSON><PERSON>"], "EffectMinLevel": 3, "EffectMaxLevel": 3, "Effect": "RogueItemUseEffect.AddSelfItemBuffNoStack(Rogue_ReflectBody,3,10,false)", "SpecialParams": {"DamagePower": "12.5", "BreakPower": "12.5"}}]}]}