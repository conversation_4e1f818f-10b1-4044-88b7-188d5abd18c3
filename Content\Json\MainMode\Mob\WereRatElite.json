{"Mob": [{"Id": "WereRatElite", "Tag": ["WereRat"], "AI": ["StopAI", "WereRat_ClearRoaredBuff", "WereRat_Debut", "WereRat_RoarBeforeRage", "WereRatAngry", "HappyPickUp<PERSON>heese", "NormalPickUpCheese", "MoveToClosetCheese", "WereRat_Roar", "WereRatCommando_SelectCrazyCombo", "WereRatCommando_SelectCombo", "WereRatCommando_Crawling", "WereRat_Pace", "WereRatTurnToStimulate", "WereRat_DoActionOnMinePoint", "WereRat_MoveToNearestMinePoint", "WereRat_MoveToNextPathNode"], "BpPath": "Core/Characters/WereRatElite/WereRat_Elite", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -50}], "OnBeKilled": ["MobBeKilled.RatManBeKilled()", "DesignerScript/TriggerScript_Country.CheckAllRatsDeathInMission04()"], "Flyable": false, "ExpGiven": 1, "LootPackageId": "WereRat", "MountsTypeRotate": false, "Name": "鼠人精英", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 800, "SightHalfAngleDregee": 135, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 1, "Attack": 5, "Balance": 10, "MoveSpeed": [325, 655, 980], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "WereRatCrazy", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "WereRatElite_AccumulateDamageAndHurt", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [{"Id": "WereRat_Commando_Body_Lack", "Rate": 1}, {"Id": "WereRat_Commando_Body_Full", "Rate": 0.5}, {"Id": "WereRat_Commando_Leg_Lack", "Rate": 1}, {"Id": "WereRat_Commando_Leg_Full", "Rate": 0.5}, {"Id": "WereRat_Commando_Arm_Lack", "Rate": 1}, {"Id": "WereRat_Commando_Arm_Full", "Rate": 0.5}, {"Id": "WereRat_Commando_Head", "Rate": 1}], "Part": [{"Meat": {"Physical": 0.8}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 0.5}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 0.9}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1.0}, "Part": "Tail", "Durability": [1], "CanBeDestroy": false, "Priority": 7, "StableMod": 1.0, "Type": "Meat"}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "", "UnArmed": ""}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/WereRatCommando/Move"]}}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Back", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Front", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Back", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "登场", "Id": "Debut", "Cmds": ["Debut"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Debut_Elite"]}, "InitAction": true}, {"说明": "向前跑（短）", "Id": "Crawling_Short", "Cmds": ["Crawling_Short"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Short"]}, "InitAction": true}, {"说明": "向前跑（长）", "Id": "Crawling_Long", "Cmds": ["Crawling_Long"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Long"]}, "InitAction": true}, {"说明": "挖矿动作（短）", "Id": "Mine_Short", "Cmds": ["Mine_Short"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/WereRatCommando/Action/Mine_Short"]}, "InitAction": true}, {"说明": "挖矿动作（长）", "Id": "Mine_Long", "Cmds": ["Mine_Long"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/WereRatCommando/Action/Mine_Long"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Daze1", "Cmds": ["Daze1"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_1"]}, "InitAction": true}, {"说明": "发呆2", "Id": "Daze2", "Cmds": ["Daze2"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_2"]}, "InitAction": true}, {"说明": "发呆3", "Id": "Daze3", "Cmds": ["Daze3"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Daze_3"]}, "InitAction": true}, {"说明": "转向刺激源动作", "Id": "TurnToStimulate", "Cmds": ["TurnToStimulate"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LookAround"]}, "InitAction": true}, {"说明": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Roar_Giant"]}, "InitAction": true}, {"说明": "吼叫2", "Id": "Roar_C1", "Cmds": ["Roar_C1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Roar_Giant"]}, "InitAction": true}, {"说明": "吼叫3", "Id": "Roar_C2", "Cmds": ["Roar_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Roar_Giant"]}, "InitAction": true}, {"说明": "进入亢奋的吼叫", "Id": "RageRoar", "Cmds": ["RageRoar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/WereRatCommando/Action/RageRoar"]}, "InitAction": true}, {"说明": "捡起奶酪", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Cmds": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/PickUpCheese"]}, "InitAction": true}, {"说明": "高兴的捡起奶酪（待替换动画）", "Id": "HappyPickUp<PERSON>heese", "Cmds": ["HappyPickUp<PERSON>heese"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Action/HappyPickUpCheese"]}, "InitAction": true}, {"说明": "抓击1", "Id": "Scratch01", "Cmds": ["Scratch01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/WereRatCommando/Attack/Attack01"]}, "InitAction": true}, {"说明": "抓击2", "Id": "Scratch02", "Cmds": ["Scratch02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/WereRatCommando/Attack/Attack02"]}, "InitAction": true}, {"说明": "抓击3", "Id": "Scratch03", "Cmds": ["Scratch03"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/WereRatCommando/Attack/Attack03"]}, "InitAction": true}, {"说明": "Attack_S1", "Id": "Attack_S1", "Cmds": ["Attack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S1"]}, "InitAction": true}, {"说明": "Attack_S1_C2", "Id": "Attack_S1_C2", "Cmds": ["Attack_S1_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S1_c2"]}, "InitAction": true}, {"说明": "Attack_S1_C3", "Id": "Attack_S1_C3", "Cmds": ["Attack_S1_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S1_c3"]}, "InitAction": true}, {"说明": "Attack_S2", "Id": "Attack_S2", "Cmds": ["Attack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S2"]}, "InitAction": true}, {"说明": "Attack_S2_C2", "Id": "Attack_S2_C2", "Cmds": ["Attack_S2_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S2_c2"]}, "InitAction": true}, {"说明": "Attack_S2_C3", "Id": "Attack_S2_C3", "Cmds": ["Attack_S2_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S2_c3"]}, "InitAction": true}, {"说明": "Attack_S3", "Id": "Attack_S3", "Cmds": ["Attack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_S3"]}, "InitAction": true}, {"说明": "Attack_M1", "Id": "Attack_M1", "Cmds": ["Attack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M1"]}, "InitAction": true}, {"说明": "Attack_M1_C2", "Id": "Attack_M1_C2", "Cmds": ["Attack_M1_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M1_c2"]}, "InitAction": true}, {"说明": "Attack_M1_C3", "Id": "Attack_M1_C3", "Cmds": ["Attack_M1_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M1_c3"]}, "InitAction": true}, {"说明": "Attack_M2", "Id": "Attack_M2", "Cmds": ["Attack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M2"]}, "InitAction": true}, {"说明": "Attack_M2_C2", "Id": "Attack_M2_C2", "Cmds": ["Attack_M2_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M2_c2"]}, "InitAction": true}, {"说明": "Attack_M2_C3", "Id": "Attack_M2_C3", "Cmds": ["Attack_M2_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_M2_c3"]}, "InitAction": true}, {"说明": "Attack_L1", "Id": "Attack_L1", "Cmds": ["Attack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_L1"]}, "InitAction": true}, {"说明": "Attack_L1_C2", "Id": "Attack_L1_C2", "Cmds": ["Attack_L1_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatCommando/Attack_Giant/NormalAttack_L1_c2"]}, "InitAction": true}, {"说明": "Attack_L1_C3", "Id": "Attack_L1_C3", "Cmds": ["Attack_L1_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatCommando/Attack_Giant/NormalAttack_L1_c3"]}, "InitAction": true}, {"说明": "Attack_L2", "Id": "Attack_L2", "Cmds": ["Attack_L2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRatCommando/Attack_Giant/NormalAttack_L2"]}, "InitAction": true}, {"说明": "Attack_L2_C2", "Id": "Attack_L2_C2", "Cmds": ["Attack_L2_C2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatCommando/Attack_Giant/NormalAttack_L2_c2"]}, "InitAction": true}, {"说明": "Attack_L2_C3", "Id": "Attack_L2_C3", "Cmds": ["Attack_L2_C3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatCommando/Attack_Giant/NormalAttack_L2_c3"]}, "InitAction": true}, {"说明": "向左突击", "Id": "LeftSpinAttack", "Cmds": ["LeftSpinAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/WereRatCommando/Attack/AttackLeftLong"]}, "InitAction": true}, {"说明": "向右突击", "Id": "RightSpinAttack", "Cmds": ["RightSpinAttack"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/WereRatCommando/Attack/AttackRightLong"]}, "InitAction": true}, {"说明": "向前突击", "Id": "ForwardStrike", "Cmds": ["ForwardStrike"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/WereRatCommando/Attack/ForwardStrike"]}, "InitAction": true}, {"说明": "亢奋向前突击", "Id": "RageForwardStrike", "Cmds": ["RageForwardStrike"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/<PERSON><PERSON>/Monster/WereRatCommando/Attack/RageForwardStrike"]}, "InitAction": true}, {"说明": "左踱步", "Id": "LeftPace", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LeftPace"]}, "InitAction": true}, {"说明": "右踱步", "Id": "RightPace", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/RightPace"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_JumpStep_Back"]}, "InitAction": true}, {"说明": "向左闪避", "Id": "Dodge_Left", "Cmds": ["Dodge_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_JumpStep_Left"]}, "InitAction": true}, {"说明": "向右闪避", "Id": "Dodge_Right", "Cmds": ["Dodge_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_JumpStep_Right"]}, "InitAction": true}, {"说明": "生气（待替换）", "Id": "WereRatAngry", "Cmds": ["WereRatAngry"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Angry"]}}, {"说明": "发呆：喘气", "Id": "WereRat_DeepBreathe", "Cmds": ["WereRat_DeepBreathe"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/DeepBreathe"]}}, {"说明": "发呆：害怕", "Id": "WereRat_Fear", "Cmds": ["WereRat_Fear"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Fear"]}}, {"说明": "发呆：累了", "Id": "WereRat_Tired", "Cmds": ["WereRat_Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Tired"]}}]}], "Buff": [{"Id": "WereRatCrazy", "Tag": ["Buff", "WereRat"], "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(CrazyBody,Temp/Effect/ParagonZ/FX_Rampage/Particles/Abilities/JungleKing/FX/P_Rampage_Passive_Attack_R,Root)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(CrazyBody)"]}, {"Id": "WereRatElite_AccumulateDamageAndHurt", "Tag": ["WereRat"], "Priority": 0, "MaxStack": 999999, "OnBeHurt": ["BuffUtils.AccumulateDamageAndHurt(150,Hurt)"]}]}