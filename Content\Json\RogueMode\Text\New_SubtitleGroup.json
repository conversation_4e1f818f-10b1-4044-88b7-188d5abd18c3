{"FSubtitleGroup": [{"_____________________________字幕__": "________敬请期待________________"}, {"SubtitleGroupId": "PleaseStayTuned", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "PleaseStayTuned", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "字幕组名", "SubtitleArray": [{"SpeakerKey": "说话者的名字Key (根据Chinese.json里的来)", "ContentKey": "说话内容Key (根据Chinese.json里的来)", "AudioId": "音频key (根据VoiceAudio.json里的来)", "AudioPlayType": "音频播放类型 共三种 PlaySound2D、PlaySoundAtLocation、PlaySoundAttachToTarget", "PlayAudioLocation": "X=0,Y=0,Z=0、播放位置 播放类型为 PlaySoundAtLocation 时使用", "AudioAttachToTargetIndex": "添加到目标对象数组的index", "IntervalTime": "0、每句话的间隔时间、这个时间是在每句话开头计算的"}]}, {"_____________________________字幕__": "_________圣杯收集__________________"}, {"SubtitleGroupId": "HaveGrailStone1", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "HaveGrailStone2", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "HaveGrailStone3", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "HaveGrailStone4", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone4", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "HaveGrailStone5", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone5", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "HaveGrailStone6", "SubtitleArray": [{"SpeakerKey": "", "ContentKey": "HaveGrailStone6", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "_________开场、新手教程__________________"}, {"_____________________________字幕__": "_________旁白__________________"}, {"SubtitleGroupId": "NoviceTutorial_Narrator", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "NoviceTutorial_Narrator", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "_________过场动画__________________"}, {"SubtitleGroupId": "CutoffAnimation", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "CutoffAnimation1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "CutoffAnimation2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "CutoffAnimation3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________过长动画播放完之后__________________"}, {"SubtitleGroupId": "AfterCutoffAnimation", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "AfterCutoffAnimation", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________开始第一个新手教程时__________________"}, {"SubtitleGroupId": "FirstNoviceTutorialStart", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "FirstNoviceTutorialStart", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________结束第一个新手教程时__________________"}, {"SubtitleGroupId": "FirstNoviceTutorialEnd", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "FirstNoviceTutorialEnd", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________开始第二个新手教程时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart1", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________拿到第一个圣遗物时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart2", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart4", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________拿到第二个圣遗物时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart3", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart5", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________如果第一次杀完没有完成新手教程的话__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart4", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart6", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________使用觉醒技时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart5", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart7", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________使用法器时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialStart6", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialStart8", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________完成第二个新手教程时__________________"}, {"SubtitleGroupId": "SecondNoviceTutorialEnd", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "SecondNoviceTutorialEnd1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________使用传送门时__________________"}, {"SubtitleGroupId": "WhenUseTeleportationGate", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "WhenUseTeleportationGate", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________到达无相之厅__________________"}, {"SubtitleGroupId": "WhenArriveFormlessFrum", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON>_<PERSON><PERSON><PERSON>", "ContentKey": "WhenArriveFormlessFrum", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________商店__________________"}, {"_____________________________字幕__": "________交互__________________"}, {"SubtitleGroupId": "WhenStepInShop1", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenStepInShop1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "WhenStepInShop2", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenStepInShop2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________购买圣遗物时__________________"}, {"SubtitleGroupId": "WhenShopRelic1", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenShopRelic1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"SubtitleGroupId": "WhenShopRelic2", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenShopRelic2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________购买法器时__________________"}, {"SubtitleGroupId": "WhenShopArtifact", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenShopArtifact", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________购买苹果时__________________"}, {"SubtitleGroupId": "WhenShopApple", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "WhenShopApple", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}]}, {"_____________________________字幕__": "________死骸骑士boss__________________"}, {"_____________________________字幕__": "________一周目第十关过场开始_________________"}, {"SubtitleGroupId": "FirstRoundLevel10Start", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10Start1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10Start2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10Start3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "2"}]}, {"_____________________________字幕__": "________一周目第十关过场结束_________________"}, {"SubtitleGroupId": "FirstRoundLevel10End", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10End1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10End2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FirstRoundLevel10End3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________二周目第十关过场开始_________________"}, {"SubtitleGroupId": "SecondRoundLevel10Start", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10Start1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10Start2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10Start3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________二周目第十关过场结束_________________"}, {"SubtitleGroupId": "SecondRoundLevel10End", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10End1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10End2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel10End3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________二周目第三十一关过场开始_________________"}, {"SubtitleGroupId": "SecondRoundLevel31Start", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31Start1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31Start2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31Start3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________二周目第三十一关过场结束_________________"}, {"SubtitleGroupId": "SecondRoundLevel31End", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31End1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31End2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "SecondRoundLevel31End3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________集齐龙珠后，第31关过场动画开始_________________"}, {"SubtitleGroupId": "FinalRoundLevel31Start", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31Start1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31Start2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31Start3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________集齐龙珠后，第31关过场动画结束_________________"}, {"SubtitleGroupId": "FinalRoundLevel31End", "SubtitleArray": [{"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31End1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31End2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "Rogue_Death_Lord", "ContentKey": "FinalRoundLevel31End3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}, {"_____________________________字幕__": "________集齐龙珠后，第31关过场动画_古神________________"}, {"SubtitleGroupId": "FinalRoundAdministrator", "SubtitleArray": [{"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "FinalRoundAdministrator1", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "0"}, {"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "FinalRoundAdministrator2", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}, {"SpeakerKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ContentKey": "FinalRoundAdministrator3", "AudioId": "", "AudioPlayType": "PlaySound2D", "PlayAudioLocation": "X=0,Y=0,Z=0", "AudioAttachToTargetIndex": "0", "IntervalTime": "1"}]}]}