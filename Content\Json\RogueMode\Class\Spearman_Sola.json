{"Class": [{"说明": "先锋-索拉-Sola", "Id": "<PERSON><PERSON><PERSON>_<PERSON>a", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "BladeDancer", "<PERSON><PERSON><PERSON>", "Warrior_Elf"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Sola", "StateActions": {"Ground": {"Armed": "Spear_Move", "Unarmed": "Spear_Unarmed_Move"}, "Flying": {"Armed": "Spear_Move", "Unarmed": "Spear_Unarmed_Move"}, "Falling": {"Armed": "Spear_Fall", "Unarmed": "Spear_Unarmed_Fall"}, "Attached": {"Armed": "Spear_Ride", "Unarmed": "Spear_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "S<PERSON>_<PERSON>", "UnArmed": "S<PERSON>_<PERSON>"}, "Blow": {"Armed": "<PERSON><PERSON>_<PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON>"}, "Frozen": {"Armed": "S<PERSON>_Frozen", "UnArmed": "S<PERSON>_Frozen"}, "Bounced": {"Armed": "<PERSON><PERSON>_<PERSON>ced", "UnArmed": "<PERSON><PERSON>_<PERSON>ced"}, "Dead": {"Armed": "Spear_Dead", "UnArmed": "Spear_Dead"}, "Landing": {"Armed": "S<PERSON>_JustFall", "UnArmed": "S<PERSON>_Unarmed_JustFall"}, "SecondWind": {"Armed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>"}, "GetUp": {"Armed": "Spear_RevivedOnSecWind", "UnArmed": "Spear_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 890], "BeStrikeRate": 1.0, "CriticalChance": 0.1, "CriticalRate": 1.5, "AirDodgePoint": 1}, "WeaponType": "PoleArm", "DefaultWeapons": ["Iron_Spear"], "ActionOnChangeTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________长枪徒手基础动作________________________________"}, {"说明": "长枪徒手走路站立", "Id": "Spear_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge", "<PERSON><PERSON><PERSON>_Aim", "S<PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive", "Spear_DrawAttack", "<PERSON><PERSON>_<PERSON>ck"], "1": ["S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge", "<PERSON><PERSON><PERSON>_Aim", "S<PERSON>_DrawWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/UnarmedMove", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/BS_Throw_Aim"]}}, {"说明": "长枪徒手起跳", "Id": "S<PERSON>_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "S<PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["Spear_Air_DrawAttack", "Spear_AirSkillAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "长枪徒手翻滚", "Id": "<PERSON><PERSON>_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["Spear_Unarmed_Move", "S<PERSON>_Unarmed_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "长枪徒手下落", "Id": "Spear_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/UnarmedFall"]}, "Priority": 1}, {"说明": "长枪徒手下落着地", "Id": "S<PERSON>_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "S<PERSON>_Unarmed_Jump", "Unarmed_<PERSON><PERSON>_Dodge", "Interactive", "S<PERSON>_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/UnarmedJustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "长枪收刀", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/SheathWeapon"]}}, {"说明": "长枪拔刀", "Id": "DrawSpear", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "S<PERSON>_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/DrawWeapon"]}}, {"Line": "_______________________________长枪(LevelSquencer)动作________________________________"}, {"说明": "Rogue大厅开场动作", "Id": "Rogue_Hall_Begin", "Cmds": ["Rogue_Hall_Begin"], "Tags": [{"Tag": "Rogue_Hall_Begin", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_Begin_Hall"]}, "InitAction": true}, {"说明": "Rogue大厅重生动作", "Id": "<PERSON>_<PERSON>_Respawn", "Cmds": ["<PERSON>_<PERSON>_Respawn"], "Tags": [{"Tag": "<PERSON>_<PERSON>_Respawn", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "S<PERSON>_Unarmed_Jump", "<PERSON><PERSON>_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_Begin_DeathRespawn_Hall"]}, "InitAction": true}, {"说明": "Rogue房间开始动作", "Id": "Rogue_Room_Start", "Cmds": ["Rogue_Room_Start"], "Tags": [{"Tag": "Rogue_Room_Start", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_Begin_Dungeon"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_01", "Cmds": ["Rogue_SecondRoundEndSeq_01"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Sequence"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_SecondRoundEndSeq_01"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_02", "Cmds": ["Rogue_SecondRoundEndSeq_02"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_SecondRoundEndSeq_02"]}, "InitAction": true}, {"说明": "Rogue31关最终结束动画", "Id": "Rogue_FinalSeq", "Cmds": ["Rogue_FinalSeq"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Rogue_FinalSeq"]}, "InitAction": true}, {"Line": "_______________________________长枪(持武器)基础动作________________________________"}, {"Id": "Spear_Move", "Cmds": ["Spear_Move"], "Tags": [{"Tag": "Spear_Move", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"], "1": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/Move_AimState"]}}, {"Id": "S<PERSON>_<PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["<PERSON><PERSON>_<PERSON>"], "2": ["Spear_QS_B"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>_<PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_QS_B"], "1": ["Spear_QS_F"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Blow_Front"]}}, {"Id": "Spear_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Dead"]}, "InitAction": true}, {"Id": "Spear_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "Spear_Jump", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}, {"Tag": "S<PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "Spear_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["Spear_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/Vanguard/Fall"]}, "Priority": 1}, {"Id": "S<PERSON>_JustFall", "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "Interactive", "S<PERSON>_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/JustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Movement/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Move", "Spear_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>_<PERSON>", "Cmds": ["Dodge"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>_<PERSON>", "From": 0}], "BeCancelledTags": {"0": ["Spear_Move", "Spear_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "S<PERSON>_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "S<PERSON>_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Step_F"]}, "Cost": {"SP": 0}}, {"Id": "S<PERSON>_Air_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "S<PERSON>_Air_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_<PERSON>_Dodge_Step_Second"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Air_Dodge_Dash"]}, "InitAction": true}, {"Id": "S<PERSON>_<PERSON>_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "S<PERSON>_<PERSON>_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Air_Dodge_Dash_Second"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "S<PERSON>_<PERSON>_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "S<PERSON>_<PERSON>_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Air_Dodge_Dash"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "<PERSON><PERSON>_<PERSON>_Dodge_Success", "Cmds": ["_"], "Tags": [{"Tag": "<PERSON><PERSON>_<PERSON>_Dodge_Success", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_Dodge_Step", "S<PERSON>_<PERSON>_Dodge_CounterAtk", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/Dodge_F_Just_Success"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "S<PERSON>_<PERSON>_<PERSON>_Success_CounterAtk", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "S<PERSON>_<PERSON>_Dodge_CounterAtk", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "S<PERSON>_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_JustDodge_CounterAtk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "Spear_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Spear_QS_B", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "<PERSON><PERSON>_<PERSON>", "Spear_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身动作前翻", "Id": "Spear_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Spear_QS_F", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "<PERSON><PERSON>_<PERSON>", "Spear_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/SecondWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "Spear_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 999, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/RevivedOnSecondWind"]}}, {"Line": "_______________________________长枪_防御_动作_______________________________"}, {"Id": "S<PERSON>man_Defense", "Cmds": ["Aim"], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}, {"Tag": "Spear_Defense", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Defense"]}}, {"说明": "防御成功", "Id": "<PERSON><PERSON><PERSON>_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "<PERSON><PERSON>_<PERSON>", "Spear_Defense", "Unarm_UseItem"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Defense_Success"]}}, {"Line": "_______________________________长枪受击(特殊)动作________________________________"}, {"Id": "S<PERSON>_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Hit/Hurt_Frozen"]}}, {"Line": "_______________________________长枪特殊动作________________________________"}, {"Line": "_______________________________长枪基础(附加)动作________________________________"}, {"说明": "长枪弹刀动作", "Id": "<PERSON><PERSON>_<PERSON>ced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Spear_Jump", "<PERSON><PERSON>_<PERSON>", "<PERSON><PERSON>_Aim", "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Bounced"]}}, {"说明": "瞄准动作", "Id": "<PERSON><PERSON>_Aim", "Cmds": [], "Tags": [{"Tag": "<PERSON><PERSON>_Aim", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>", "S<PERSON>_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/Spearman_Aim"]}}, {"Line": "_______________________________长枪战斗触发动作________________________________"}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_HitJump"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "<PERSON><PERSON><PERSON>_DashSpike_HitJump2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_HitJump2"]}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapDownSlash"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_RiseSlash2_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": []}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_RiseSlash2"]}}, {"Line": "_______________________________法器反击_______________________________"}, {"说明": "法器反击成功了就自动变成这个了(仅用于justblock)", "Id": "FrozenParry_JustBlock", "Cmds": [], "Tags": [{"Tag": "FrozenParry_JustBlock", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/MagicItem/MagicItem_Parry_CounterAtk"]}}], "RogueBattleActions": [{"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["Dodge"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Action/PickUp"]}}, {"Line": "_______________________________长枪_普通攻击_动作_______________________________"}, {"Line": "_______________________________长枪_普攻_地面_______________________________"}, {"Id": "Spearman_LAttack01", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_LAttack1", "From": 0}, {"Tag": "Spear_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_LAttack2", "Spear_LAttack2_T1"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["Spear_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash0"]}, "InitAction": true}, {"Id": "Spearman_LAttack02", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash1"]}, "InitAction": true}, {"Id": "Spearman_LAttack03", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack1"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash2"]}, "InitAction": true}, {"说明": "长枪分支普通攻击1", "Id": "Spearman_AttackB1", "Cmds": [], "Tags": [{"Tag": "Spear_AttackB1", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_AttackB2"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SlashB1"]}, "InitAction": true}, {"说明": "长枪分支普通攻击2", "Id": "Spearman_AttackB2", "Cmds": [], "Tags": [{"Tag": "Spear_AttackB2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_AttackB3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SlashB2"]}, "InitAction": true}, {"说明": "长枪分支普通攻击3", "Id": "Spearman_AttackB3", "Cmds": [], "Tags": [{"Tag": "Spear_AttackB3", "From": 0.0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SlashB3"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击2", "Id": "<PERSON><PERSON><PERSON>_LAttack02_T1", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack2_T1", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack3_T1"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash1_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击3", "Id": "<PERSON><PERSON><PERSON>_LAttack03_T1", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack3_T1", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_LAttack4_T1"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash2_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击4", "Id": "<PERSON><PERSON><PERSON>_LAttack04_T1", "Cmds": [], "Tags": [{"Tag": "Spear_LAttack4_T1", "From": 0.0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_Slash3_T1"]}, "InitAction": true}, {"Line": "_______________________________长枪_普攻_空中_______________________________"}, {"Id": "Spearman_AirSweapAttack1", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSweapAttack1", "From": 0}, {"Tag": "Spear_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirSweapAttack2"], "1": ["_"], "2": ["Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_SweapSlash0"]}, "InitAction": true}, {"Id": "Spearman_AirSweapAttack2", "Cmds": [], "Tags": [{"Tag": "Spear_AirSweapAttack2", "From": 0}], "BeCancelledTags": {"0": ["Spear_AirSweapAttack1"], "1": ["_"], "2": ["Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_SweapSlash1"]}, "InitAction": true}, {"Line": "_______________________________长枪_技能A(横扫4连)_地面Action2_______________________________"}, {"Id": "Spearman_SweapAttack1", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["Spear_SpAttack2"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SweapSlash0"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack2", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_SpAttack3"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SweapSlash1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack3", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_SpAttack4"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SweapSlash2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_SweapAttack4", "Cmds": [], "Tags": [{"Tag": "Spear_SpAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["_"], "1": ["S<PERSON>_Dodge_Step"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_SweapSlash3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_ConsecutiveSpike", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_ConsecutiveSpike"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "Spearman_UpSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem", "S<PERSON>_InitAttack"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_UpSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________长枪_技能A(下落)_空中Action2_______________________________"}, {"Id": "Spearman_AirDownSpikeAttack", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSpike_FallDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON><PERSON>_AirDownSpikeAttack_B", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSpike_FallDown_B"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________长枪_技能B(突刺、后续技能)_地面Action3_______________________________"}, {"Id": "<PERSON><PERSON><PERSON>_DashSpike_Add_Rise", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSpike"]}}, {"Id": "<PERSON><PERSON><PERSON>_DashSpike_Add_Spike", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSpike_2"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_DashSpike_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSpike_Combo"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_RiseSlash_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_RiseSlash"]}}, {"说明": "冲刺出去合理命中会有后续Combo，可接连段攻击、升龙攻击", "Id": "<PERSON><PERSON><PERSON>_RiseSlash2_Combo", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_RiseSlash2"]}}, {"Line": "_______________________________长枪_技能B(突刺、后续技能)_空中Action3_______________________________"}, {"Id": "<PERSON><PERSON><PERSON>_AirDashSpike_ForwardDown", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSpike_ForwardDown"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "冲刺出去合理命中会有后续Combo，可接空中后跳", "Id": "<PERSON><PERSON><PERSON>_DashSpike_HitJump", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_HitJump"]}}, {"Line": "_______________________________长枪_技能C(位移、后续技能)_地面Action4_______________________________"}, {"Id": "Spearman_DashThrust", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashThrust"]}, "Cost": {"MP": 0}}, {"Id": "S<PERSON><PERSON>_DashSweapSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSweapSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON><PERSON>_DashSweapSlash_RiseSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSweapSlash_RiseSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON><PERSON>_DashSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/G_DashSweapSlash_HitJump"]}, "Cost": {"MP": 0}}, {"Id": "S<PERSON><PERSON>_BackJumpSweapSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_BackJumpSweapSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON><PERSON>_BackJumpSweapSlash_DownSlash", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_BackJumpSweapSlash_DownSlash"]}, "Cost": {"MP": 0}}, {"Id": "<PERSON><PERSON><PERSON>_BackJumpSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "<PERSON><PERSON>_<PERSON>ck", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_BackJumpSweapSlash_HitJump"]}, "Cost": {"MP": 0}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "<PERSON><PERSON><PERSON>_DashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapDownSlash"]}}, {"说明": "地面前冲横扫出去合理命中会有后续Combo，可接空中弹跳", "Id": "<PERSON><PERSON><PERSON>_DashSweap_HitJump2", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["Spear_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_HitJump2"]}}, {"Line": "_______________________________长枪_技能C(位移、后续技能)_空中Action4_______________________________"}, {"Id": "Spearman_AirDashSweapSlash", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "S<PERSON><PERSON>_AirDashSweapSlash_DownSlash", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapSlash_DownSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "S<PERSON><PERSON>_AirDashSweapSlash_HitJump", "Cmds": [], "Tags": [{"Tag": "Spear_AirInitAttack", "From": 0}, {"Tag": "Spear_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapSlash_HitJump"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "空中前冲横扫出去合理命中会有后续Combo，可接空中下劈", "Id": "<PERSON><PERSON><PERSON>_AirDashSweap_DownSlash", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Spear_AirInitAttack", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["_"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/A_DashSweapDownSlash"]}}, {"Line": "_______________________________长枪_动作连招式_轻重击_______________________________"}, {"说明": "长枪地面普攻1", "Id": "S<PERSON>man_Attack_GN1", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN1", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN2", "Spear_Attack_GH2"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["Spear_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN1"]}, "InitAction": true}, {"说明": "长枪地面普攻2", "Id": "S<PERSON><PERSON>_Attack_GN2", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN2", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN3", "Spear_Attack_GH3"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN2"]}, "InitAction": true}, {"说明": "长枪地面普攻3", "Id": "S<PERSON><PERSON>_Attack_GN3", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN3", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN4", "Spear_Attack_GH4"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN3"]}, "InitAction": true}, {"说明": "长枪地面普攻4", "Id": "S<PERSON><PERSON>_Attack_GN4", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN4", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN5", "Spear_Attack_GH5"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN4"]}, "InitAction": true}, {"说明": "长枪地面普攻5", "Id": "S<PERSON><PERSON>_Attack_GN5", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN5", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN6", "Spear_Attack_GH6"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN5"]}, "InitAction": true}, {"说明": "长枪地面普攻6", "Id": "S<PERSON><PERSON>_Attack_GN6", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN6", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN7", "Spear_Attack_GH7"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN6"]}, "InitAction": true}, {"说明": "长枪地面普攻7", "Id": "S<PERSON><PERSON>_Attack_GN7", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GN7", "From": 0.0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GN7"]}, "InitAction": true}, {"说明": "长枪地面重攻1", "Id": "<PERSON><PERSON><PERSON>_Attack_GH1", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH1", "From": 0}, {"Tag": "S<PERSON>_InitAttack", "From": 0}, {"Tag": "Spear_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_AN1", "Spear_Attack_AH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻2", "Id": "S<PERSON><PERSON>_Attack_GH2", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH2", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻3", "Id": "S<PERSON><PERSON>_Attack_GH3", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH3", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻4", "Id": "S<PERSON><PERSON>_Attack_GH4", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH4", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_AN1", "Spear_Attack_AH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH4"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻5", "Id": "S<PERSON><PERSON>_Attack_GH5", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH5", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH5"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻6", "Id": "S<PERSON><PERSON>_Attack_GH6", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH6", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH6"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面重攻7", "Id": "S<PERSON><PERSON>_Attack_GH7", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_GN1", "Spear_Attack_GH1"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_GH7"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面空中普攻1", "Id": "S<PERSON><PERSON>_Attack_AN1", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_AN1", "From": 0}, {"Tag": "Spear_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_AN2", "Spear_Attack_AH1", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_AN1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面空中普攻2", "Id": "S<PERSON><PERSON>_Attack_AN2", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_AN2", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_AN3", "Spear_Attack_AH1", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_AN2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面空中普攻3", "Id": "S<PERSON><PERSON>_Attack_AN3", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_AN3", "From": 0}], "BeCancelledTags": {"0": ["Spear_Attack_AH1", "Spear_AirSkillAttack", "S<PERSON>_Air_Dodge_Step"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_AN3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "长枪地面空中重攻1", "Id": "S<PERSON><PERSON>_Attack_AH1", "Cmds": [], "Tags": [{"Tag": "Spear_Attack_AH1", "From": 0}, {"Tag": "Spear_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["S<PERSON>_InitAttack", "Unarm_UseItem"], "1": ["S<PERSON>_Dodge_Step", "Spear_Defense"], "2": ["<PERSON><PERSON>_<PERSON>ck"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/Vanguard/Battle/Attack_AH1"]}, "InitAction": true, "Cost": {"MP": 0}}]}], "Buff": [], "Aoe": []}