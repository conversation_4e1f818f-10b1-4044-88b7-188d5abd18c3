{"Mob": [{"Id": "<PERSON>_<PERSON><PERSON>_Golem_Small", "Tag": [""], "AI": ["StopAIBuff", "<PERSON><PERSON>_Golem_ChangeStage", "Lava_Golem_MoveToSetPoint", "Lava_Golem_NormalAttack_S5", "<PERSON><PERSON>_<PERSON><PERSON>_RageAttack_S5", "RogueMob_BasicBattle", "Lava_Golem_MoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Golem/Lava_Golem/Lava_Golem_Small", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Lava_Golem", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 15, "PAtk": 18, "Balance": 10, "MoveSpeed": [200, 400, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "Rogue_Boss_FirstStage", "Stack": 50, "Time": 0, "Infinity": true}, {"Id": "FireDamageResistance", "Stack": 7000, "Time": 0, "Infinity": true}, {"Id": "ImmuneLava", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "SpeedDownResistance", "Stack": 6000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 9000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Lava_Golem/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Lava_Golem/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Hit/Break_Down"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "燃烧践踏", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "熔岩地震", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "爆射炎弹", "Id": "NormalAttack_S4", "Cmds": ["NormalAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S4"]}, "InitAction": true}, {"说明": "烈焰扫射", "Id": "NormalAttack_S5", "Cmds": ["NormalAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S5"]}, "InitAction": true}, {"说明": "近战攻击EX", "Id": "RageAttack_S1", "Cmds": ["RageAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S1"]}, "InitAction": true}, {"说明": "燃烧践踏EX", "Id": "RageAttack_S2", "Cmds": ["RageAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S2"]}, "InitAction": true}, {"说明": "熔岩地震EX", "Id": "RageAttack_S3", "Cmds": ["RageAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S3"]}, "InitAction": true}, {"说明": "熔岩地震EX 2", "Id": "RageAttack_S3_2", "Cmds": ["RageAttack_S3_2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S3_2"]}, "InitAction": true}, {"说明": "爆射炎弹EX", "Id": "RageAttack_S4", "Cmds": ["RageAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S4"]}, "InitAction": true}, {"说明": "烈焰扫射EX", "Id": "RageAttack_S5", "Cmds": ["RageAttack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S5"]}, "InitAction": true}, {"说明": "火山陨落", "Id": "RageAttack_S6", "Cmds": ["RageAttack_S6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S6"]}, "InitAction": true}, {"说明": "熔岩咆哮", "Id": "RageState", "Cmds": ["RageState"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageState"]}, "InitAction": true}, {"说明": "熔岩咆哮", "Id": "RageAttack_S7", "Cmds": ["RageAttack_S7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S7"]}, "InitAction": true}, {"说明": "位移到指定位置的火山陨落", "Id": "Action_MoveToSetPoint", "Cmds": ["Action_MoveToSetPoint"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Action_MoveToSetPoint"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Action_Stare01"]}, "InitAction": true}, {"说明": "快速左转90", "Id": "Turn_Fast_Left_90", "Cmds": ["Turn_Fast_Left_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Turn_Fast_Left_90"]}, "InitAction": true}, {"说明": "快速左转180", "Id": "Turn_Fast_Left_180", "Cmds": ["Turn_Fast_Left_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Turn_Fast_Left_180"]}, "InitAction": true}, {"说明": "快速右转90", "Id": "Turn_Fast_Right_90", "Cmds": ["Turn_Fast_Right_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Turn_Fast_Right_90"]}, "InitAction": true}, {"说明": "快速右转180", "Id": "Turn_Fast_Right_180", "Cmds": ["ActionTurn_Fast_Right_180_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Turn_Fast_Right_180"]}, "InitAction": true}, {"说明": "左转90", "Id": "TurnLeft_90", "Cmds": ["TurnLeft_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/TurnLeft_90"]}, "InitAction": true}, {"说明": "左转180", "Id": "TurnLeft_180", "Cmds": ["TurnLeft_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/TurnLeft_180"]}, "InitAction": true}, {"说明": "右转90", "Id": "TurnRight_90", "Cmds": ["TurnRight_90"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/TurnRight_90"]}, "InitAction": true}, {"说明": "右转180", "Id": "TurnRight_180", "Cmds": ["TurnRight_180"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/TurnRight_180"]}, "InitAction": true}, {"说明": "往前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Action/Walk_Front"]}, "InitAction": true}, {"说明": "岩浆跃进", "Id": "NormalAttack_S8", "Cmds": ["NormalAttack_S8"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/NormalAttack_S8"]}, "InitAction": true}, {"说明": "岩浆跃进EX", "Id": "RageAttack_S8", "Cmds": ["RageAttack_S8"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Lava_Golem/Battle/RageAttack_S8"]}, "InitAction": true}]}]}