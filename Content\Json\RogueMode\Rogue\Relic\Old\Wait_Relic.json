{"RelicInfo1": [{"分割": "==========================================第一次小更新=========================================="}, {"分割": "==========================================自动发射类遗物可叠加上限为3=========================================="}, {"分割": "==========================================一些上位替代（不确定）=========================================="}, {"分割": "==========================================一特效不好看========================================="}, {"id": "ColdOfIlm_18", "描述": "冰受击aoe", "Desc": "ColdOfIlm_18_Desc", "RelicType": "Attack", "RecordId": "22", "Tags": ["Group_Ilm"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Ice", "I07"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["Hurt_CreateIceShield"]}, {"id": "LightOfAzem_18", "描述": "光受击aoe", "RelicType": "Attack", "RecordId": "67", "Desc": "LightOfAzem_18_Desc", "Tags": ["Group_Azem"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Light", "L07"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Hurt_LightAoe"]}, {"id": "CorruptionOfErminda_18", "描述": "击杀暗球", "Desc": "CorruptionOfErminda_18_Desc", "RelicType": "Attack", "RecordId": "81", "Tags": ["Group_Eminendanis"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Magic", "D07"], "Value": 240, "RelicRarity": "Rarely", "EffectBuff": ["<PERSON>_<PERSON><PERSON><PERSON>"]}, {"id": "ColdOfIlm_14", "描述": "对有debuff的角色伤害提高15%", "Desc": "ColdOfIlm_14_Desc", "Tags": ["Group_Ilm", "Word_ElementalDebuff"], "MaxNum": 3, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Ice", "Rogue_IlmEmblem", "Rogue_2"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_DebuffCarrierDamageUp(15)"]}, {"id": "LuanaGodLife4", "描述": "最大生命提升50%，受击伤害提升50%", "Desc": "LuanaGodLife4_Desc", "RelicType": "Survive", "RecordId": "165", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Life"], "IconPath": ["Rogue_None", "H10"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_MaxHealthPercentUp(5000)", "Rogue_HurtUp(50)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(Hp,Rogue_MaxHealthPercentUp,1,Survive)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1)"]}, {"分割": "==========================================大剑相关DLC=========================================="}, {"分割": "==========================================大剑相关遗物=========================================="}, {"id": "BurnOfIgni_8", "描述": "火蓄力", "Desc": "BurnOfIgni_8_Desc", "Tags": ["Group_Igni", "BattleState_Power", "Word_RogueBurning", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Fire", "Rogue_IgniRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_BeHurtCastBurn"]}, {"id": "BurnOfIgni_9", "描述": "火蓄力满", "Desc": "BurnOfIgni_9_Desc", "Tags": ["Group_Igni", "BattleState_Power", "Word_RogueBrust", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Fire", "Rogue_IgniRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_FireBrustHit"]}, {"id": "ColdOfIlm_8", "描述": "冰蓄力", "Desc": "ColdOfIlm_8_Desc", "Tags": ["Group_Ilm", "BattleState_Power", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Ice", "Rogue_IlmRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_BeHurtCastIce"]}, {"id": "ColdOfIlm_9", "描述": "冰蓄满", "Desc": "ColdOfIlm_9_Desc", "Tags": ["Group_Ilm", "BattleState_Power", "Word_RogueIce", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Ice", "Rogue_IlmRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_FrozenHit"]}, {"id": "ThunderOfPoltick_8", "描述": "雷蓄力", "Desc": "ThunderOfPoltick_8_Desc", "Tags": ["Group_Poltick", "BattleState_Power", "Word_RogueElectric", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Thunder", "Rogue_PoltickRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_TickCreateThunderAoe"]}, {"id": "ThunderOfPoltick_9", "描述": "雷蓄满", "Desc": "ThunderOfPoltick_9_Desc", "Tags": ["Group_Poltick", "BattleState_Power", "Word_RogueThunder", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Thunder", "Rogue_PoltickRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_ThunderHit"]}, {"id": "WindOfZantia_8", "描述": "风蓄力", "Desc": "WindOfZantia_8_Desc", "Tags": ["Group_Zantia", "BattleState_Power", "Word_RogueWind", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Wind", "Rogue_ZantiaRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_WindShield"]}, {"id": "WindOfZantia_9", "描述": "风蓄满", "Desc": "WindOfZantia_9_Desc", "Tags": ["Group_Zantia", "BattleState_Power", "Word_RogueWindErosion", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Wind", "Rogue_ZantiaRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_WindHit"]}, {"id": "LightOfAzem_8", "描述": "光蓄力", "Desc": "LightOfAzem_8_Desc", "Tags": ["Group_Azem", "BattleState_Power", "Word_Roguelight", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Light", "Rogue_AzemRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_LightShield"]}, {"id": "LightOfAzem_9", "描述": "光蓄满", "Desc": "LightOfAzem_9_Desc", "Tags": ["Group_Azem", "BattleState_Power", "Word_RogueFlash", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Light", "Rogue_AzemRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_LightHit"]}, {"id": "CorruptionOfErminda_8", "描述": "暗蓄力", "Desc": "CorruptionOfErminda_8_Desc", "Tags": ["Group_Eminendanis", "BattleState_Power", "Word_RogueCorrupte", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Magic", "Rogue_EminendanisRibbon", "Rogue_1"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Power_DarkShield"]}, {"id": "CorruptionOfErminda_9", "描述": "暗蓄满", "Desc": "CorruptionOfErminda_9_Desc", "Tags": ["Group_Eminendanis", "BattleState_Power", "Word_RogueDevour", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Magic", "Rogue_EminendanisRibbon", "Rogue_2"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Power_DarkHit"]}, {"id": "Lunara_PowerFinalSpeed1", "描述": "蓄力满后2s物理伤害提升50%", "Desc": "Lunara_PowerFinalSpeed1_Desc", "RelicType": "Attack", "RecordId": "155", "Tags": ["OtherGods", "Group_Lunara", "BattleState_Power"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON>_Lunara", "Rogue_3"], "Value": 350, "RelicRarity": "Epic", "EffectBuff": ["PowerFinal_AddPhysicalDamage01"]}, {"id": "Lunara_PowerFinalSpeed2", "描述": "蓄力满后2s物理伤害提升30%", "Desc": "Lunara_PowerFinalSpeed2_Desc", "RelicType": "Attack", "RecordId": "156", "Tags": ["OtherGods", "Group_Lunara", "BattleState_Power"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON>_Lunara", "Rogue_3"], "Value": 350, "RelicRarity": "Rarely", "EffectBuff": ["PowerFinal_AddPhysicalDamage02"]}, {"分割": "-------------------------------------------弓箭手，天空城-----------------------------------------"}, {"分割": "------------------------------------------弓箭手相关遗物-----------------------------------------"}, {"分割": "-------------------------------------------Other-----------------------------------------"}, {"id": "Mix1", "描述": "造成伤害提升50%，受到伤害提升50%", "Desc": "Mix1_Desc", "Tags": ["OtherGods", "Group_Azouk", "NotInPool"], "MaxNum": 2, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rogue_1"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Rogue_DamageUp(50)", "Rogue_HurtUp(50)"]}, {"id": "Mix2", "描述": "造成伤害提升50%，受到伤害提升50%", "Desc": "Mix1_Desc", "Tags": ["OtherGods", "Group_Azouk", "NotInPool"], "MaxNum": 2, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rogue_1"], "Value": 400, "RelicRarity": "Epic", "EffectBuff": ["Rogue_DamageUp(50)", "Rogue_HurtUp(50)"]}, {"id": "BurnOfIgni_19", "描述": "元素伤害增强，暴击伤害减少", "Desc": "BurnOfIgni_19_Desc", "Tags": ["Group_Igni", "Word_ElementalDamage", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_Fire", "Rogue_IgniEmblem", "Rogue_4"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_ElementalDamageUp(40)", "Rogue_CriticalRateDown(2000)"]}, {"id": "PhyscialDamage2", "描述": "物理伤害增强，暴击伤害减少", "Desc": "PhyscialDamage2_Desc", "Tags": ["Group_Azouk", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rogue_4"], "Value": 300, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_PhysicalDamageUp(40)", "Rogue_CriticalRateDown(2000)"]}, {"id": "ElementalExchange2", "描述": "一级元素伤害增强30%，二级元素伤害减少20%", "Desc": "ElementalExchange2_Desc", "Tags": ["OtherGods", "Group_Azouk", "NotInPool"], "MaxNum": 2, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rogue_4"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_BasicElementalDamageUp(30)", "Rogue_AdvancedElementalDamageDown(10)"]}, {"id": "ElementalExchange4", "描述": "一级元素伤害增强30%，二级元素伤害减少10%", "Desc": "ElementalExchange4_Desc", "Tags": ["OtherGods", "Group_Azouk", "NotInPool"], "MaxNum": 2, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Rogue_4"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_BasicElementalDamageDown(30)", "Rogue_AdvancedElementalDamageUp(10)"]}, {"id": "Ardipeng_3", "描述": "法器cd时暴击率提升10%", "Desc": "Ardipeng_3_Desc", "Tags": ["OtherGods", "Group_Ardipeng", "NotInPool"], "MaxNum": 2, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue_Ardipeng", "Rogue_3"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_CriticalChanceUpWhenRogueItemInCoolDown(1000)"]}, {"id": "Ardipeng_6", "描述": "法器cd时暴击伤害提升20%", "Desc": "Ardipeng_6_Desc", "Tags": ["OtherGods", "Group_Ardipeng", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue_Ardipeng", "Rogue_6"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_CriticalRateUpWhenRogueItemInCoolDown(2000)"]}, {"id": "Ardipeng_7", "描述": "breakdown时暴击率增加30%", "Desc": "Ardipeng_7_Desc", "Tags": ["OtherGods", "Group_Ardipeng", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue_Ardipeng", "Rogue_7"], "Value": 260, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_CriticalChanceUpWhenTargetLowBreak(3000)"]}, {"id": "Ardipeng_8", "描述": "breakdown时暴击率增加20%", "Desc": "Ardipeng_8_Desc", "Tags": ["OtherGods", "Group_Ardipeng", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue_Ardipeng", "Rogue_7"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_CriticalChanceUpWhenTargetLowBreak(2000)"]}, {"id": "LuanaCriticalChanceForHP1", "描述": "生命值增加50%，暴击率降低15%", "Desc": "LuanaCriticalChanceForHP1_Desc", "Tags": ["OtherGods", "Group_Luana", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue<PERSON><PERSON><PERSON>", "Rogue_2"], "Value": 350, "RelicRarity": "Epic", "EffectBuff": ["Rogue_MaxHealthPercentUp(5000)", "Rogue_CriticalChanceDown(1500)"]}, {"id": "LuanaCriticalChanceForHP3", "描述": "生命值增加20%，暴击率降低5%", "Desc": "LuanaCriticalChanceForHP3_Desc", "Tags": ["OtherGods", "Group_Luana", "NotInPool"], "MaxNum": 3, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue<PERSON><PERSON><PERSON>", "Rogue_2"], "Value": 260, "RelicRarity": "Normal", "EffectBuff": ["Rogue_MaxHealthPercentUp(2000)", "Rogue_CriticalChanceDown(500)"]}, {"id": "LuanaCriticalDamageForHP1", "描述": "生命值增加50%，暴击伤害降低30%", "Desc": "LuanaCriticalDamageForHP1_Desc", "Tags": ["OtherGods", "Group_Luana", "NotInPool"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue<PERSON><PERSON><PERSON>", "Rogue_2"], "Value": 350, "RelicRarity": "Epic", "EffectBuff": ["Rogue_MaxHealthPercentUp(5000)", "Rogue_CriticalaaRateeDown(3000)"]}, {"id": "LuanaCriticalDamageForHP3", "描述": "生命值增加20%，暴击伤害降低10%", "Desc": "LuanaCriticalDamageForHP3_Desc", "Tags": ["OtherGods", "Group_Luana", "NotInPool"], "MaxNum": 3, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "Rogue<PERSON><PERSON><PERSON>", "Rogue_2"], "Value": 260, "RelicRarity": "Normal", "EffectBuff": ["Rogue_MaxHealthPercentUp(2000)", "Rogue_CriticalaaRateeDown(1000)"]}]}