{"Class": [{"说明": "法师", "Id": "Mage", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["_"], "Buffs": ["Character_Element"], "BaseActionType": "Player_Warrior", "StateActions": {"Ground": {"Armed": "Stave_Move", "UnArmed": "Stave_Unarmed_Move"}, "Flying": {"Armed": "Stave_Move", "UnArmed": "Stave_Unarmed_Move"}, "Falling": {"Armed": "Stave_Fall", "UnArmed": "Stave_Unarmed_Fall"}, "Attached": {"Armed": "Stave_Ride", "UnArmed": "Stave_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Stave_Hurt", "UnArmed": "Stave_Hurt"}, "Blow": {"Armed": "Stave_Blow", "UnArmed": "Stave_Blow"}, "Frozen": {"Armed": "Stave_Frozen", "UnArmed": "Stave_Frozen"}, "Bounced": {"Armed": "Stave_Bounced", "UnArmed": "Stave_Bounced"}, "Dead": {"Armed": "Stave_Dead", "UnArmed": "Stave_Dead"}, "Landing": {"Armed": "Stave_JustFall", "UnArmed": "Stave_Unarmed_JustFall"}, "SecondWind": {"Armed": "Stave_SecWind", "UnArmed": "Stave_SecWind"}, "GetUp": {"Armed": "Stave_RevivedOnSecWind", "UnArmed": "Stave_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 790], "BeStrikeRate": 1.0}, "WeaponType": "stave", "DefaultWeapons": ["Skeleton_Sword", "Wooden_Shield"], "ActionOnChangeTo": "ChangeToRanger", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________法杖徒手基础动作________________________________"}, {"说明": "法杖徒手走路站立", "Id": "Stave_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["Stave_Unarmed_Jump", "Stave_Unarmed_Dodge", "Stave_Aim", "Stave_DrawWeapon", "Unarm_UseItem", "Stave_DrawAttack", "Interactive"], "1": ["Stave_Unarmed_Jump", "Stave_Unarmed_Dodge", "Stave_Aim", "Stave_DrawWeapon", "Unarm_UseItem", "Interactive"], "2": ["Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Mage/Mage/UnarmedMove"]}}, {"说明": "法杖徒手起跳", "Id": "Stave_Unarmed_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "Stave_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["Stave_Air_DrawAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "法杖徒手翻滚", "Id": "Stave_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "Stave_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["Stave_Move", "Stave_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/Dodge_F"]}, "InitAction": true}, {"说明": "法杖徒手下落", "Id": "Stave_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "Stave_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Mage/Mage/UnarmedFall"]}, "Priority": 1}, {"说明": "法杖徒手下落着地", "Id": "Stave_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "Stave_Unarmed_Jump", "Interactive"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/UnarmedJustFall", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "法杖收刀", "Id": "SheathStave", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "Stave_SheathWea<PERSON>n", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Mage/Mage_<PERSON>/SheathW<PERSON>pon"]}}, {"说明": "法杖拔刀", "Id": "DrawStave", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "Stave_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Mage/Mage_<PERSON>/DrawWeapon"]}}, {"Line": "_______________________________法杖(LevelSquencer)动作________________________________"}, {"说明": "法杖趴地上_LevelSquencer用", "Id": "FallDown_Loop", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_Loop"]}}, {"说明": "法杖趴地上起来_LevelSquencer用", "Id": "FallDown_End", "Cmds": ["Move"], "Tags": [{"Tag": "FallDown_ShortBow", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Ranger_FallDown_End"]}}, {"说明": "法杖_Rodiantown_Mission04_OgreBossStartSeq", "Id": "Mission04_BossStartSeq", "Cmds": ["Move"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Move"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Sequence/Seq_Mission4_BossRoomStart"]}}, {"Line": "_______________________________法杖(持武器)基础动作________________________________"}, {"Id": "Stave_Move", "Cmds": ["Stave_Move"], "Tags": [{"Tag": "Stave_Move", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Jump", "Stave_Dodge", "Stave_Aim", "Stave_SheathWea<PERSON>n", "Unarm_UseItem", "Interactive"], "1": ["Stave_InitAttack", "Stave_Jump", "Stave_Dodge", "Stave_Aim", "Stave_SheathWea<PERSON>n", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Mage/Mage/Move", "ArtResource/Anim/BlendSpace/Player/Mage/Mage/Defense", "ArtResource/Anim/BlendSpace/Player/Mage/Mage/Defense"]}}, {"Id": "Stave_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Stave_HurtCounter"], "1": ["Stave_Dodge"], "2": ["Stave_QS_B"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Hurt_Back", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Hurt_Front", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Hurt_Air"]}}, {"Id": "Stave_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Stave_QS_B"], "1": ["Stave_QS_F"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Mage/Mage_<PERSON>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Blow_Front", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Blow_Up", "ArtResource/Anim/Montage/Player/Mage/Mage_<PERSON>/Hurt/Blow_Back", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/Blow_Front"]}}, {"Id": "Stave_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_Loop"]}, "InitAction": true}, {"Id": "Stave_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "Stave_Jump", "From": 0}, {"Tag": "Stave_Dodge", "From": 0}, {"Tag": "Stave_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Stave_AirInitAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "Stave_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Stave_AirInitAttack"], "1": ["Stave_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Mage/Mage/Fall"]}, "Priority": 1}, {"Id": "Stave_JustFall", "BeCancelledTags": {"0": ["Stave_Move", "Stave_Jump", "Stave_Dodge"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/JustFall", "ArtResource/Anim/Montage/Player/Mage/Mage_Male/Jump/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Stave_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "Stave_Dodge", "From": 0}], "BeCancelledTags": {"0": ["Stave_Move", "Stave_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/Dodge_F"]}, "InitAction": true}, {"Id": "Stave_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "Stave_Dodge_AJ", "From": 0}], "BeCancelledTags": {"0": ["Stave_Move", "Stave_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/Step_F"]}, "InitAction": true}, {"Id": "Stave_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "Stave_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Jump", "Stave_Dodge", "Stave_Aim", "Stave_SheathWea<PERSON>n", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/Step_F"]}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "Stave_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Stave_QS_B", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_SkillAttack", "Stave_Dodge", "Stave_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/QuickStanding_B"]}}, {"说明": "受身动作前翻", "Id": "Stave_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "Stave_QS_F", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_SkillAttack", "Stave_Dodge", "Stave_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Dodge/QuickStanding_F"]}}, {"说明": "倒地动作", "Id": "Stave_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Normal/Death_NoSecWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "Stave_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 100, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Hurt/RevivedOnSecondWind"]}}, {"Line": "_______________________________法杖特殊动作________________________________"}, {"Id": "Stave_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "Stave_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Mage/Mage_Male/Jump/AttachOnTarget"]}}, {"Id": "Stave_Ride", "Cmds": [], "Tags": [{"Tag": "Stave_Move", "From": 0}], "BeCancelledTags": {"0": ["Dismount"], "1": ["Stave_Move"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Player/Fighter/Ride"]}}, {"Line": "_______________________________法杖受击(特殊)动作________________________________"}, {"Id": "Stave_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Mage/Mage_Male/Hurt/Hurt_Frozen"]}}, {"Line": "_______________________________法杖基础(附加)动作________________________________"}, {"说明": "剑与盾盾", "Id": "ChangeToRanger", "Priority": 9, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/ChangeToRanger1"]}}, {"说明": "法杖弹刀动作", "Id": "Stave_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Jump", "Stave_Dodge", "Stave_Aim", "Stave_SheathWea<PERSON>n", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON>im/Montage/Player/Mage/Mage_Male/Attack/Bounced"]}}, {"说明": "瞄准动作", "Id": "Stave_Aim", "Cmds": ["Aim"], "Tags": [{"Tag": "Stave_InitAttack", "From": 0}, {"Tag": "Stave_SkillAttack", "From": 0}, {"Tag": "Stave_Defense", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_SkillAttack"], "1": ["Stave_Dodge"], "2": ["Stave_Defense_Attack1", "Stave_Defense_Attack2", "Stave_Defense_Attack3"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_Defense"]}}, {"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": []}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Fighter/Warrior_Male/PickUp"]}}, {"Line": "_______________________________法杖命令AI队友________________________________"}, {"说明": "命令AI队友向玩家聚集的动作", "Id": "Stave_OrderMoveToPlayer", "Cmds": ["OrderBuddyMoveToPlayer"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["Stave_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/OrderBuddy/OrderBuddyMoveToPlayer"]}}, {"说明": "命令AI队友向玩家指定目标移动的动作", "Id": "Stave_OrderMoveToLoc", "Cmds": ["OrderBuddyMoveToLoc"], "Tags": [{"Tag": "Buddy<PERSON><PERSON>r", "From": 0}], "BeCancelledTags": {"0": ["Stave_Dodge"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/OrderBuddy/OrderBuddyMoveToTarget"]}}], "RogueBattleActions": [{"Line": "_______________________________法杖战斗动作_______________________________"}, {"Line": "_______________________________法杖_普攻_地面_______________________________"}, {"说明": "法杖 普通攻击 1", "Id": "Ranger_LAttack01", "Cmds": [], "Tags": [{"Tag": "Stave_InitAttack", "From": 0}, {"Tag": "Stave_LAttack1", "From": 0}, {"Tag": "Stave_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_LAttack2"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"], "3": ["Stave_BranchAttack1"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_Slash0"]}, "InitAction": true}, {"说明": "法杖 普通攻击 2", "Id": "Ranger_LAttack02", "Cmds": [], "Tags": [{"Tag": "Stave_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_LAttack3"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_Slash1"]}, "InitAction": true}, {"Id": "Ranger_LAttack03", "Cmds": [], "Tags": [{"Tag": "Stave_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_LAttack1"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_Slash2"]}, "InitAction": true}, {"说明": "法杖分支普通攻击1", "Id": "Ranger_BranchAttack1", "Cmds": [], "Tags": [{"Tag": "Stave_BranchAttack1", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_BranchAttack2"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SlashB0"]}, "InitAction": true}, {"说明": "法杖分支普通攻击2", "Id": "Ranger_BranchAttack2", "Cmds": [], "Tags": [{"Tag": "Stave_BranchAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_BranchAttack3"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SlashB1"]}, "InitAction": true}, {"说明": "法杖分支普通攻击3", "Id": "Ranger_BranchAttack3", "Cmds": [], "Tags": [{"Tag": "Stave_BranchAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_BranchAttack4"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SlashB2"]}, "InitAction": true}, {"说明": "法杖分支普通攻击4", "Id": "Ranger_BranchAttack4", "Cmds": [], "Tags": [{"Tag": "Stave_BranchAttack4", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_BranchAttack5"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SlashB3"]}, "InitAction": true}, {"说明": "法杖分支普通攻击5", "Id": "Ranger_BranchAttack5", "Cmds": [], "Tags": [{"Tag": "Stave_BranchAttack5", "From": 0.0}], "BeCancelledTags": {"0": ["Stave_InitAttack"], "1": ["Stave_Dodge_Step"], "2": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SlashB4"]}, "InitAction": true}, {"Line": "_______________________________法杖_普攻_空中_______________________________"}, {"Id": "Ranger_AirAttack1", "Cmds": [], "Tags": [{"Tag": "Stave_AirInitAttack", "From": 0}, {"Tag": "Stave_AirAttack1", "From": 0}, {"Tag": "Stave_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_AirAttack2", "Stave_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/A_Slash0"]}, "InitAction": true}, {"Id": "Ranger_AirAttack2", "Cmds": [], "Tags": [{"Tag": "Stave_AirAttack2", "From": 0}], "BeCancelledTags": {"0": ["Stave_AirAttack1"], "1": ["_"], "2": ["Stave_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/A_Slash1"]}, "InitAction": true}, {"Line": "_______________________________法杖_技能_地面_动作_______________________________"}, {"Id": "Ranger_SkillSpell_1", "Cmds": [], "Tags": [{"Tag": "Stave_InitAttack", "From": 0}, {"Tag": "Stave_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Dodge_Step"], "1": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SkillSpell_1"]}, "InitAction": true}, {"Id": "<PERSON>_SkillSpell_Blast", "Cmds": [], "Tags": [{"Tag": "Stave_InitAttack", "From": 0}, {"Tag": "Stave_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Dodge_Step"], "1": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SkillSpell_Blast"]}, "InitAction": true}, {"Id": "<PERSON>_SkillSpell_Lightball", "Cmds": [], "Tags": [{"Tag": "Stave_InitAttack", "From": 0}, {"Tag": "Stave_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_InitAttack", "Stave_Dodge_Step"], "1": ["Stave_SkillAttack"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/G_SkillSpell_Lightball"]}, "InitAction": true}, {"Line": "_______________________________法杖_技能_空中_动作_______________________________"}, {"Id": "Ranger_Air_SkillSpell_Blast", "Cmds": [], "Tags": [{"Tag": "Stave_AirInitAttack", "From": 0}, {"Tag": "Stave_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["Stave_AirInitAttack", "Stave_AirAttack1", "Stave_AirSkillAttack"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Player/Mage/Mage_Male/Attack/A_SkillSpell_Blast"]}, "InitAction": true}, {"Line": "_______________________________法杖_瞄准_动作_______________________________"}], "RogueBattleStyle": ["MageStyle_1", "MageStyle_2", "MageStyle_3"]}], "Buff": [], "Aoe": []}