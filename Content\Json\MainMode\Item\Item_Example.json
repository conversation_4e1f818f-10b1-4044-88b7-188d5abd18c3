{"Item": {"Id": "Item的Id，根据这个Id，UI这里可以组织对应的信息", "OnUse": {"UseActionId": "【使用】这个道具的时候做的动作，必须使用者有对应的ActionInfo才行", "RemoveOnUsed": "<bool>是否在耐久度归0之后，就会导致ItemObj被消耗掉", "UseEffects": ["使用效果，是脚本函数名，类似ItemUtils.RestoreHealth(0.4)之类", "多条都会在对应的Action的Montage的OnUse的AnimNotify中一起生效", "(FItem<PERSON><PERSON><PERSON>, AAwCharacter* User, TArray<FString> DesignerParams)=>FItemUseResult"]}, "OnThrow": {"UseActionId": "【投掷】这个道具的时候做的动作，必须使用者有对应的ActionInfo才行", "RemoveOnUsed": "<bool>是否在耐久度归0之后，就会导致ItemObj被消耗掉", "UseEffects": ["投掷效果，是脚本函数名，类似ItemUtils.FireBullet(Grenade)之类", "多条都会在对应的Action的Montage的OnUse的AnimNotify中一起生效", "(FItem<PERSON><PERSON><PERSON>, AAwCharacter* User, TArray<FString> DesignerParams)=>FItemUseResult"]}, "OnEnchant": {"UseActionId": "【涂抹（概念上类似涂抹到武器上，但是是否真的涂抹上去看实现的UseEffects效果）】这个道具的时候做的动作，必须使用者有对应的ActionInfo才行", "RemoveOnUsed": "<bool>是否在耐久度归0之后，就会导致ItemObj被消耗掉", "UseEffects": ["使用效果，是脚本函数名，类似ItemUtils.EnchantMainHandWeapon(Poison)之类", "多条都会在对应的Action的Montage的OnUse的AnimNotify中一起生效", "(FItem<PERSON><PERSON><PERSON>, AAwCharacter* User, TArray<FString> DesignerParams)=>FItemUseResult"]}, "Appearance": [{"说明": "如果在道具对应的Action的Montage里面拉了AnimNotifyState(TempItemInHand)，里面会把当前使用的道具临时显示到角色身上，这时候就需要这个东西了，比如喝药拿在手里的小瓶子", "BluePrintPath": "Core/Characters/Equipment/BigSword 对应装备部位蓝图的位置", "BindPointId": "RightWeapon 绑点id，RightWeapon LeftWeapon EquipmentRoot Feather", "AppearanceType": "外观类别：Normal：普通类型组件，比如武器一般都是，没有动画，不用蒙皮；SkinnedMesh,动画类型组件，比如角色装备、弓箭等会跟着角色动画播放的，属于蒙皮组件；Physical 物理组件，需要计算物理的，比如衣服上的飘带、披风、狼皮等", "PhysicalBoneName": "PhysicalBone 从这个名称对应的骨骼开始，往下（包括这根骨骼）都会开始接受物理的影响，前提是AppearanceType==Physical"}], "Durability": "<int> 耐久度，大多是1，但是火把之类可以用多次的可以多点"}}