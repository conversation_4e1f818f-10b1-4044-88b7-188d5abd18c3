{"Dialogs": [{"说明": "第一次进大厅的对话", "Id": "Rogue_Hepha<PERSON>mose_FirstInHallDialog", "FirstClip": "Dialog01", "NpcId": [], "Clips": [{"Id": "Dialog01", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NoviceTutorial1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NoviceTutorial2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NoviceTutorial3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON>esmose_NoviceTutorial4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON>esmose_NoviceTutorial5"}, {"Type": "Speak", "Id": "Step5", "NextId": "Step6", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON>mose_NoviceTutorial6"}], "Selections": []}], "EndDialogScript": []}, {"说明": "初次对话", "Id": "<PERSON>_<PERSON><PERSON><PERSON><PERSON>e_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_VeryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_VeryFirstDialog2"}], "Selections": [{"说明": "故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueHephaesmoseStoryDialogPicker()", "Actions": []}]}, {"说明": "初次对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_StoryFirstDialog2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_First_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Hephaesmose_VeryFirst_Dialog,1)"]}, {"说明": "默认开始对话", "Id": "Rogue_<PERSON><PERSON><PERSON><PERSON><PERSON>_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_FirstDialog2"}], "Selections": [{"说明": "故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueHephaesmoseStoryDialogPicker()", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "初次对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_StoryFirstDialog2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_First_StoryDialog,1)"]}, {"说明": "默认的重复对话", "Id": "DefaultStoryDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1,Step2)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON>mose_DefaultRepeat1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON>mose_DefaultRepeat2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON>mose_DefaultRepeat3"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：这里是哪里？", "Id": "NormalQuestion1", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer1a"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer1b"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer1c"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Id": "NormalQuestion2", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer2"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：再跟我说说你是谁", "Id": "NormalQuestion3a", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer3a"}], "Selections": [{"说明": "常驻问题：你立下了什么誓言？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3b", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3b)", "Actions": []}]}, {"说明": "常驻问题：你立下了什么誓言？", "Id": "NormalQuestion3b", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer3b1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer3b2"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：我想了解一下赫曼城", "Id": "NormalQuestion4", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer4"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Id": "NormalQuestion5", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer5a"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer5b"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Id": "NormalQuestion6", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_NormalAnswer6"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "角色死亡后的故事对话", "Id": "AfterDeathDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_AfterDeath1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_AfterDeath2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON>mose_AfterDeath3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON><PERSON><PERSON>_AfterDeath4"}, {"Type": "Speak", "Id": "Step4", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON><PERSON><PERSON>_AfterDeath5"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_AfterDeath_StoryDialog,1)"]}, {"说明": "见过熔岩图后的故事对话", "Id": "ArriveLavaLevelDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_AfterLava1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_AfterLava2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_AfterLava3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_ArriveLavaLevel_StoryDialog,1)"]}, {"说明": "见过沙漠图后的故事对话", "Id": "ArriveDesertLevelDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hepha<PERSON>mose_AfterDesert1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hepha<PERSON>mose_AfterDesert2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_ArriveDesertLevel_StoryDialog,1)"]}, {"说明": "通关周目的重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRoundAgainRepeat1"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "拿到2个圣杯碎片的对话", "Id": "SecondGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainSecondGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainSecondGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_GrailLevel02_Dialog,1)"]}, {"说明": "拿到3个圣杯碎片的对话", "Id": "ThirdGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainThirdGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hepha<PERSON>mose_ObtainThirdGrail2"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_GrailLevel03_Dialog,1)"]}, {"说明": "拿到4个圣杯碎片的对话", "Id": "ForthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainForthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainForthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainForthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_GrailLevel04_Dialog,1)"]}, {"说明": "拿到5个圣杯碎片的重复对话", "Id": "FifthGrailDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainFifthGrailRepeat1"}], "Selections": [{"说明": "常驻问题：这里是哪里？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion1", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion1)", "Actions": []}, {"说明": "常驻问题：你怎么知道我是觉醒者？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion2", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion2)", "Actions": []}, {"说明": "常驻问题：再跟我说说你是谁？", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion3a", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion3a)", "Actions": []}, {"说明": "常驻问题：我想了解一下赫曼城", "Conditions": [], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion4", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion4)", "Actions": []}, {"说明": "常驻问题：我想了解一下熔岩渊堡", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveLavaLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion5", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion5)", "Actions": []}, {"说明": "常驻问题：我想了解一下龙脊荒漠", "Conditions": ["DialogCondition.CheckRogueSwitchGreater(ArriveDesertLevel)"], "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "Hephaesmose_NormalQuestion6", "NextEventFunc": "DialogAction.DirectGoTo(NormalQuestion6)", "Actions": []}, {"说明": "结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "提示交易没开的初次对话", "Id": "NoTradeFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_Trade1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_Trade2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_Trade3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_Trade4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_NoTrade_StoryDialog,1)"]}, {"说明": "提示交易没开的重复对话", "Id": "NoTradeDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_TradeRepeat1"}], "Selections": []}, {"说明": "交易开始对话", "Id": "TradeStartDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_TradeAfterKillDeathLord1"}], "EndClipScript": ["DialogAction.ShowCurrencyShopUI()"]}, {"说明": "交易结束对话", "Id": "TradeEndDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_TradeAfterKillDeathLord3"}], "Selections": []}]}, {"说明": "二周目第一次进大厅的对话", "Id": "Rogue_Hepha<PERSON>mose_WeeklyRoundDialog", "FirstClip": "WeeklyRoundDialog", "NpcId": [], "Clips": [{"说明": "新周目初次的故事对话", "Id": "WeeklyRoundDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRound1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRound2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRound3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRound4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_WeeklyRound_Dialog,1)", "DialogAction.SetRogueSwitch(GetGrailInfo,1)"]}], "EndDialogScript": []}, {"说明": "二周目开二楼的门的对话", "Id": "Rogue_Hephaesmose_WeeklyRoundAgainDialog", "FirstClip": "WeeklyRoundAgainDialog", "NpcId": [], "Clips": [{"说明": "新周目第二次的故事对话", "Id": "WeeklyRoundAgainDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRoundAgain1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRoundAgain2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_WeeklyRoundAgain3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON>esmose_WeeklyRoundAgain4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_WeeklyRoundAgain_Dialog,1)"]}], "EndDialogScript": []}, {"说明": "获得5个圣杯碎片回到大厅的对话", "Id": "<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>_FifthGrailDialog", "FirstClip": "FifthGrailDialog", "NpcId": [], "Clips": [{"说明": "拿到5个圣杯碎片的对话", "Id": "FifthGrailDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainFifthGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainFifthGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "Hephaesmose_ObtainFifthGrail3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_GrailLevel05_Dialog,1)"]}], "EndDialogScript": []}, {"说明": "打完最终BOSS回到大厅的对话", "Id": "Rogue_<PERSON><PERSON><PERSON><PERSON><PERSON>_KillFinalBossDialog", "FirstClip": "KillFinalBossDialog", "NpcId": [], "Clips": [{"说明": "击败真·真死骸骑士的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON><PERSON><PERSON>_KillRealDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON><PERSON><PERSON>_KillRealDeathLord2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Text": "He<PERSON><PERSON><PERSON><PERSON>_KillRealDeathLord3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Hephaesmose_KillFinalBoss_Dialog,1)"]}], "EndDialogScript": []}]}