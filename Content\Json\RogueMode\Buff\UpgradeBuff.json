{"Buff": [{"Desc": "又一层就添加一点空中闪避点数", "Id": "AddAirDodgePoint", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 2, "Property": [{"AirDodgePoint": 1}, {}]}, {"Desc": "立马回复10点觉醒值，回复完立刻删除自己", "Id": "AddAwakePoint_10", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 1, "MaxStack": 1000, "OnOccur": ["BattleUpgradeScript.AddAP_OnOccur(10)"]}, {"Desc": "CounterAction击杀回复觉醒点AP，每一层回生命值是1点", "Id": "AddAp_OnKill_ByCounterAction", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1000, "OnKill": ["BattleUpgradeScript.AddAp_OnKill_ByCounterAction()"]}, {"Desc": "CounterAction击杀回血，每一层回生命值是伤害的1%，最低为1点", "Id": "AddHp_OnKill_ByCounterAction", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1000, "OnKill": ["BattleUpgradeScript.AddHp_OnKill_ByCounterAction()"]}, {"Desc": "JustAttackAction击杀回血，每一层回生命值是伤害的1%，最低为1点", "Id": "AddHp_OnKill_ByJustAttackAction", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1000, "OnKill": ["BattleUpgradeScript.AddHp_OnKill_ByJustAttackAction()"]}, {"Desc": "MaxPowerAction击杀回血，每一层回生命值是伤害的1%，最低为1点", "Id": "AddHp_OnKill_ByMaxPowerAction", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1000, "OnKill": ["BattleUpgradeScript.AddHp_OnKill_ByMaxPowerAction()"]}, {"Desc": "在空中击杀回血，每一层回生命值是伤害的1%，最低为1点", "Id": "AddHp_OnKill_OnAir", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1000, "OnKill": ["BattleUpgradeScript.AddHp_OnKill_OnAir()"]}, {"Desc": "瞬时攻击造成伤害的时添加一个单次减伤Buff", "Id": "AddHalveDamageBuff_OnHit", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 1, "MaxStack": 1, "OnHit": ["BattleUpgradeScript.AddBuff_OnHit_ByJustAttackAction(HalveDamageBuff_OnHit,1,0,true)"]}, {"Desc": "受到伤害减半，只生效一次", "Id": "HalveDamageBuff_OnHit", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 1, "MaxStack": 1, "OnBeHurt": ["BattleUpgradeScript.ModifyDamage_OnBeHurt(0.5)"]}, {"Desc": "增加下一次伤害，只生效一次", "Id": "ModifyDamage_Once", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 1, "MaxStack": 1, "OnHit": ["BattleUpgradeScript.ModifyDamage_OnHit(1.5)"]}, {"Desc": "在空中击造成击杀之时添加一个一次伤害翻倍Buff", "Id": "AddDoubleDamageBuff_OnKill_OnAir", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 0, "MaxStack": 1, "OnKill": ["BattleUpgradeScript.AddBuff_OnKill_OnAir(DoubleDamageBuff_OnHit,1,0,true)"]}, {"Desc": "一次伤害翻倍Buff", "Id": "DoubleDamageBuff_OnHit", "Tag": ["BattleUpgrade"], "Priority": 0, "TickTime": 1, "MaxStack": 1, "OnHit": ["BattleUpgradeScript.ModifyDamage_OnHit(2)"]}, {"Line": "---------- ---------- 新战斗强化的buff 2023-10-10 ---------- ----------"}, {"Line": "---------- Swordsman ----------"}, {"Desc": "反击伤害增加buff，每层1%", "Id": "BattleUpgrade_CounterAttackDamageUp", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 150, "OnHit": ["BattleUpgradeScript.AddCounterAttackDamage()"]}, {"Desc": "回复觉醒值buff，每层1%。在完美防御动作的时候判断", "Id": "BattleUpgrade_AddApOnNotify", "Tag": ["BattleUpgrade", "AddApOnPerfectDefense"], "Priority": 0, "MaxStack": 10, "OnAnimNotfiy": ["BattleUpgradeScript.AddApOnAnimNotify()"]}, {"Desc": "回复HP，每层1%。在完美防御动作的时候判断", "Id": "BattleUpgrade_AddHpOnNotify", "Tag": ["BattleUpgrade", "AddHpOnPerfectDefense"], "Priority": 0, "MaxStack": 15, "OnAnimNotfiy": ["BattleUpgradeScript.AddHpOnAnimNotify()"]}, {"Line": "---------- BladeDancer ----------"}, {"Desc": "瞬时攻击的伤害增加，每层1%", "Id": "BattleUpgrade_AddJustAttackDamage", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 50, "OnHit": ["BattleUpgradeScript.AddJustAttackDamage()"]}, {"Desc": "添加收到伤害减半的buff，有几层就把目标buff设置为多少层，在动作里触发", "Id": "BattleUpgrade_AddBuffDamageHalved", "Tag": ["BattleUpgrade", "AddBuffDamageHalved"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiy": ["BattleUpgradeScript.SetBuffStackToThisStack(BattleUpgrade_DamageHalved_OnBeHurt)"]}, {"Desc": "伤害减半的buff，每判断一次，减少一层", "Id": "BattleUpgrade_DamageHalved_OnBeHurt", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 5, "OnBeHurt": ["BattleUpgradeScript.DamageHalved_OnBeHurt()"]}, {"Desc": "瞬时攻击造成伤害的时候后回血，每层是伤害量的1%", "Id": "BattleUpgrade_AddHpOnJustAttack", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 10, "OnHit": ["BattleUpgradeScript.AddHpOnJustAttack()"]}, {"Line": "---------- S<PERSON>man ----------"}, {"Desc": "在空中造成伤害增加，每层1%", "Id": "BattleUpgrade_AddDamageOnAir", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 50, "OnHit": ["BattleUpgradeScript.AddDamageOnAir()"]}, {"Desc": "每次跃升动作添加n层动作伤害增加20%的buff，添加的buff层数和当前buff层数一样", "Id": "BattleUpgrade_AddBuff_AddActionDamage", "Tag": ["BattleUpgrade", "AddBuff_AddActionDamage"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiy": ["BattleUpgradeScript.SetBuffStackToThisStack(BattleUpgrade_AddActionDamage20Percent)"]}, {"Desc": "动作伤害增加20%，每判断1次减1层", "Id": "BattleUpgrade_AddActionDamage20Percent", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 5, "OnHit": ["BattleUpgradeScript.AddActionDamage(0.2)"]}, {"Desc": "在空中造成伤害的时候回血，每层是伤害量的1%", "Id": "BattleUpgrade_AddHpOnAir", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 15, "OnHit": ["BattleUpgradeScript.AddHpOnAir()"]}, {"Desc": "在空中造成击杀，添加一次免疫伤害的buff", "Id": "BattleUpgrade_AddBuff_ImmuneDamage", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 1, "OnKill": ["BattleUpgradeScript.SetBuffStackToThisStack_OnKill(BattleUpgrade_ImmuneDamage)"]}, {"Desc": "一次免疫伤害的buff，判断1次减1层", "Id": "BattleUpgrade_ImmuneDamage", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BattleUpgradeScript.ImmuneDamage()"]}, {"Line": "---------- Warrior ----------"}, {"Desc": "蓄力攻击伤害增加，每层1%", "Id": "BattleUpgrade_AddPowerDamage", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 50, "OnHit": ["BattleUpgradeScript.AddPowerDamage()"]}, {"Desc": "蓄力速度增加，每层1%", "Id": "BattleUpgrade_AddPowerSpeed", "Tag": ["BattleUpgrade", "AddPowerSpeed"], "Priority": 0, "MaxStack": 40, "OnAnimNotfiyBegin": ["BattleUpgradeScript.AddPowerSpeed()"], "OnAnimNotfiyEnd": ["BattleUpgradeScript.ResetPowerSpeed()"]}, {"Desc": "回复觉醒值buff，每层1%。在剑意反击动作的时候判断", "Id": "BattleUpgrade_AddApOnParry", "Tag": ["BattleUpgrade", "AddApOnParry"], "Priority": 0, "MaxStack": 10, "OnAnimNotfiy": ["BattleUpgradeScript.AddApOnAnimNotify()"]}, {"Desc": "满蓄力造成伤害的时候回血，每层是伤害量的1%", "Id": "BattleUpgrade_AddHpOnMaxPower", "Tag": ["BattleUpgrade"], "Priority": 0, "MaxStack": 15, "OnHit": ["BattleUpgradeScript.AddHPOnMaxPower()"]}]}