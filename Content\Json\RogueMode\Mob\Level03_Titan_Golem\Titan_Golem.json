{"Mob": [{"Id": "Rogue_Titan_Golem", "MobRank": "Elite", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "Titan_GolemMoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Golem/Titan_Golem/Titan_Golem", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "Rogue_Titan_Golem", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 15, "PAtk": 15, "Balance": 10, "MoveSpeed": [120, 300, 790], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}, {"Id": "SpeedDownResistance", "Stack": 6000, "Time": 0, "Infinity": true}, {"Id": "ControlBuffResistance", "Stack": 3000, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "这是石头的部位", "Id": "Stone", "Meat": {"Physical": 1}, "Breakable": {"Physical": 1.0}, "Part": "Weapon", "Durability": [], "CanBeDestroy": true, "Type": "Rock", "HideSightPart": ["RockStone"], "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Titan_Golem/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Golem/Titan_Golem/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Mont<PERSON>/Monster/Golem/Titan_Golem/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"notes": "被Break的受击动作", "Id": "BreakHurt", "Cmds": ["BreakHurt"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 99, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Hit/Break_Down"]}, "InitAction": true}, {"说明": "近距离三连击", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "召唤地刺追踪攻击", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/NormalAttack_S2"]}, "InitAction": true}, {"说明": "前方召唤三个石板", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/NormalAttack_S3"]}, "InitAction": true}, {"说明": "岩石雨", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/NormalAttack_L1"]}, "InitAction": true}, {"说明": "车轮滚滚", "Id": "NormalAttack_S4", "Cmds": ["NormalAttack_S4"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/NormalAttack_S4"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Action_Stare01", "Cmds": ["Action_Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "左走走", "Id": "Action_Walk_Left", "Cmds": ["Action_Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Walk_Left"]}, "InitAction": true}, {"说明": "右走走", "Id": "Action_Walk_Right", "Cmds": ["Action_Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Golem/Titan_Golem/Movement/Walk_Right"]}, "InitAction": true}]}], "AOE": [{"Id": "Titan_Golem_Attack_S2_ExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_Attack_S2_ExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,2.5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_RockSpike_Tracking", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_RockSpike_Tracking", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_RockSpike", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_RockSpike", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_RockSpike_ExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_RockSpike_ExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,2.5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_Attack_S3_AOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_Attack_S3_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_Attack_S3_Slate", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_Attack_S3_Slate", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,2)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_Attack_S3_ExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_Attack_S3_ExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_Attack_L1_ExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_Attack_L1_ExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByTeam(1,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Stone_Explosion", "Tag": ["StoneExplosion"], "TickTime": 0.1, "BpPath": "Core/Item/AOE/PillarExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.DealDamageByFallingPillar(20, 3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Stone_Small_Explosion", "Tag": ["Stone_Small_Explosion"], "TickTime": 0.1, "BpPath": "Core/Item/Monster/Titan_Golem/StoneExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.DealDamageByFallingPillar(5, 5)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Titan_Golem_AttackExplosion", "Tag": ["Titan_Golem_AttackExplosion"], "TickTime": 0.2, "BpPath": "Core/Item/Monster/Titan_Golem/Titan_Golem_AttackExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["Ogre_AOEScript.DealDamageByFallingPillar(6, 3)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}], "Bullet": [{"Id": "Titan_Stone", "Tag": [], "BpPath": "Core/Item/Bullet/Titan_Stone", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(Stone_Explosion,2,true)", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactGround_Cue)"], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(Titan_Golem_Attack_L1_ExplodeAOE,0.5,true)", "BulletScript.CreateVFXOnRemoved(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnRemoved(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactCharacter_Cue)"]}, {"Id": "Titan_SmallStone", "Tag": [], "BpPath": "Core/Item/Bullet/Titan_SmallStone", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(Stone_Small_Explosion,0.5,true)", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)"], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(Stone_Small_Explosion,0.5,true)", "BulletScript.CreateVFXOnRemoved(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Rock_HitCharacter_2,0.5)"]}], "Buff": [{"Id": "ShowOrHideStone", "Tag": ["ShowOrHideStone"], "Priority": 0, "MaxStack": 1, "OnChangeAction": [], "OnHit": [], "OnOccur": ["TitanGolemBuff.ShowStone()"], "OnRemoved": ["TitanGolemBuff.HideStone()"]}]}