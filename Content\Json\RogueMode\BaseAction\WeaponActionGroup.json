{"说明": "这是武器对应的基础动作列表，当玩家角色切换到对应武器类型的时候，就会切换对应的StateAction和MontageAction设置", "范例": {"WeaponType": "武器类型枚举：BigSword、TwinSword，还可以再加，不过得程序代码中加", "StateActions": {"Ground": "站地上和跑步的动作", "Flying": "飞行的动作，就算不能飞好歹配个吧", "Falling": "下落动作，空中下落阶段用的，跟跳没有直接关系", "Attached": "攀附在敌人身上的标准动作（骑乘也是攀附，此处十分混沌，今后会重构）"}, "PreorderActionKeys": {"Hurt": "受伤动作", "Blow": "被吹飞动作", "Bounced": "弹刀动作，已经没意义了，但是还是先配一个", "Dead": "死亡动作", "Landing": "落地瞬间的动作，比如蹲一下", "SecondWind": "濒死状态动作", "GetUp": "爬起来的时候的动作，目前还没作用，先配好今后会有用"}, "ChangeWeaponAction": "更换到这个类型的武器之后会做的动作，不填写更换就没动作，乱写的话做了动作别喊bug就行了"}, "WeaponActionGroup": [{"WeaponType": "BigSword", "StateActions": {"Ground": "Standard_Move", "Flying": "Standard_Move", "Falling": "Standard_Fall", "Attached": "Standard_Ride"}, "PreorderActionKeys": {"Hurt": "Hurt", "Blow": "Hurt", "Bounced": "Warrior_Bounced", "Dead": "Dead", "Landing": "JustFall", "SecondWind": "SecWind", "GetUp": "RevivedOnSecWind"}, "ChangeWeaponAction": "Standard_LAttack01"}, {"WeaponType": "TwinSword", "StateActions": {"Ground": "TwinSword_Move", "Flying": "TwinSword_Move", "Falling": "TwinSword_Fall", "Attached": "TwinSword_Ride"}, "PreorderActionKeys": {"Hurt": "TwinSword_Hurt", "Blow": "TwinSword_Hurt", "Bounced": "TwinSword_Bounced", "Dead": "TwinSword_Dead", "Landing": "TwinSword_JustFall", "SecondWind": "TwinSword_SecWind", "GetUp": "TwinSword_RevivedOnSecWind"}, "ChangeWeaponAction": "Standard_LAttack01"}, {"WeaponType": "PoleArm", "StateActions": {"Ground": "Spear_Move", "Flying": "Spear_Move", "Falling": "Spear_Fall", "Attached": "Spear_Ride"}, "PreorderActionKeys": {"Hurt": "S<PERSON>_<PERSON>", "Blow": "S<PERSON>_<PERSON>", "Bounced": "<PERSON><PERSON>_<PERSON>ced", "Dead": "Spear_Dead", "Landing": "S<PERSON>_JustFall", "SecondWind": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>", "GetUp": "Spear_RevivedOnSecWind"}, "ChangeWeaponAction": "Standard_LAttack01"}]}