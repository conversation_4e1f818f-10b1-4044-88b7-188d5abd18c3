{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}]}]}}, {"Id": "<PERSON>_<PERSON>_Golem_Small", "Actions": [{"Id": "NormalAttack_X1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 600, "Weight": 5}, {"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_X2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 5}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}], "OutofCamera": -5.5, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 5, "MaxActionCD": 8}, {"Id": "Action_Walk_Left", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 4, "MaxActionCD": 8}, {"Id": "Action_Walk_Right", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 10}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 5, "MaxActionCD": 7}]}]}