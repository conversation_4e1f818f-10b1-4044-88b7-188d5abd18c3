{"ElementalTalentDesc": [{"Id": "Fire_ChanceDamage", "Elemental": "Fire", "TriggerType": "OnHit", "Level": 1, "EP": 5, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "星星之火", "BaseEffect": "在攻击时附带一定比例的火焰伤害。"}, {"Id": "Fire_BurnTarget", "Elemental": "Fire", "TriggerType": "OnHit", "Level": 2, "EP": 15, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "爆燃", "BaseEffect": "点燃目标，在一定时间内造成持续性火焰伤害。", "WeatherEffect": [{"Weather": "Volcano", "Desc": "持续性火焰伤害间隔变短，伤害变高。"}]}, {"Id": "Fire_Explosive", "Elemental": "Fire", "TriggerType": "OnUse", "Level": 3, "EP": 50, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "火焰爆破", "BaseEffect": "产生一阵火焰爆破。"}, {"Id": "Fire_BurningRestore", "Elemental": "Fire", "TriggerType": "Passive", "Level": 5, "EP": 120, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "烈焰永生", "BaseEffect": "受到火焰伤害不在损失生命，改为治疗自己。", "WeatherEffect": [{"Weather": "Volcano", "Desc": "受到火焰伤害获得持续性治疗。"}]}, {"Id": "Fire_BurningPath", "Elemental": "Fire", "TriggerType": "OnUse", "Level": 6, "EP": 180, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "烈焰之径", "BaseEffect": "所过之处留下一片烈焰。", "TerrainEffect": [{"Terrain": "Grass", "Effect": "烈焰范围更大，持续更久。"}]}, {"Id": "Fire_Phonix", "Elemental": "Fire", "TriggerType": "Passive", "Level": 10, "EP": 5500, "Icon": "ArtResource/UI/Icon/Item/potion_green", "Name": "凤凰涅槃", "BaseEffect": "生命值降低到0时立即回复一部分，很长时间内只能生效1次。", "WeatherEffect": [{"Weather": "Volcano", "Effect": "回复更多生命值。"}]}]}