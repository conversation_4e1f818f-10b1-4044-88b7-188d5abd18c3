{"说明": "标准的，常用的一些Buff", "Buff": [{"说明": "角色攻击力下降，每层0.01%", "Id": "Standard_AttackReduce", "Tag": ["<PERSON>rm", "PropertyDown"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"Attack": -1}]}, {"说明": "角色造成伤害提高，每层0.01%", "Id": "Standard_DamageUp", "Tag": ["Help", "DamageModify"], "Priority": 0, "MaxStack": 1000000, "OnHit": ["BuffUtils.DamageTimesUp(0.0001)"]}, {"说明": "角色移动速度提高，每层0.01%", "Id": "Standard_MoveSpeedUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"MoveSpeed": [0, 0, 0]}]}, {"说明": "角色行动速度提高，每层0.01%", "Id": "Standard_ActionSpeedUp", "Tag": ["Help", "PropertyUp"], "Priority": 0, "MaxStack": 1000000, "Property": [{}, {"ActionSpeed": 1}]}, {"说明": "免疫一次伤害", "Id": "Standard_IgnoreDamageOnce", "Tag": ["Help", "<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 9999, "OnBeHurt": ["BuffUtils.IgnoreDamage()"]}, {"说明": "免疫致死伤害", "Id": "Standard_IgnoreDeathDamage", "Tag": ["Help", "<PERSON><PERSON><PERSON>"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.IgnoreDeathDamage()"]}, {"说明": "角色被冻结", "Id": "Standard_PlayerFrozen", "Tag": [], "TickTime": 0.1, "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayerFrozen()"], "OnTick": ["BuffUtils.PrintBuffDuration()"]}, {"说明": "角色自己 TNND 燃起来了", "Id": "Standard_Burning", "Tag": ["<PERSON>rm", "Dot", "Fire"], "TickTime": 1.2, "Priority": 0, "MaxStack": 5, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(Burn,ArtResource/Environment/FX/Fire,Body,false,1)"], "OnTick": ["BuffUtils.DamageOverTime(Fire,10,0.5)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(Burn)"]}, {"说明": "AI 确定“最近的”攻击目标 ", "Id": "AIFindClosestTarget", "Tag": ["UpdateEnemy"], "Priority": 0, "TickTime": 0.1, "MaxStack": 1, "OnTick": ["BuffUtils.AIFindClosestTarget(300)"]}]}