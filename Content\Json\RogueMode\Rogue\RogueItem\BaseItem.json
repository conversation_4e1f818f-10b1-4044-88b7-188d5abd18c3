{"Item": [{"Id": "Global_Setting"}, {"Line": "________________________上面的是2.1版本占位道具____________________________"}, {"Id": "WindProtect_Rogue", "OnUse": {"UseActionId": "WindProtect_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "WindAroundKnife_Rogue", "OnUse": {"UseActionId": "WindProtect_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "Cannon_Rogue", "OnUse": {"UseActionId": "CannonSummon_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "LightCannon_Rogue", "OnUse": {"UseActionId": "CannonSummon_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "HealingPotion_Rogue", "OnUse": {"UseActionId": "DrinkPotion_Rogue", "RemoveOnUsed": "false", "UseEffects": []}, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/HealingPotion", "BindPointId": ["LeftWeapon", "LeftWeapon"], "AppearanceType": "Normal"}], "Durability": 1}, {"Id": "AegisImpact_Rogue", "OnUse": {"UseActionId": "AegisImpact_Rogue", "RemoveOnUsed": "false", "UseEffects": ["ItemUseEffect.CreateTargetOnUserPoint()"]}, "Appearance": [{"BluePrintPath": "Core/Item/AOE/Rogue/Item/Rogue_Aoe_LightShield", "BindPointId": [], "AppearanceType": "Normal"}], "Durability": 1}, {"Id": "LightingHammer_Rogue", "OnUse": {"UseActionId": "LightingHammer_Rogue", "RemoveOnUsed": "false", "UseEffects": ["ItemUseEffect.CreateTargetOnUserPoint()"]}, "Durability": 1}, {"Id": "ShadowHiddenWeapon_Rogue", "OnUse": {"UseActionId": "ShadowHiddenWeapon_Rogue", "RemoveOnUsed": "false", "UseEffects": ["ItemUseEffect.CreateTargetOnUserPoint()"]}, "Durability": 1}, {"Id": "FrozenDash_Rogue", "OnUse": {"UseActionId": "FrozenDash_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "FireDash_Rogue", "OnUse": {"UseActionId": "FlameDash_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "ThrowScythe_Rogue", "OnUse": {"UseActionId": "ThrowScythe_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "CircleSlash_Rogue", "OnUse": {"UseActionId": "CircleSlash_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "SummonDragon_Rogue", "OnUse": {"UseActionId": "Item_SummonD<PERSON>on", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "LandDragon_Rogue", "OnUse": {"UseActionId": "Item_SummonD<PERSON>on", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "DarkHole_Rogue", "OnUse": {"UseActionId": "DarkHole_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "WindHole_Rogue", "OnUse": {"UseActionId": "WindHole_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "EarthQuake_Rogue", "OnUse": {"UseActionId": "EarthQuake_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "Berserker_Rogue", "OnUse": {"UseActionId": "BerserkMode_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "CritUpItem_Rogue", "OnUse": {"UseActionId": "CannonSummon_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "ReflectBody_Rogue", "OnUse": {"UseActionId": "ThunderProtect_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "RangeThunder_Rogue", "OnUse": {"UseActionId": "RangeThunder_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "Range_Enchant", "OnUse": {"UseActionId": "Range_Enchant", "RemoveOnUsed": "false"}, "Durability": 1}, {"Id": "FrozenParry_Rogue", "OnUse": {"UseActionId": "FrozenParry_Rogue", "RemoveOnUsed": "false"}, "Durability": 1}]}