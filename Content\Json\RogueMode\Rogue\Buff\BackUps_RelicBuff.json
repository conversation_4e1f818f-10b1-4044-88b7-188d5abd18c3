{"说明": "遗物相关的Buff", "弃用": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个燃烧", "Id": "Anim_BurningHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Burning,1,8,true,false)"]}, {"说明": "动画中造成伤害时给对方一个冰霜", "Id": "Anim_IceHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Ice,1,8,true,false)"]}, {"说明": "动画中造成伤害时给对方一个带电", "Id": "An<PERSON>_ThunderOfPoltick_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Thunder,1,8,true,false)"]}, {"说明": "动画中造成伤害时给对方一个风切", "Id": "Anim_WindOfZantia_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Wind,1,10,true,false)"]}, {"说明": "动画中造成伤害时给对方一个耀光", "Id": "Anim_LightOfAzem_1_Effect", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Light,1,8,true,false)"]}, {"说明": "动画中造成伤害时给对方一个侵蚀", "Id": "Anim_CorruptionalHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Corruption,1,10,true,false)"]}, {"说明": "动画中引发“爆燃", "Id": "Anim_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(1,<PERSON>_<PERSON>,<PERSON>_FireBurst,1,1)"]}, {"说明": "动画中引发无消耗“爆燃", "Id": "Anim_FireBrustHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(1,<PERSON>_<PERSON>,<PERSON>_FireBurst,1,1,<PERSON><PERSON><PERSON><PERSON>,false)"]}, {"说明": "动画中概率引发“爆燃", "Id": "Anim_ChanceFireBrustHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToAoe(0.5,<PERSON>_<PERSON>,Rogue_FireBurst,1,1)"]}, {"说明": "动画中引发冻结", "Id": "Anim_FrozenHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,Rogue_IceCastFrozen,1,5,true,false,)"]}, {"说明": "动画中引发无消耗冻结", "Id": "<PERSON><PERSON>_FrozenHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(1,Rogue_IceCastFrozenNoCost,1,0.01,true,false,)"]}, {"说明": "动画中概率引发冻结", "Id": "Anim_ChanceFrozenHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(0.5,Rogue_IceCastFrozen,1,5,true,false,)"]}, {"说明": "动画中引发电涌", "Id": "Anim_ThunderHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(1,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10)"]}, {"说明": "动画中引发无消耗电涌", "Id": "Anim_ThunderHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(1,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10,<PERSON>T<PERSON><PERSON>,false)"]}, {"说明": "动画中概率引发电涌", "Id": "Anim_ChanceThunderHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToBullet(0.5,<PERSON>_<PERSON>,<PERSON>_Bullet_Poltick2,1,10)"]}, {"说明": "动画中引发风蚀", "Id": "Anim_WindHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.5,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON><PERSON><PERSON>,<PERSON>_WindErosion,1,1,1,true,false,true,<PERSON>_Aoe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0,0.5,<PERSON>_<PERSON><PERSON>_Zantia2,1)"]}, {"说明": "动画中无消耗引发风蚀", "Id": "Anim_WindHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.3,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1,false)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON>_<PERSON>,<PERSON>_WindErosion,1,1,1,true,false,true,false,<PERSON>_<PERSON>oe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.3,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1,false)"]}, {"说明": "动画中概率引发风蚀", "Id": "Anim_ChanceWindHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "另一种OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.3,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1)", "RogueBuff.BuffChanceCastById_StackToWindErosionBuff(1,<PERSON><PERSON><PERSON>,<PERSON>_WindErosion,1,3,1,true,false,true,<PERSON>_<PERSON>oe_Zantia2,1)"], "OnHit": ["RogueBuff.BuffChanceCastById_StackToWindErosion(1,<PERSON><PERSON><PERSON>,1,0.3,0.2,<PERSON>_<PERSON><PERSON>_Zantia2,1)"]}, {"说明": "动画中引发闪光", "Id": "Anim_LightHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,1.6,1,false,true)"]}, {"说明": "动画中无消耗引发闪光", "Id": "Anim_LightHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,1.6,1,false,true,false)"]}, {"说明": "动画中概率引发闪光", "Id": "Anim_ChanceLightHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToTimeOnHit(1,<PERSON>_<PERSON>,Rogue_Flash,1.6,1,false,true)"]}, {"说明": "动画中引发暗噬", "Id": "Anim_DarkHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(1,<PERSON>_Corruption,<PERSON>_DarkDevour,1,1.5,0.01,false,true,true)"]}, {"说明": "动画中引发无消耗暗噬", "Id": "Anim_DarkHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(1,<PERSON>_Corruption,<PERSON>_DarkDevour,1,1.5,0.01,false,true,true,false)"]}, {"说明": "动画中概率引发暗噬", "Id": "Anim_ChanceDarkHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(0.5,<PERSON>_Corruption,<PERSON>_DarkDevour,1,1.5,0.01,false,true,true)"]}, {"说明": "动画中引发黑暗领域", "Id": "Anim_DarkHit2", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.CreateAoeOnHitTarget(Rogue_Aoe_Eminendanis13,3.5,pelvis,<PERSON><PERSON><PERSON><PERSON>,true)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_DarkHit2,1,0,true)"]}, {"说明": "动画中引发火焰旋涡", "Id": "Anim_JustDodgeFireSpiral", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Igni11,4,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeFireSpiral,1,0,true)"]}, {"说明": "动画中引发冰霜旋涡", "Id": "Anim_JustDodgeIceSpiral", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_IceArea,2,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeIceSpiral,1,0,true)"]}, {"说明": "动画中引发闪电", "Id": "<PERSON><PERSON>_JustDodgeThunder", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Poltick11,0.7,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeThunder,1,0,true)"]}, {"说明": "动画中引发大旋风", "Id": "Anim_JustDodgeWind", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Zantia11,0.35,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeWind,0.7,0,true)"]}, {"说明": "动画中产生复数光弹", "Id": "Anim_JustDodgeLight", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Azem11,1,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeLight,1,0,true)"]}, {"说明": "动画中产生复数黑影", "Id": "Anim_JustDodgeDark", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Eminendanis11,1,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeDark,1,0,true)"]}, {"说明": "动画中生成持续到动画结束的闪电Aoe，AOE蓝图tick检测消失", "Id": "Anim_<PERSON>ick<PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick8,256,root,NoTween,true)"]}, {"说明": "动画中生成光盾特效并减伤", "Id": "Anim_LightShield", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(LightShield,ArtResource/ProjectRogue/VFX/ParagonZ/p_Ult_Windzone_Shield_Yellow,Mesh,false,1)"], "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_<PERSON>,1,5,true,false)", "BuffUtils.HurtDamageUp(-0.3)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(LightShield)"]}, {"说明": "动画中生成光盾特效并反伤", "Id": "An<PERSON>_WindShield", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia8,256,root,NoTween,true)"], "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,<PERSON>_<PERSON>,1,5,true,false)", "RogueBuff.CounterattackOnHurtByCarrierProp(15,0.7)"]}, {"说明": "动画中挨打概率持续给燃烧", "Id": "<PERSON><PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_Burning,1,5,true,false)"]}, {"说明": "动画中挨打概率持续给冰霜", "Id": "Anim_<PERSON><PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,<PERSON>_<PERSON>,1,5,true,false)"]}, {"说明": "动画中挨打概率持续给侵蚀", "Id": "<PERSON><PERSON>_<PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_Corruption,1,5,true,false)"]}, {"说明": "动画中挨打引发特殊冰霜旋涡", "Id": "Anim_<PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnBeHurtSocket(Rogue_Aoe_Ilm8,4,root)", "BuffUtils.RemoveSelfBuffStackOnHit(An<PERSON>_<PERSON>,1,0,true)"]}, {"说明": "动画中完美闪避生成火焰Aoe", "Id": "An<PERSON>_DogeCreateFireSpiral", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Igni11,4,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeFireSpiral,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeFireSpiral,1,0,true)"]}, {"说明": "动画中概率引发额外突刺", "Id": "Anim_Pierce<PERSON>it", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["BuffUtils.DoExtraOffense(0.2,3,<PERSON><PERSON><PERSON><PERSON><PERSON>,Physical)"]}, {"说明": "动画中造成的break上升", "Id": "Anim_BreakUp", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "OnHit": ["BuffUtils.BreakDamageUp(0.05)"]}, {"分割": "-------------------------------------------Fire-----------------------------------------"}, {"说明": "造成伤害33%时给对方一个燃烧", "Id": "BurningHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Fire"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_BurningHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_BurningHit,1,0,true)"]}, {"说明": "造成伤害时,若燃烧层数叠加到3层以上时，50%引发“爆燃", "Id": "FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 5, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceFireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceFireBrustHit,1,0,true)"]}, {"说明": "下砸时候向下落方向点生成一道火柱", "Id": "Smash_FireBulletAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_3_IgniBullet,10,Root_Bullet,BulletScript.GoStraightAhead(5000))"]}, {"说明": "下砸落地的时候产生一个Aoe", "Id": "Smash_GroundFireAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni4,3,root)"]}, {"说明": "下砸时候对击中的目标产生一次爆燃", "Id": "Smash_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "位移终点生成一个火焰Aoe", "Id": "Dash_FireAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd", "FXRelic_Dash_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni6,1,Weapon_rSocket)"]}, {"说明": "位移技能时造成伤害则触发燃烧转换", "Id": "Dash_FireCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "蓄力时被打,反手给个燃烧", "Id": "Power_BeHurtCastBurn", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次爆燃", "Id": "Power_FireBrustHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个延迟引爆的Aoe", "Id": "Doge_FireSpiral", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON>oe_Igni10,2.5,<PERSON>)"]}, {"说明": "击飞技能时对击中的目标产生一次爆燃", "Id": "Rise_FireBrustHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState", "FXRelic_Below_Fire"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FireBrustHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FireBrustHit,1,0,true)"]}, {"说明": "击飞技能施放时在面前产生一个Aoe", "Id": "Rise_FireAoe", "Tag": ["Relic", "NotSave", "Rogue_BelowStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Igni13,1,Root)"]}, {"说明": "造成火元素伤害的时候概率对火元素的主动道具进行额外充能", "Id": "AddItemRecover_Fire", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Fire)"]}, {"说明": "每过TickTime秒发射一个风火轮", "Id": "Launch_FireTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Igni17,0,0.1,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON>rit_FireAoe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Igni18,0.3,Root)"]}, {"分割": "-------------------------------------------Ice-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个冰霜", "Id": "IceHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_IceHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_IceHit,1,0,true)"]}, {"说明": "造成伤害时引爆冰霜给对方一个冻结", "Id": "FrozenHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceFrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceFrozenHit,1,0,true)"]}, {"说明": "击中的第一个目标产生一次冰爆", "Id": "IceBurst", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnHit": ["BuffUtils.CreateAoeOnHitPos(Rogue_IceBurst,1)", "BuffUtils.RemoveSelfBuffStackOnHit(IceBurst,1,0,true)"]}, {"说明": "下砸时候对击中的第一个目标产生一次冰爆", "Id": "Smash_IceBurst", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(IceBurst,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(IceBurst,1,0,true)"]}, {"说明": "下砸时候产生一个即将坠落的冰晶", "Id": "Smash_IceAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashStart", "FXRelic_Smash_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_Bullet_Ilm4,20,NONE)"]}, {"说明": "下砸时对击中的目标产生一次冻结", "Id": "Smash_FrozenHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "位移终点生成一个冰霜Aoe", "Id": "Dash_IceAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd", "FXRelic_Dash_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Ilm6,0.5,Weapon_rSocket)"]}, {"说明": "位移技能时造成伤害则触发冰霜转换", "Id": "Dash_IceCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "蓄力时被打,反手给个冰霜", "Id": "Power_BeHurtCastIce", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(An<PERSON>_<PERSON>,1,0,true)", "BuffUtils.AddBuffObj(An<PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_<PERSON>,1,0,true)", "BuffUtils.RemoveSubBuffObj(Anim_<PERSON>ce<PERSON>,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次冻结", "Id": "Power_FrozenHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个冰霜领域的AOE持续一段时间", "Id": "IceDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Ilm8,3,<PERSON>)"]}, {"说明": "动画中完美闪避生成冰霜Aoe", "Id": "An<PERSON>_DogeCreateIceSpiral", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_IceArea,2,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeIceSpiral,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeIceSpiral,1,0,true)"]}, {"说明": "击飞技能时对击中的目标产生一次冻结", "Id": "Rise_FrozenHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_FrozenHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_FrozenHit,1,0,true)"]}, {"说明": "击飞技能施放时在面前产生一个冰Aoe", "Id": "Rise_IceAoe", "Tag": ["Relic", "NotSave", "Rogue_BelowStart", "FXRelic_Below_Ice"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Ilm13,4,Root)"]}, {"说明": "造成冰元素伤害的时候概率对冰元素的主动道具进行额外充能", "Id": "AddItemRecover_Ice", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Ice)"]}, {"说明": "每过TickTime秒发射一个冰匕首", "Id": "Launch_IceTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Ilm17,0,0.1,pelvis)"]}, {"说明": "受击时产生一个冰盾,CDxxx秒", "Id": "Hurt_CreateIceShield", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.CreateAoeOnBeHurtSocketByCoolDown(Rogue_Aoe_Ilm18,3,pelvis,10,true)"]}, {"分割": "-------------------------------------------Thunder-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个带电", "Id": "ThunderOfPoltick_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(An<PERSON>_ThunderOfPoltick_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(An<PERSON>_ThunderOfPoltick_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率引爆带电产生一次电涌", "Id": "ThunderOfPoltick_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceThunderHit,1,0,true)"]}, {"说明": "下砸时候,向前方产生一道电流Aoe", "Id": "Smash_ThunderAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick3,2,Root)"]}, {"说明": "下砸时候产生一个落雷", "Id": "Smash_ThunderAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick4,0.5,Root)"]}, {"说明": "下砸时对击中的目标产生一次电涌", "Id": "Smash_ThunderHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHit,1,0,true)"]}, {"说明": "位移起点生成一个闪电残影", "Id": "Dash_ThunderStanding", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashStart", "FXRelic_Dash_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick6,1,Root)"]}, {"说明": "位移技能时造成伤害则触发电击转换", "Id": "Dash_ThunderCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHit,1,0,true)"]}, {"说明": "蓄力时,周围周期性产生一圈电", "Id": "Power_TickCreateThunderAoe", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>Tick<PERSON><PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(An<PERSON>_ThunderTick<PERSON>oe,1,0,true)"]}, {"说明": "蓄满攻击时候对击中的目标产生一次电涌", "Id": "Power_ThunderHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHitNoCost,1,0,true)"]}, {"说明": "闪避时产生一个闪电Aoe", "Id": "ThunderDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Poltick10,0.5,None)"]}, {"说明": "动画中完美闪避生成闪电Aoe", "Id": "<PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Poltick11,0.7,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeThunder,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeThunder,1,0,true)"]}, {"说明": "击飞技能时对击中的目标产生一次电涌", "Id": "Rise_ThunderCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ThunderHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ThunderHit,1,0,true)"]}, {"说明": "击飞技能施放时在面前产生一个闪电鞭", "Id": "Rise_ThunderWhips", "Tag": ["Relic", "NotSave", "Rogue_BelowStart", "FXRelic_Below_Thunder"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Poltick13,0.5,Root)"]}, {"说明": "造成雷元素伤害的时候概率对雷元素的主动道具进行额外充能", "Id": "AddItemRecover_Thunder", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Thunder)"]}, {"说明": "每过TickTime在目标生成一个Aoe", "Id": "Launch_ThunderTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Poltick17,0,0.1,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON><PERSON>_<PERSON>oe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Poltick18,0.5,Root)"]}, {"分割": "-------------------------------------------Wind-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个风切", "Id": "WindOfZantia_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindOfZantia_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindOfZantia_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率引爆风切产生一次风蚀", "Id": "WindOfZantia_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceWindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceWindHit,1,0,true)"]}, {"说明": "下砸时候,周围卷起一阵旋风Aoe", "Id": "Smash_WindAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia3,1,Root)"]}, {"说明": "下砸技能时，产生一道龙卷风，每秒向周遭的敌人进行一次攻击", "Id": "Smash_WindAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia4,2,<PERSON>)"]}, {"说明": "下砸技能时，被击中的目标引发“风蚀”效果。", "Id": "Smash_WindHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "位移技能时，在位移的起点，产生一个气流弹，掀飞被击中的敌人。", "Id": "Dash_WindBullet", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd", "FXRelic_Dash_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_Bullet_Zantia6,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“风蚀”效果。", "Id": "Dash_WindCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "玩家蓄力时，在自身周围产生风盾，反伤，给敌人一层风切。", "Id": "Power_WindShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>Shield,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON>im_WindShield,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“风蚀”效果。", "Id": "Power_WindHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个小风环刃。", "Id": "WindDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Zantia10,1,<PERSON>)"]}, {"说明": "玩家完美闪避时，在敌人位置产生数道次元斩，并且给敌人一层“风切”。", "Id": "Anim_DogeCreateWindAoe", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Zantia11,0.35,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeWind,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeWind,1,0,true)"]}, {"说明": "击飞技能时，击中有”风切“层数的单位时，引发“风蚀”效果。", "Id": "Rise_WindCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_WindHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_WindHit,1,0,true)"]}, {"说明": "击飞技能时，在周围卷起一阵旋风，掀飞被击中的敌人。", "Id": "Rise_WindAoe", "Tag": ["Relic", "NotSave", "Rogue_BelowStart", "FXRelic_Below_Wind"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Zantia13,0.5,Root)"]}, {"说明": "对空中敌人造成的伤害提升30%,并且带有风切的敌人视为空中敌人", "Id": "Wind_Effect_14", "Tag": ["Relic", "NotSave", "WindCastSky"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["RogueBuff.DamageTimesUpToSkyOrWind(0.3)"]}, {"说明": "造成风元素伤害的时候概率对风元素的主动道具进行额外充能", "Id": "AddItemRecover_Wind", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Wind)"]}, {"说明": "每过TickTime在目标生成一个Aoe", "Id": "Launch_WindTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Zantia17,0,0.1,Root)"]}, {"说明": "暴击时在敌方脚下产生一个Aoe", "Id": "<PERSON><PERSON>_<PERSON>", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnCrit": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Zantia18,0.5,Root)"]}, {"分割": "-------------------------------------------Light-----------------------------------------"}, {"说明": "造成伤害时10%概率给对方一个耀光", "Id": "LightOfAzem_1_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightOfAzem_1_Effect,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightOfAzem_1_Effect,1,0,true)"]}, {"说明": "造成伤害时20%概率给对方一个闪光", "Id": "LightOfAzem_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceLightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceLightHit,1,0,true)"]}, {"说明": "下砸技能时，产生一道光芒AOE，每秒向周遭的敌人进行一次攻击", "Id": "Smash_LightAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashStart"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Rogue_3_AzemBullet,10,Root_Bullet,BulletScript.GoStraightAhead(5000))"]}, {"说明": "下砸技能时，产生一圈光弹，每颗产生一次耀光", "Id": "Smash_LightAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem4,1,Root)"]}, {"说明": "下砸技能时，被击中的目标引发“闪光”效果。", "Id": "Smash_LightHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "位移技能时，在位移的终点，产生一圈光弹。", "Id": "Dash_LightBullet", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashStart", "FXRelic_Dash_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem6,1,Root_Bullet)"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“闪光”效果。", "Id": "Dash_LightCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "玩家蓄力时，在自身周围产生光盾，减伤，给敌人一层光耀。", "Id": "Power_LightShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightShield,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightShield,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“闪光”效果。", "Id": "Power_LightHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个光点尾迹aoe。", "Id": "LightDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Azem10,1.6,Root)"]}, {"说明": "玩家完美闪避时，在自身位置产生n个自动追踪的光弹，光弹命中给敌人一层“闪光”", "Id": "Anim_DogeCreateLightBullet", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Azem11,1,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeLight,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeLight,1,0,true)"]}, {"说明": "击飞技能时，击中有”耀光“层数的单位时，引发“闪光”效果。", "Id": "Rise_LightCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_LightHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_LightHit,1,0,true)"]}, {"说明": "击飞技能时，产生一个光柱。", "Id": "Rise_LightAoe", "Tag": ["Relic", "NotSave", "Rogue_BelowStart", "FXRelic_Below_Light"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Azem13,1,Root)"]}, {"说明": "造成光元素伤害的时候概率对光元素的主动道具进行额外充能", "Id": "AddItemRecover_Light", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Light)"]}, {"说明": "每过TickTime秒发射一个光剑", "Id": "Launch_LightTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 5, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Azem17,0,0.1,pelvis)"]}, {"说明": "受击时在敌人处产生一道圣光,CDxxx秒", "Id": "Hurt_LightAoe", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnBeHurt": ["RogueBuff.CreateAoeOnEnemySocketByCoolDown(Rogue_Aoe_Azem18,1,root,5,true)"]}, {"分割": "-------------------------------------------Eminendanis-----------------------------------------"}, {"说明": "造成伤害时给对方一个侵蚀", "Id": "CorruptionalHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Dark"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_CorruptionalHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_CorruptionalHit,1,0,true)"]}, {"说明": "造成伤害时20%概率给对方一个暗噬", "Id": "CorruptionOfErminda_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceDarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceDarkHit,1,0,true)"]}, {"说明": "下砸技能时，产生一道黑暗AOE，每秒向周遭的敌人进行一次攻击并吸引敌人", "Id": "Smash_DarkAoe1", "Tag": ["Relic", "Hit", "NotSave", "Rogue_SmashEnd", "FXRelic_Smash_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis3,1,Root)"]}, {"说明": "下砸技能时，将落点附近的敌人拉扯到落点", "Id": "Dash_DarkAoe2", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis4,0.7,root)"]}, {"说明": "下砸技能时，被击中的目标引发“暗噬”效果。", "Id": "Smash_DarkHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "位移技能时，在位移的终点，产生一个黑暗残像。", "Id": "Dash_DarkShadow", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd", "FXRelic_Dash_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis6,5,root)"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“暗噬”效果。", "Id": "Dash_DarkCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState", "FXRelic_Dash_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "玩家蓄力时被打，反给一个侵蚀。", "Id": "Power_DarkShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON><PERSON>_<PERSON>Dark,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“暗噬”效果。", "Id": "Power_DarkHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个黑影。", "Id": "DarkDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(3,<PERSON>_<PERSON><PERSON>_Eminendanis10,5,None)"]}, {"说明": "玩家完美闪避时，在自身位置产生n个黑影", "Id": "Anim_DogeCreateDarkShadow", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Eminendanis11,1,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeDark,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeDark,1,0,true)"]}, {"说明": "击飞技能时，击中有”侵蚀“层数的单位时，引发“暗噬”效果。", "Id": "Rise_DarkCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "击飞技能时，对击中的一个敌人产生一个黑暗立场。", "Id": "Rise_DarkAoe", "Tag": ["Relic", "NotSave", "Rogue_BelowState", "FXRelic_Below_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit2,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit2,1,0,true)"]}, {"说明": "造成暗元素伤害的时候概率对暗元素的主动道具进行额外充能", "Id": "AddItemRecover_Darkness", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Darkness)"]}, {"说明": "每过TickTime秒生成围绕的骷髅头", "Id": "Launch_DarkTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 10, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Eminendanis17,0,0.1,pelvis,0,true)"]}, {"说明": "直接命中击杀敌人时产生一个追踪的黑暗子弹", "Id": "<PERSON>_<PERSON><PERSON><PERSON>", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnKill": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Eminendanis18,0.1,root)"]}, {"分割": "-------------------------------------------Other-----------------------------------------"}, {"说明": "攻击时产生一个剑气", "Id": "AttackCreateBulletOnSocket", "Tag": ["CreateBullet", "Rogue_Slash", "NotSave", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateBulletOnSocket(Bullet_SwordLight,10,<PERSON>_Bullet,BulletScript.GoStraightAhead(2500))"]}, {"说明": "突刺攻击时刻产生额外距离Aoe", "Id": "Rogue_Pierce1_Effect", "Tag": ["Hit", "NotSave", "<PERSON><PERSON><PERSON>", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(<PERSON>_<PERSON><PERSON>_<PERSON>,1,<PERSON>_<PERSON>et)"]}, {"说明": "突刺攻击命中时额外造成伤害侵犯", "Id": "Rogue_Pierce2_Effect", "Tag": ["NotSave", "Rogue_Pierce_State", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_PierceHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_PierceHit,1,0,true)"]}, {"说明": "钝击攻击时刻产生额外Aoe", "Id": "Rogue_Bludgeon1_Effect", "Tag": ["Hit", "NotSave", "Rogue_Bludgeon", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Blu<PERSON>on,1,Root)"]}, {"说明": "钝击攻击时造成的break上升", "Id": "Rogue_Bludgeon2_Effect", "Tag": ["Hit", "NotSave", "Rogue_Bludgeon_State", "NotShow"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_BreakUp,10,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_BreakUp,10,0,true)"]}, {"说明": "造成伤害时33%概率给对方一个会受到额外伤的DeBuff", "Id": "BloodlyHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTargetByChance(0.34,<PERSON>_<PERSON>ly,1,20,true,false)"]}, {"说明": "造成暴击时回复1点生命", "Id": "HealHpOnCrit", "Tag": ["Relic", "<PERSON>rit", "NotSave"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnCrit": ["RogueBuff.HealHpOnCrit(1)"]}, {"说明": "受到伤害时候+5金币", "Id": "<PERSON><PERSON><PERSON>", "Tag": ["Relic", "Hurted", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnBeHurt": ["RogueBuff.GiveRogueCurrencyOnHurt(Rogue_Coin,1)"]}, {"说明": "击杀怪物时增加主动道具充能", "Id": "AddItemRecoverOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnKill": ["RogueBuff.AddItemRecoverOnKill(0.01)"]}, {"说明": "击杀精英怪或Boss时永久加15生命上限", "Id": "LifeOfKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnOccur": ["RogueBuff.AddMaxLifByKillOnOccur(10,999999)"], "OnKill": ["RogueBuff.AddMaxLifeOnKill(Elite,10,999999)"]}, {"说明": "击杀敌人时候获得5s的加速Buff", "Id": "Rogue_AddSpeedOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.AddBuffOnKill(Rogue_SpeedUpOnKill,3000,5,false,true)"]}, {"说明": "肉鸽击杀后速度提高", "Id": "Rogue_SpeedUpOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 3000, "TickTime": 0, "Property": [{}, {"ActionSpeed": 1, "MoveSpeed": [1, 1, 1]}]}, {"说明": "肉鸽击杀后10%概率生成范围治疗", "Id": "Rogue_CreateHealAoeOnKill", "Tag": ["Relic", "Kill", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnKill": ["RogueBuff.ChanceCreateAoeOnKill(0.1,<PERSON>_<PERSON><PERSON>_<PERSON><PERSON><PERSON>,5,root)"]}, {"说明": "身上每有一个圣遗物，全攻击力增加0.01%，上限300%约等于没有上限", "Id": "Rogue_RelicCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToAttackPercent(1,30000)"]}, {"说明": "身上每有一种神的圣遗物，全攻击力增加0.01%", "Id": "Rogue_RelicGodCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToAttackPercent(1)"]}, {"说明": "身上每有一块钱，全攻击力增加0.01%", "Id": "Rogue_MoneyCastAttack", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastCoinNumToAttackPercent(1)"]}, {"说明": "身上每有一个圣遗物，暴击率增加0.01%，上限100%（约等于没有上限", "Id": "Rogue_RelicCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalChance(0.0001,1)"]}, {"说明": "身上每有一种神的圣遗物，暴击率增加0.01%", "Id": "Rogue_RelicGodCastCriticalChance", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalChance(0.0001)"]}, {"说明": "身上每有一个圣遗物，暴击伤害倍率增加0.01%，上限200%", "Id": "Rogue_RelicCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicNumToCriticalRate(0.0001,2)"]}, {"说明": "身上每有一种神的圣遗物，暴击伤害倍率增加0.01%", "Id": "Rogue_RelicGodCastCriticalRate", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CastRelicGodTagToCriticalRate(0.0001)"]}, {"说明": "身上每有一个圣遗物，最大生命增加1，上限100（约等于没有上限", "Id": "Rogue_CastRelicNumToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicNumToMaxHp(1,100)"], "OnTick": ["RogueBuff.CastRelicNumToMaxHp(1,100)"]}, {"说明": "身上每有一种神的圣遗物，最大生命增加1", "Id": "Rogue_CastRelicGodTagToMaxHp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "AfterRogueLoadSaveData": ["RogueBuff.CastRelicGodTagToMaxHp(1)"], "OnTick": ["RogueBuff.CastRelicGodTagToMaxHp(1)"]}, {"说明": "肉鸽主动道具充能时,提升暴击率", "Id": "Rogue_CriticalChanceUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalChanceUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "肉鸽主动道具充能时,提升暴击倍率", "Id": "Rogue_CriticalRateUpWhenRogueItemInCoolDown", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0.1, "OnTick": ["RogueBuff.CriticalRateUpWhenRogueItemInCoolDown(0.0001)"]}, {"说明": "敌人没有或break值低于50%的时候暴击率提升1%", "Id": "Rogue_CriticalChanceUpWhenTargetLowBreak", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.LowBreakEnemyCriticalChanceUp(0.5,0.0001)"]}, {"说明": "对生命值低于50%的敌人造成的伤害提升1%", "Id": "Rogue_DamageUpToLowHPEnemy", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.DamageUpToLowHpEnemy(0.5,0.01)"]}, {"说明": "每过n秒没有直接攻击,则下次攻击造成的伤害+1%", "Id": "Rogue_TimeIntervalDamageUp", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100000, "TickTime": 0, "OnHit": ["RogueBuff.TimeIntervalDamageUp(3,0.01)"]}]}