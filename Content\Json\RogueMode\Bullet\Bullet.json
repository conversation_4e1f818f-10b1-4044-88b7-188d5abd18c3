{"Bullet": [{"Id": "Test01", "Tag": ["Fire"], "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 5, "HitDelay": 0.1, "Life": 5, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit()"], "OnRemoved": []}, {"Id": "Claw", "Tag": ["Claw"], "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.TryCatchOnTarget(Asshole)"], "OnRemoved": []}, {"Id": "Test_Fireball", "Tag": ["<PERSON><PERSON>", "Magic"], "BpPath": "Core/Item/Bullet/FireballScroll_Bullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.FireBallHit(500)"], "OnRemoved": ["BulletScript.FireBallRemoved()"]}, {"Id": "FallingPillar", "Tag": [], "BpPath": "Core/Item/Monster/Ogre/FallingPillar", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(PillarExplosion,0.4,true)", "OgreBulletScript.DestroyAllPillar()", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactGround_Cue)"], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(PillarExplosion,0.4,true)", "BulletScript.CreateSFXOnRemoved(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactCharacter_Cue)"]}, {"Id": "OgrePillar", "Tag": [], "BpPath": "Core/Item/Bullet/Ogre_PillarBullet", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": [], "OnHit": ["BulletScript.CreateAOEOnHit(PillarExplosion,0.2,true)", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnHit(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactGround_Cue)", "OgreBulletScript.CreateExplodePillarOnHit()"], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(PillarExplosion,0.2,true)", "BulletScript.CreateVFXOnRemoved(ArtResource/ProjectRogue/VFX/ParagonZ/P_Rampage_Lunge_ImpactNEW_NoCrmLt_Spread_XL)", "BulletScript.CreateSFXOnRemoved(Audio/Sound_Cue/Monster/Troll/Troll_Pillar_ImpactCharacter_Cue)", "OgreBulletScript.CreateExplodePillarOnRemoved()"]}, {"描述": "玩家丢出来的奶酪", "Id": "ThrownCheese", "Tag": [], "BpPath": "Core/Item/Bullet/Cheese", "SightEffect": "", "CanHitFoe": false, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 1, "Type": "Bullet", "OnCreate": [], "OnHit": [], "OnRemoved": ["BulletScript.CreateAoEOnRemoved(<PERSON><PERSON>,60,true)"]}, {"描述": "冰恶魔丢出来的镰刀", "Id": "IceScythe", "Tag": [], "BpPath": "Core/Item/Monster/IceDevil/Bullet_IceScythe", "SightEffect": "", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": ["IceDevilBulletScript.SetBulletOriginRotation(-2)"], "OnHit": ["IceDevilBulletScript.DealDamageOnHit(1)", "BulletScript.CreateVFXOnHit(ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Ultimate_Explode_Small)"], "OnRemoved": ["BulletScript.CreateVFXOnRemoved()", "IceDevilBulletScript.CreateIceScytheSceneItem(ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Decoy_Explode_Fragments,ArtResource/ProjectRogue/VFX/ParagonZ/P_Aurora_Ultimate_Explode_Small)"]}, {"Id": "Arrow", "Tag": [], "BpPath": "Core/Item/Bullet/Arrow", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": ["BulletScript.SetBulletRotationByBulletDir()"], "OnHit": ["BulletScript.DealDamageOnHit(1.2)"], "OnRemoved": []}, {"Id": "<PERSON>_<PERSON>kel<PERSON>_Crossbowman", "Tag": [], "BpPath": "Core/Item/Bullet/Arrow", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": ["BulletScript.SetBulletRotationByBulletDir()"], "OnHit": ["BulletScript.DealDamageOnHit(0.6)"], "OnRemoved": []}, {"Id": "Rogue_TrapArrow", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/TrapArrow", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 20, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealDamageOnHit(2)"], "OnRemoved": []}, {"Id": "CannonBullet", "Tag": [], "BpPath": "Core/Item/Bullet/CannonBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "BreakBoneBullet", "Tag": [], "BpPath": "Core/Item/Rouge/Trap/BreakBoneBullet", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 5, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.DealFixedDamageOnHit(30)"], "OnRemoved": []}, {"Id": "LavaBall", "Tag": [], "BpPath": "Core/Item/Rouge/Trap/LavaBall", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 5, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}, {"Id": "Knife", "Tag": [], "BpPath": "Core/Item/Bullet/Knife", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.2, "Life": 1, "Size": 100, "Type": "Bullet", "OnCreate": ["BulletScript.SetBulletRotationByBulletDir()"], "OnHit": ["BulletScript.DealDamageOnHit(1.8)"], "OnRemoved": []}, {"Id": "Rogue_Meteorite", "Tag": [], "BpPath": "Core/Item/Bullet/Rogue/Meteorite", "CanHitFoe": true, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0, "Life": 10, "Size": 100, "Type": "Bullet", "OnHit": [], "OnRemoved": []}]}