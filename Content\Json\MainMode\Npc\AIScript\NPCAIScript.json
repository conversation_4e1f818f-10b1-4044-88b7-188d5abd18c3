{"AIScript": [{"说明": "检查是否前往下一个点巡逻，每到一个巡逻点，发一次呆", "Id": "NPCMoveToNearestPathPoint", "Condition": ["MobAIScript.CheckMoveToNextPathNode()"], "OnReady": [], "Action": ["MobAIScript.MoveToNextPathNode(Daze1,<PERSON>ze2,<PERSON>ze3)"], "Tag": ""}, {"说明": "检查是否前往下一个尽可能抵达点巡逻，每到一个巡逻点，进行一次停留的随机行为", "Id": "NPCRunToNearestPossiblePathPoint", "Condition": ["MobAIScript.CheckMoveToNextPossiblePathNode()"], "OnReady": [], "Action": ["MobAIScript.MoveToNextPossiblePathNode(2, GuardA_LongSpeech)"], "Tag": ""}, {"说明": "检查是否前往下一个尽可能抵达点巡逻，每到一个巡逻点，进行一次停留的随机行为", "Id": "NPCWalkToNearestPossiblePathPoint", "Condition": ["MobAIScript.CheckMoveToNextPossiblePathNode()", "MobAIScript.CheckHasBuff(NPCNoCombat)"], "OnReady": [], "Action": ["MobAIScript.MoveToNextPossiblePathNode(1, GuardA_LongSpeech)"], "Tag": ""}, {"说明": "前往保护目标附近 check的三个参数 抵达目标范围最小范围 目标偏移中心最大距离 目标偏移中心最小距离 action的fun参数为速度临界距离 高速度等级 低速度等级", "Id": "MoveToProtectedSceneItemAround", "Condition": ["MobAIScript.CheckMoveToProtectedSceneItemAround(500,300,150)"], "OnReady": [], "Action": ["MobAIScript.MoveToProtectedSceneItemAround(1000,1,2)"], "Tag": ""}, {"说明": "执行非战斗常态状态行为被打扰后反应", "Id": "DoInterruptedReflaction", "Condition": ["MobAIScript.CheckCharacterNotInWar()", "NPCAIScript.CheckNpcInterruptedInterestHigher(LowInterested)"], "OnReady": [], "Action": ["NPCAIScript.DoInterruptedReflaction(KeepShortStay,KeepLongStay,InterruptHurt)"], "Tag": ""}, {"说明": "发呆", "Id": "", "Condition": [], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(<PERSON>ze1,<PERSON><PERSON>2,<PERSON><PERSON>3)"], "Tag": ""}, {"说明": "拔出武器", "Id": "", "Condition": ["MobAIScript.CheckStimulateByView()", "NPCAIScript.CheckNpcNotArmed()"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyDoAction(拔出武器)"]}, {"说明": "收起武器", "Id": "", "Condition": ["MobAIScript.CheckNotStimulateByView()", "NPCAIScript.CheckNpcArmed()"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(收起武器)"]}, {"说明": "近距离攻击动作（愤怒）", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,300,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,300,-60,60, 近距离愤怒攻击动作)"]}, {"说明": "近距离攻击动作", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,300,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,300,-60,60,近距离攻击动作)"]}, {"说明": "中距离攻击动作（愤怒）", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,700,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,700,-60,60,中距离愤怒攻击动作)"]}, {"说明": "中距离攻击动作", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,700,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,700,-60,60,中距离攻击动作)"]}, {"说明": "向前闪避", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(700,3000,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(700,3000,-60,60.向前闪避)"]}, {"说明": "向右闪避", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,200,60,90)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(向右闪避)"]}, {"说明": "向左闪避", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,200,-90,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(向左闪避)"]}, {"说明": "向后闪避", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,100,-30,30)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(向后闪避)"]}, {"说明": "左右踱步", "Id": "", "Condition": ["WereRatAIScript.CheckHasViewedEnemyInRange(0,400)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["WereRatCommandoAIScript.MoveAroundViewedClosetEnemy(0,400,左踱步,右踱步)"]}, {"说明": "随机左右转身", "Id": "", "Condition": ["MobAIScript.CheckNotStimulateByView()", "NPCAIScript.CheckNpcArmed()", "MobAIScript.CheckHasEnemyInRange(3000)"], "OnReady": [], "Action": ["MobAIScript.AIDoRandomAction(左90转身,左180转身,右90转身,右180转身)"]}]}