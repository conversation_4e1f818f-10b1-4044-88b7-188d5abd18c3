{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Rogue_Skeleton_Newbie", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 5}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 0}], "MinActionCD": 4, "MaxActionCD": 8}, {"Id": "Stare01", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 3}, {"MinRange": 500, "MaxRange": 1000, "Weight": 1}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "0"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "10"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 6, "MaxActionCD": 10}, {"Id": "Walk_Left", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 0}, {"MinRange": 0, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "10"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "-99"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 10}, {"Id": "Walk_Right", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 0}, {"MinRange": 400, "MaxRange": 1000, "Weight": -99}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "10"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 10}, {"Id": "Walk_Front", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 1000, "Weight": 10}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 0, "MaxActionCD": 3}, {"Id": "Walk_Back", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 0, "MaxActionCD": 5}, {"Id": "Action_Defense", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": -99}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 6, "MaxActionCD": 8}]}]}