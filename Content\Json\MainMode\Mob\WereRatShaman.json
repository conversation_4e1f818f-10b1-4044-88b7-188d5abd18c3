{"Mob": [{"Id": "WereRatShaman", "Tag": ["WereRat"], "AI": ["StopAI", "WereRat_Debut", "CheckShamanInBattle", "CheckShamanInCircle", "CheckShamanExcited", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "WereRatShaman_Crawling", "WereRatTurnToStimulate"], "BpPath": "Core/Characters/WereRatShaman/WereRat_Shaman", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -30}], "OnBeKilled": ["MobBeKilled.RatManBeKilled()", "DesignerScript/TriggerScript_Country.CheckAllRatsDeathInMission04()"], "Flyable": false, "ExpGiven": 1, "LootPackageId": "WereRat", "MountsTypeRotate": false, "Name": "鼠人萨满", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"SightHalfAngleDregee": 135, "LoseSightHalfAngleDregee": 140, "ViewRadius": 500, "SightRadius": 2000, "SightZRadius": 1200, "LoseSightRadius": 3000, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 4, "Attack": 5, "Balance": 10, "MoveSpeed": [145, 290, 435], "BeStrikeRate": 0.75}, "Buff": [{"Id": "WereRat_BeHurt", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "WereRat_DelayRecordNum", "Stack": 1, "Time": 3, "Infinity": false}, {"Id": "StopAI", "Stack": 1, "Time": 0, "Infinity": true}], "Equipments": [{"Id": "WereRat_Shaman_Body", "Rate": 1}, {"Id": "WereRat_Shaman_Arm", "Rate": 1}, {"Id": "WereR<PERSON>_Shaman_Leg", "Rate": 1}, {"Id": "<PERSON><PERSON><PERSON>_Shaman_Head", "Rate": 1}, {"Id": "WereRatShamanWeapeon", "Rate": 1}], "Part": [{"Meat": {"Slash": 0.8, "Blunt": 1.0, "Bullet": 0.9}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Slash": 0.5, "Blunt": 0.6, "Bullet": 0.6}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Slash": 0.9, "Blunt": 0.4, "Bullet": 0.5}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Slash": 1.0, "Blunt": 0.3, "Bullet": 0.2}, "Part": "Tail", "Durability": [1], "CanBeDestroy": false, "Priority": 7, "StableMod": 1.0, "Type": "Meat"}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["Move", "InitAttack", "ProtectSceneItem"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/Monster/WereRatCommando/Move"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Dead_Blow_Back"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": ["Hurt"], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up", "ArtResource/Anim/Montage/Monster/WereRat/Hurt/Blow_Up"]}}, {"Id": "BellRinging", "Cmds": ["BellRinging"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/Spell_BellRinging"]}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>", "Cmds": ["<PERSON><PERSON><PERSON>"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/Spell_CrazyBuff"]}, "InitAction": true}, {"Id": "Attack_S1", "Cmds": ["Attack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Attack/NormalAttack_S1"]}, "InitAction": true}, {"Id": "Attack_S2", "Cmds": ["Attack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Attack/NormalAttack_S2"]}, "InitAction": true}, {"Id": "ThrowFireBall", "Cmds": ["ThrowFireBall"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Attack/NormalAttack_L1"]}, "InitAction": true}, {"Id": "<PERSON><PERSON><PERSON>", "Cmds": ["<PERSON><PERSON><PERSON>"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/Sniff"]}, "InitAction": true}, {"Id": "Talk", "Cmds": ["Talk"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/Talk"]}, "InitAction": true}, {"Id": "StampAlly", "Cmds": ["StampAlly"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/StampAlly"]}, "InitAction": true}, {"说明": "发呆：喘气", "Id": "WereRat_DeepBreathe", "Cmds": ["WereRat_DeepBreathe"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/DeepBreathe"]}}, {"说明": "发呆：害怕", "Id": "WereRat_Fear", "Cmds": ["WereRat_Fear"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Fear"]}}, {"说明": "发呆：累了", "Id": "WereRat_Tired", "Cmds": ["WereRat_Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["ProtectSceneItem"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Tired"]}}, {"Id": "ReBirth", "Cmds": ["ReBirth"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/ReBirth"]}, "InitAction": true}, {"说明": "登场", "Id": "Debut", "Cmds": ["Debut"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Mont<PERSON>/Monster/WereRat/Action/Debut"]}, "InitAction": true}, {"Id": "Crawling_Short", "Cmds": ["Run_Forward_Short"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Short"]}, "InitAction": true}, {"Id": "Crawling_Long", "Cmds": ["Run_Forward_Long"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Crawling_Long"]}, "InitAction": true}, {"说明": "转向刺激源动作", "Id": "TurnToStimulate", "Cmds": ["TurnToStimulate"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LookAround"]}, "InitAction": true}, {"说明": "左踱步", "Id": "LeftPace", "Cmds": ["LeftPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/LeftPace"]}, "InitAction": true}, {"说明": "右踱步", "Id": "RightPace", "Cmds": ["RightPace"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/RightPace"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_Back", "Cmds": ["Dodge_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRat/Action/Dodge_JumpStep_Back"]}, "InitAction": true}, {"说明": "保护场景物件", "Id": "ProtectSceneItem", "Cmds": ["ProtectSceneItem"], "Tags": [{"Tag": "ProtectSceneItem", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/WereRatShaman/Action/CastingOnce"]}, "InitAction": true}]}], "AOE": [{"Id": "WereRatShaman_Fire_Explosion", "Tag": ["Explosion"], "TickTime": 0, "BpPath": "Core/Item/Monster/WereRat/WereRatShaman_Fire_Explosion", "OnCreate": [], "OnCharacterEnter": ["WereRat_AOEScript.DealDamageAndStunWereRat(40)"]}], "Bullet": [{"Id": "WereRatShaman_PoisonBall", "Tag": ["Fire"], "BpPath": "Core/Item/Monster/WereRat/WereRatShaman_PoisonBall", "CanHitFoe": false, "CanHitAlly": false, "HitSameTarget": 1, "HitDelay": 0.1, "Life": 1, "Size": 100, "Type": "Bullet", "OnHit": ["BulletScript.CreateAOEOnHit(WereRatShaman_Fire_Explosion,0.3)"], "OnRemoved": ["BulletScript.CreateAoE(WereRatShaman_Fire_Explosion,0.3)"]}], "Buff": [{"Id": "WereRat_Berserk", "Tag": ["Buff", "Berserk"], "Priority": 0, "MaxStack": 1, "OnOccur": ["BuffUtils.PlayVFXOnCha_Occur(BerserkEye,Temp/Effect/ParagonZ/FX_Morigesh/Particles/Morigesh/Abilities/StabDoll/FX/P_Morigesh_StabDoll_Targeting_ARMFX,Eye)", "BuffUtils.PlayVFXOnCha_Occur(BerserkBody,Temp/Effect/ParagonZ/FX_Morigesh/Particles/Morigesh/Abilities/LifeDrain/FX/P_Morigesh_PhaseShift_Looping,Body)"], "OnRemoved": ["BuffUtils.StopVFXOnCha_Remove(BerserkEye)", "BuffUtils.StopVFXOnCha_Remove(BerserkBody)"]}, {"Id": "WereRat_Berserk_CD", "Tag": ["Buff", "WereRat"], "Priority": 0, "MaxStack": 1}, {"Id": "WereRat_ReBirth_CD", "Tag": ["Buff", "WereRat"], "Priority": 0, "MaxStack": 1}]}