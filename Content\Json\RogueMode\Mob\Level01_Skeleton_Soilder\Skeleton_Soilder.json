{"Mob": [{"Id": "Rogue_Skeleton_Soilder", "Tag": [""], "AI": ["StopAIBuff", "RogueMob_BasicBattle", "MoveToPlayer"], "BpPath": "Core/Characters/Rogue_Mob/Skeleton/Skeleton_Soilder/Skeleton_Soilder", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "SoundSocket": "head", "DungeonCampImpact": [], "OnBeKilled": [], "Flyable": false, "LootPackageId": "", "MountsTypeRotate": false, "Name": "普通骷髅兵", "Portrait": "icon_smelling_luozi", "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "MoveProp": {"StopRangeWhenMoveFollow": 400, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 20000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 20001, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 3, "PAtk": 1, "Balance": 10, "MoveSpeed": [150, 400, 600], "BeStrikeRate": 0.75}, "Buff": [{"Id": "StopAI", "Stack": 1, "Time": 1.5, "Infinity": false}], "Equipments": [], "Part": [{"Meat": {"Physical": 1}, "Part": "Head", "Durability": [1], "CanBeDestroy": false, "Priority": 8, "StableMod": 1.0, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Body", "Durability": [1], "CanBeDestroy": false, "StableMod": 1.0, "Priority": 6, "Type": "Meat"}, {"Meat": {"Physical": 1}, "Part": "Arm", "Durability": [1], "CanBeDestroy": false, "Priority": 4, "StableMod": 1.0, "Type": "Meat"}, {"Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [1], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Standard_Move", "Unarmed": "Standard_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Fall", "Unarmed": "Standard_Fall"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Fall_Land", "UnArmed": "Fall_Land"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Standard_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction", "Jump"]}, "CanUseOnFalling": false, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Skeleton/Skeleton_Solider/Move"]}}, {"Id": "Standard_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["Standard_Move"]}, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Monster/Skeleton/Skeleton_Solider/Fall_Loop"]}, "Priority": 1}, {"Id": "Fall_Land", "BeCancelledTags": {"0": ["Move"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Movement/Jump_Land"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Blow_Front"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Hit/Dead_Blow_Back"]}, "InitAction": true}, {"说明": "近距离攻击二连", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击单次", "Id": "NormalAttack_P1", "Cmds": ["NormalAttack_P1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/NormalAttack_P1"]}, "InitAction": true}, {"说明": "发呆1", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Action_Stare01"]}, "InitAction": true}, {"说明": "防御", "Id": "Action_Defense", "Cmds": ["Action_Defense"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"], "1": ["Defense_Success"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Action_Defense"]}, "InitAction": true}, {"说明": "防御成功", "Id": "Skeleton_Defense_Success", "Cmds": ["Skeleton_Defense_Success"], "Tags": [{"Tag": "Defense_Success", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 51, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Action_Defense_Success"]}, "InitAction": true}, {"说明": "向左走", "Id": "Walk_Left", "Cmds": ["Walk_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Walk_Left"]}, "InitAction": true}, {"说明": "向右走", "Id": "Walk_Right", "Cmds": ["Walk_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Walk_Right"]}, "InitAction": true}, {"说明": "向前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Walk_Front"]}, "InitAction": true}, {"说明": "向后走", "Id": "Walk_Back", "Cmds": ["Walk_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Monster/Skeleton/Skeleton_Solider/Battle/Walk_Back"]}, "InitAction": true}]}]}