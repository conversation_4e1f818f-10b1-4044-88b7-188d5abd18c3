{"RelicInfo1": [{"分割": "-------------------------------------------金钱-16个-----------------------------------------"}, {"id": "OriginalCoin_1", "描述": "获得金币提高20%", "RelicType": "Other", "RecordId": "107", "Desc": "OriginalCoin_1_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_GetCoinUp(20)"]}, {"id": "OriginalCoin_2", "描述": "获得金币提高10%", "RelicType": "Other", "RecordId": "108", "Desc": "OriginalCoin_2_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_GetCoinUp(10)"]}, {"id": "OriginalCoin_3", "描述": "获得金币提高5%", "RelicType": "Other", "RecordId": "109", "Desc": "OriginalCoin_3_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_GetCoinUp(5)"]}, {"id": "OriginalSoul_1", "描述": "获得魂晶提高25%", "RelicType": "Other", "RecordId": "110", "Desc": "OriginalSoul_1_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_GetSoulUp(25)"]}, {"id": "OriginalSoul_2", "描述": "获得魂晶提高15%", "RelicType": "Other", "RecordId": "111", "Desc": "OriginalSoul_2_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_GetSoulUp(15)"]}, {"id": "OriginalSoul_3", "描述": "获得魂晶提高10%", "RelicType": "Other", "RecordId": "112", "Desc": "OriginalSoul_3_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_GetSoulUp(10)"]}, {"id": "OriginalPiece_1", "描述": "获得残响提高3个", "RelicType": "Other", "RecordId": "113", "Desc": "OriginalPiece_1_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Key"], "IconPath": ["Rogue_None", "H14"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_GetKeyUp(3)"]}, {"id": "OriginalPiece_2", "描述": "获得残响提高2个", "RelicType": "Other", "RecordId": "114", "Desc": "OriginalPiece_2_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Key"], "IconPath": ["Rogue_None", "H14"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_GetKeyUp(2)"]}, {"id": "OriginalPiece_3", "描述": "获得残响提高1个", "RelicType": "Other", "RecordId": "115", "Desc": "OriginalPiece_3_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Key"], "IconPath": ["Rogue_None", "H14"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_GetKeyUp(1)"]}, {"id": "OriginalHurtCoin1", "描述": "受伤获得金币3", "RelicType": "Other", "RecordId": "116", "Desc": "OriginalHurtCoin1_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["<PERSON><PERSON><PERSON>(3)"]}, {"id": "OriginalHurtCoin2", "描述": "受伤获得金币2", "RelicType": "Other", "RecordId": "117", "Desc": "OriginalHurtCoin2_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["<PERSON><PERSON><PERSON>(2)"]}, {"id": "OriginalHurtCoin3", "描述": "受伤获得金币1", "RelicType": "Other", "RecordId": "118", "Desc": "OriginalHurtCoin3_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["<PERSON><PERSON><PERSON>"]}, {"id": "OriginalShopDiscount1", "描述": "商店打九折", "RelicType": "Other", "RecordId": "119", "Desc": "OriginalShopDiscount1_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 345, "RelicRarity": "Epic", "EffectBuff": ["Rogue_ShopDiscount(10)"]}, {"id": "OriginalShopDiscount2", "描述": "商店打九三折", "RelicType": "Other", "RecordId": "120", "Desc": "OriginalShopDiscount2_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_ShopDiscount(7)"]}, {"id": "OriginalShopDiscount3", "描述": "商店打九五折", "RelicType": "Other", "RecordId": "121", "Desc": "OriginalShopDiscount3_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 155, "RelicRarity": "Normal", "EffectBuff": ["Rogue_ShopDiscount(5)"]}, {"id": "OriginalFixedRefresh", "描述": "商店刷新固定50金", "RelicType": "Other", "RecordId": "122", "Desc": "OriginalFixedRefresh_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 230, "RelicRarity": "Rarely", "EffectBuff": ["Rogue_ShopRefreshFixedPrices"]}, {"id": "MoneyIsPower", "描述": "持有金币增加攻击力，1000金提升30%攻击力", "RelicType": "Attack", "RecordId": "106", "Desc": "MoneyIsPower_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 480, "RelicRarity": "Legend", "EffectBuff": ["Rogue_MoneyCastAttack(3)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_MoneyCastAtk(Atk,Rogue_MoneyCastAttack,1,1)"]}]}