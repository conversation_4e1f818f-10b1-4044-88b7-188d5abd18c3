{"AOE": [{"Id": "Test_Fireball_Explosion", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/FireballScroll_Explosion", "OnCreate": ["AOEScript.PlayVFXOnCreate(ArtResource/ProjectRogue/VFX/ParagonZ/P_IggyScorch_Turret_Explosion)"], "OnTick": [], "OnRemoved": ["AOEScript.DealDamageOnRemoved(200)"], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Cheese", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/Cheese", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.SetCanGetCheeseBuffOnCharacterEnter()"], "OnCharacterLeave": ["AOEScript.RemoveCanGetCheeseBuffOnCharacterLeave()"], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MineCarExpode", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/MineCar_Explosion", "OnCreate": ["AOEScript.PlayVFXOnCreate(ArtResource/ProjectRogue/VFX/ParagonZ/P_IggyScorch_Turret_Explosion)"], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.FireAoeByItem(50)"], "OnCharacterLeave": [], "OnActorEnter": ["AOEScript.DestroySceneItem()"], "OnActorLeave": []}, {"Id": "FireAoeByItem", "Tag": ["Fire", "<PERSON><PERSON>", "FlameScroll"], "TickTime": 0, "BpPath": "Core/Item/AOE/FireAoeByItem", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByItem(50)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "ExplosionAoeByCannon_Center", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/ExplosionAoeByCannon_Center", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByTeam(0,1000)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(1000)"], "OnActorLeave": []}, {"Id": "ExplosionAoeByCannon_Range", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/ExplosionAoeByCannon_Range", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.FireAoeByTeam(0,30)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "A<PERSON>_Awake_EarthQuakeWave", "Tag": ["Awake", "EarthQuake"], "TickTime": 0, "BpPath": "Core/Item/AOE/AOE_EarthQuakeWave", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCasterProp(1.8,1,Physical,ExtraDamage,Blow,100,0,100)"], "OnCharacterLeave": [], "OnActorEnter": ["AOESCript.FireAoeHitActors(2)"], "OnActorLeave": []}, {"Id": "MouseTrap", "Tag": ["Trap"], "TickTime": 0, "BpPath": "Core/Item/AOE/MouseTrap", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "KillRatAOE", "Tag": ["SceneItem"], "TickTime": 0, "BpPath": "Core/Item/AOE/MineCar_Explosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.DestroyRat()"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "FireAoeInScene", "Tag": ["SceneItem", "Fire"], "TickTime": 0.3, "BpPath": "Core/Item/AOE/FireAoe", "OnCreate": [], "OnTick": ["AOEScript.DealDamageOnTick(10)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "LavaAoeInScene", "Tag": ["SceneItem", "<PERSON><PERSON>"], "TickTime": 1, "BpPath": "Core/Item/AOE/FireAoe", "OnCreate": [], "OnTick": ["AOEScript.DoLavaDamageOnTick(0.03)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "LavaExplode", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/Rouge/Trap/LavaExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.DealDamageOnTouch(10,Physical,DirectDamage,Keep,5,ImmuneLava)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "LavaFireBurstAOE", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/Rouge/Trap/LavaFireBurstAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.DoLavaDamageOnTouch(0.08)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "LavaResidue", "Tag": ["<PERSON><PERSON>"], "TickTime": 0.3, "BpPath": "Core/Item/Rouge/Trap/LavaResidue", "OnCreate": [], "OnTick": ["AOEScript.DoLavaDamageOnTick(0.01)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "CastleTrapFire", "Tag": ["SceneItem", "Fire"], "TickTime": 0.3, "BpPath": "Core/Item/AOE/CastleTrapFire", "OnCreate": [], "OnTick": ["AOEScript.DealDamageOnTick(5)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "CastleTrapFire02", "Tag": ["SceneItem", "Fire"], "TickTime": 0.3, "BpPath": "Core/Item/AOE/CastleTrapFire02", "OnCreate": [], "OnTick": ["AOEScript.DealDamageOnTick(5)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "DesertQuickSandAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Rouge/Trap/DesertQuickSandAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "BarrelExplodeAOE", "Tag": [], "TickTime": 0, "BpPath": "Core/Item/Rouge/Trap/BarrelExplodeAOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOESCript.DoLavaDamageOnTouch(0.2)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "BlowAroundSphere", "Tag": ["SceneItem", "Fire"], "TickTime": 0, "BpPath": "Core/Item/AOE/AOE_BlowAroundSphere", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealMoveVelocityOnTouch(300,0,100,ToMontageState,KnockOut,7,.7)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MeteoriteExplode", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/Monster/Rogue_AoE_MeteoriteExplosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)", "AOEScript.DealMoveVelocityOnTouch(300,0,300,ToMontageState,KnockOut,5,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MeteoriteExplode_Terrian", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/AOE/Monster/Rogue_AoE_MeteoriteExplosion_Terrian", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": ["AOEScript.DealDamageByCsterPropOnTouch(1.2,0,<PERSON>,RogueJuniorDamage,ToMontageState,3)", "AOEScript.DealMoveVelocityOnTouch(300,0,300,ToMontageState,KnockOut,5,1)"], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MeteoriteLava", "Tag": ["<PERSON><PERSON>"], "TickTime": 1, "BpPath": "Core/Item/AOE/MeteoriteLava", "OnCreate": [], "OnTick": ["AOEScript.DoLavaDamageOnTick(0.03)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "MeteoriteLava_S", "Tag": ["<PERSON><PERSON>"], "TickTime": 1, "BpPath": "Core/Item/AOE/MeteoriteLava_S", "OnCreate": [], "OnTick": ["AOEScript.DoLavaDamageOnTick(0.03)"], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Beam_Aoe_Base", "Tag": ["Explsion"], "TickTime": 0, "BpPath": "Core/Item/Monster/Lava_Golem/Beam_Aoe_Base", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "Beam_AoE_Explosion", "Tag": ["Explsion"], "TickTime": 0.02, "BpPath": "Core/Item/Monster/Lava_Golem/Beam_AoE_Explosion", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "TrackingBomb_Explosion_AOE", "Tag": ["Explsion"], "TickTime": 0.3, "BpPath": "Core/Item/AOE/TrackingBomb_Explosion_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}, {"Id": "TrackingBomb_AOE", "Tag": [], "TickTime": 1, "BpPath": "Core/Item/AOE/TrackingBomb_AOE", "OnCreate": [], "OnTick": [], "OnRemoved": [], "OnCharacterEnter": [], "OnCharacterLeave": [], "OnActorEnter": [], "OnActorLeave": []}]}