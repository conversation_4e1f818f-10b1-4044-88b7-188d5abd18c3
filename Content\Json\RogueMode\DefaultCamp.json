{"说明": {"作用": "默认的阵营关系", "CampId": "<uint8> 阵营的id，这是一个严肃的数字", "Sides": ["<uint8> 这个阵营包含的Side的id"], "CanAttackCamps": ["<uint8>这个阵营可以攻击的阵营(campId)数组"], "CanInteractSides": ["<uint8>这个阵营的人可以跟哪几个Side的人交谈，如果他们能交谈，并且InWar()==false的话"]}, "Camps": [{"说明": "玩家所在阵营", "CampId": 0, "Sides": [0, 10, 20], "CanAttackCamps": [1, 2, 3], "CanInteractSides": [5, 6, 7, 8, 9]}, {"说明": "一般敌人（如鼠人）所在阵营", "CampId": 1, "Sides": [1, 2, 3], "CanAttackCamps": [0, 2, 3], "CanInteractSides": []}, {"说明": "第二敌人阵营", "CampId": 2, "Sides": [4, 5, 6], "CanAttackCamps": [0, 1, 3], "CanInteractSides": []}, {"说明": "第三敌人阵营", "CampId": 3, "Sides": [7, 8, 9], "CanAttackCamps": [1, 2, 0], "CanInteractSides": []}]}