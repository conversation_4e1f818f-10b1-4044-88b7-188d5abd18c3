{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "1"}]}]}}, {"Id": "<PERSON>_Wasteland_Wolf", "Actions": [{"Id": "NormalAttack_S2", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 5}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "Front": 1, "Back": -99, "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 1}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 8, "MaxActionCD": 13}, {"Id": "NormalAttack_S3", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 400, "Weight": 5}, {"MinRange": 400, "MaxRange": 99999, "Weight": -99}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 1}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "OutofCamera": -5, "WaitAction": true, "MinActionCD": 7, "MaxActionCD": 11}, {"Id": "NormalAttack_S1", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": -99}, {"MinRange": 300, "MaxRange": 800, "Weight": 5}, {"MinRange": 800, "MaxRange": 99999, "Weight": -99}], "HPWeight": [{"MinRange": 0, "MaxRange": 0.5, "Weight": 3}, {"MinRange": 0.5, "MaxRange": 1, "Weight": 1}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "OutofCamera": -5, "MinWaitCD": 0.1, "MaxWaitCD": 0.5, "MinActionCD": 4, "MaxActionCD": 8}, {"Id": "Action_Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 0.05}, {"MinRange": 300, "MaxRange": 600, "Weight": 0.1}, {"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "MinActionCD": 6, "MaxActionCD": 9}, {"Id": "Action_Stare03", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": -99}, {"MinRange": 300, "MaxRange": 600, "Weight": 0.1}, {"MinRange": 600, "MaxRange": 1000, "Weight": 0.2}, {"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 5, "MaxActionCD": 10}, {"Id": "Dodge_DashStep_LeftFrontR", "BaseWeight": 0.01, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 0.01}, {"MinRange": 300, "MaxRange": 600, "Weight": 0.1}, {"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "0.1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "0.1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 6, "MaxActionCD": 10}, {"Id": "Dodge_DashStep_RightFrontL", "BaseWeight": 0.01, "DistanceWeight": [{"MinRange": 0, "MaxRange": 300, "Weight": 0.01}, {"MinRange": 300, "MaxRange": 600, "Weight": 0.1}, {"MinRange": 600, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"MinRange": "<float>这个角度区间的最小值", "MaxRange": "<float>这个角度区间的最大值", "Weight": "<float>这个角度区间内，这个动作的权重加成"}, {"例子": "正面范围", "MinRange": "-45", "MaxRange": "45", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "0.1"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "0.1"}, {"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "0.1"}, {"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "0.1"}], "HPWeight": [{"MinRange": 0, "MaxRange": 1, "Weight": 1}], "MinActionCD": 6, "MaxActionCD": 10}]}]}