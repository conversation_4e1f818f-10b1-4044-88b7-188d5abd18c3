{"Mob": [{"Id": "Ogre", "Tag": ["Ogre"], "BpPath": "Core/Characters/Ogre/Ogre_New", "XAI": ["OgreTestAction"], "AI": ["OgreProvoke", "OgreRage", "OgreWeak", "OgreThrowPillar", "OgrePullUpPillar", "OgreMoveToNearestPillar", "OgreCreatePillar", "OgreBasicBattle"], "OldAI": ["OgreProvoke", "OgreRage", "OgreStare", "OgreStare_WithPillar", "OgrePullUpPillar", "OgreMoveToNearestPillar", "OgreCreatePillar", "OgreAttackHigherEnemy", "OgreAttackHigherEnemy_WithPillar", "OgreAttackVeryNearViewdEnemy", "OgreAttackVeryNearViewdEnemy_WithPillar", "OgreAttackNearViewdEnemy", "OgreAttackNearViewdEnemy_WithPillar", "OgreAttackMiddleViewdEnemy", "OgreAttackMiddleViewdEnemy_WithPillar", "OgreAttackFarViewdEnemy", "OgreAttackFarViewdEnemy_WithPillar", "OgreAttackOutViewEnemy", "OgreAttackOutViewEnemy_WithPillar", "OgreTurn", "OgreTurn_WithPillar", "TurnToStimulate"], "AIOrder": [{"OrderId": "MoveToLoc", "bReversedCheckTag": true, "CheckTag": "", "AIScriptIdList": ["AIOrderMoveToLocation"]}, {"OrderId": "MoveToTarget", "bReversedCheckTag": false, "CheckTag": "Attack", "AIScriptIdList": ["AIOrderMoveToEnemy"]}, {"OrderId": "Auto", "bReversedCheckTag": true, "CheckTag": "", "AIScriptIdList": []}], "DungeonCampImpact": [{"DungeonId": "TestDungeon", "CampId": "<PERSON><PERSON><PERSON>", "ModValue": -500}], "OnBeKilled": ["MobBeKilled.OgreBeKilled()"], "LootPackageId": "Ogre", "ExpGiven": 500, "StartFightingWillLevel": 1, "MaxFightingWillLevel": 3, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "巨魔", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 10000, "SightZRadius": 500, "SightHalfAngleDregee": 120, "LoseSightRadius": 12000, "LoseSightHalfAngleDregee": 130, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 50, "Attack": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [{"Id": "AddRageWhenBeHurt", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "OgreAlwaysRage", "Stack": 1, "Time": 0, "Infinity": true}, {"Id": "AccumulateDamageAndHurt", "Stack": 0, "Time": 0, "Infinity": true}], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 0.7}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 0.35}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 0.45}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 0.3}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "这是石柱子的部位", "Id": "<PERSON><PERSON>", "Meat": {"Physical": 0.3}, "Breakable": {"Physical": 1.0}, "Part": "Weapon", "Durability": [75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75, 75], "CanBeDestroy": true, "Type": "Rock", "HideSightPart": ["RockPillar"], "OnPartBroken": ["OgreAIScript.PillarBroken()"]}], "NotInitChaParts": ["<PERSON><PERSON>"], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack"]}, "Priority": 0, "Anim": {"StateFunc": "StateAnimPickFunc.StateByBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/BlendSpace/Monster/Ogre/Unarmed/Move", "ArtResource/Anim/BlendSpace/Monster/Ogre/Pillar/Move"]}, "InitAction": true}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/<PERSON><PERSON>/Monster/Ogre/Hurt/Dead"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/Ogre/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/Monster/Ogre/Hurt/HurtFromFront", "ArtResource/Anim/Montage/Monster/Ogre/Hurt/HurtFromBehind"]}, "InitAction": true}, {"说明": "FightingWill0时跳跃落地会摔倒，用这个", "Id": "SlipOnGround", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Monster/Ogre/Hurt/SlipOnGround"]}, "InitAction": true}, {"说明": "FightingWill0时前冲类会摔倒，用这个", "Id": "FallOnGround", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Monster/Ogre/Hurt/FallOnGround"]}, "InitAction": true}, {"说明": "被自己召唤的石头砸中", "Id": "StunByPillar", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Mont<PERSON>/Monster/Ogre/Hurt/FallOnGround"]}, "InitAction": true}, {"notes": "吼叫", "Id": "Roar", "Cmds": ["Roar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/<PERSON><PERSON>/Montage/Monster/Ogre/Attack/Unarmed_Roar"]}, "InitAction": true}, {"notes": "切换到虚弱状态", "Id": "ChangeToWeak", "Cmds": ["ChangeToWeak"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/StateChange_Weak"]}, "InitAction": true}, {"notes": "Attack_S1", "Id": "Attack_S1", "Cmds": ["Attack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S1", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S1"]}, "InitAction": true}, {"notes": "Attack_S2", "Id": "Attack_S2", "Cmds": ["Attack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S2", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S2"]}, "InitAction": true}, {"notes": "Attack_M1", "Id": "Attack_M1", "Cmds": ["Attack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_M1", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_M1"]}, "InitAction": true}, {"notes": "Attack_L1", "Id": "Attack_L1", "Cmds": ["Attack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L1", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_L1"]}, "InitAction": true}, {"notes": "Attack_L1_Down", "Id": "Attack_L1_Down", "Cmds": ["Attack_L1_Down"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L1_Down"]}, "InitAction": true}, {"notes": "跳", "Id": "Attack_L2", "Cmds": ["Attack_L2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L2", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_L2"]}, "InitAction": true}, {"notes": "跳", "Id": "Attack_L2_Down", "Cmds": ["Attack_L2_Down"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_L2_Down"]}, "InitAction": true}, {"notes": "Attack_S5", "Id": "Attack_S5", "Cmds": ["Attack_S5"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S5_TurnRAtk", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S5_TurnRAtk"]}, "InitAction": true}, {"notes": "Attack_S6", "Id": "Attack_S6", "Cmds": ["Attack_S6"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S6", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S6"]}, "InitAction": true}, {"notes": "Attack_S7", "Id": "Attack_S7", "Cmds": ["Attack_S7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S7", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S7"]}, "InitAction": true}, {"说明": "丢柱子 throw pillar", "notes": "Attack_XL1", "Id": "Attack_XL1", "Cmds": ["Attack_XL1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Normal_Attack_XL1", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Rage_Attack_XL1"]}, "InitAction": true}, {"notes": "Attack_S3_TurnRAtk", "Id": "Attack_S3_TurnRAtk", "Cmds": ["Attack_S3_TurnRAtk"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S3_TurnRAtk", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S3_TurnRAtk"]}, "InitAction": true}, {"notes": "Attack_S4_TurnLAtk", "Id": "Attack_S4_TurnLAtk", "Cmds": ["Attack_S4_TurnLAtk"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.CheckFightingWillLevel(2)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Normal_Attack_S4_TurnLAtk", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Rage_Attack_S4_TurnLAtk"]}, "InitAction": true}, {"notes": "TurnL", "Id": "TurnL", "Cmds": ["TurnL"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_TurnL"]}, "InitAction": true}, {"notes": "TurnR", "Id": "TurnR", "Cmds": ["TurnR"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_TurnR"]}, "InitAction": true}, {"notes": "Pillar_TurnL", "Id": "Pillar_TurnL", "Cmds": ["Pillar_TurnL"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_TurnL"]}, "InitAction": true}, {"notes": "Pillar_TurnR", "Id": "Pillar_TurnR", "Cmds": ["Pillar_TurnR"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_TurnR"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作", "Id": "CallPillar", "Cmds": ["CallPillar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["CallPillar1"], "1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作第二次", "Id": "CallPillar1", "Cmds": ["CallPillar1"], "Tags": [{"Tag": "CallPillar1", "From": 0.9}], "BeCancelledTags": {"0": ["CallPillar2"], "1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar1"]}, "InitAction": true}, {"notes": "吼叫掉落柱子动作第三次", "Id": "CallPillar2", "Cmds": ["CallPillar2"], "Tags": [{"Tag": "CallPillar2", "From": 0.9}], "BeCancelledTags": {"1": ["SpecialHurt"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_CallPillar2"]}, "InitAction": true}, {"notes": "拔起柱子动作", "Id": "PickUpPillar", "Cmds": ["PickUpPillar"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_PickUpPillar"]}, "InitAction": true}, {"notes": "发呆动作01", "Id": "Stare01", "Cmds": ["Stare01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare01", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare01"]}, "InitAction": true}, {"notes": "发呆动作02", "Id": "Stare02", "Cmds": ["Stare02"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare02", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare02"]}, "InitAction": true}, {"notes": "发呆动作03", "Id": "Stare03", "Cmds": ["Stare03"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare03", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare03"]}, "InitAction": true}, {"notes": "发呆动作04", "Id": "Stare04", "Cmds": ["Stare04"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare04", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare04"]}, "InitAction": true}, {"notes": "发呆动作05", "Id": "Stare05", "Cmds": ["Stare05"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare05", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare05"]}, "InitAction": true}, {"notes": "发呆动作06", "Id": "Stare06", "Cmds": ["Stare06"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Stare06", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Stare06"]}, "InitAction": true}, {"notes": "挑衅动作01", "Id": "Taunt01", "Cmds": ["Taunt01"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_Taunt01", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_Taunt01"]}, "InitAction": true}, {"notes": "RageState", "Id": "RageState", "Cmds": ["RageState"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "MontageAnimPickFunc.GetBuffStack(OgreWithPillar)", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Attack/Unarmed_RageState", "ArtResource/Anim/Montage/Monster/Ogre/Attack/Pillar_RageState"]}, "InitAction": true}, {"notes": "被石柱砸到的特殊受击", "Id": "HurtFromRock", "Cmds": ["HurtFromRock"], "Tags": [{"Tag": "SpecialHurt", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": false, "Priority": 51, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/Monster/Ogre/Hurt/HurtFromRock"]}, "InitAction": true}]}]}