{"Class": [{"说明": "双刀战士", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SoundBase": "Audio/Sound_Cue/Character/Female/<PERSON><PERSON>_Voice/", "CanChangeClass": ["Warrior", "<PERSON><PERSON><PERSON>", "Swordsman"], "Buffs": ["Character_Element"], "BaseActionType": "<PERSON><PERSON><PERSON>", "StateActions": {"Ground": {"Armed": "TwinSword_Move", "Unarmed": "TwinSword_Unarmed_Move"}, "Flying": {"Armed": "TwinSword_Move", "Unarmed": "TwinSword_Unarmed_Move"}, "Falling": {"Armed": "TwinSword_Fall", "Unarmed": "TwinSword_Unarmed_Fall"}, "Attached": {"Armed": "TwinSword_Ride", "Unarmed": "TwinSword_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "TwinSword_Hurt", "UnArmed": "TwinSword_Hurt"}, "Blow": {"Armed": "TwinSword_Blow", "UnArmed": "TwinSword_Blow"}, "Frozen": {"Armed": "TwinSword_Frozen", "UnArmed": "TwinSword_Frozen"}, "Bounced": {"Armed": "TwinSword_Bounced", "UnArmed": "TwinSword_Bounced"}, "Dead": {"Armed": "TwinSword_Dead", "UnArmed": "TwinSword_Dead"}, "Landing": {"Armed": "TwinSword_JustFall", "UnArmed": "TwinSword_Unarmed_JustFall"}, "SecondWind": {"Armed": "TwinSword_SecWind", "UnArmed": "TwinSword_SecWind"}, "GetUp": {"Armed": "TwinSword_RevivedOnSecWind", "UnArmed": "TwinSword_RevivedOnSecWind"}}, "FallWeight": 120.0, "MountsTypeRotate": false, "Potential": {"HP": 10, "MP": 4, "SP": 6, "MoveSpeed": [210, 525, 890], "BeStrikeRate": 1.0, "CriticalChance": 0.1, "CriticalRate": 1.5, "AirDodgePoint": 1}, "WeaponType": "TwinSword", "DefaultWeapons": ["Skeleton_Sword", "Skeleton_Sword"], "ActionOnChangeTo": "ChangeToBladeDancer", "ClassBuff": [], "Actions": [], "RogueBaseActions": [{"Line": "_______________________________双剑徒手基础动作________________________________"}, {"说明": "双剑徒手走路站立", "Id": "TwinSword_Unarmed_Move", "Cmds": ["Move"], "Tags": [{"Tag": "UnarmedMove", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge", "TwinSword_Aim", "TwinSword_DrawWeapon", "Unarm_UseItem", "Interactive", "TS_DrawAttack", "TS_SkillAttack"], "1": ["TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge", "TwinSword_Aim", "TwinSword_DrawWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/UnarmedMove", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/BS_Throw_Aim"]}}, {"说明": "双剑徒手起跳", "Id": "Unarmed_TwinSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "TwinSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": ["TS_Air_DrawAttack", "TS_AirSkillAttack"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/UnarmedJump"]}, "CanStopSprint": false, "Priority": 3}, {"说明": "双剑徒手翻滚", "Id": "TwinSword_Unarmed_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "TwinSword_Unarmed_Dodge", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_Unarmed_Move", "TwinSword_Unarmed_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "双剑徒手下落", "Id": "TwinSword_Unarmed_Fall", "Cmds": [], "Tags": [{"Tag": "TwinSword_Unarmed_Jump", "From": 0}], "BeCancelledTags": {"0": [], "1": ["UnarmedMove"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/UnarmedFall"]}, "Priority": 1}, {"说明": "双剑徒手下落着地", "Id": "TwinSword_Unarmed_JustFall", "BeCancelledTags": {"0": ["UnarmedMove", "TwinSword_Unarmed_Jump", "Interactive", "TS_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/UnarmedJustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/UnarmedJustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"说明": "双剑收刀", "Id": "SheathTwinSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "TwinSword_ShealthWeapon", "From": 0}], "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/SheathWeapon"]}}, {"说明": "双剑拔刀", "Id": "DrawTwinSword", "Cmds": ["ToggleWeapon"], "Tags": [{"Tag": "TwinSword_DrawWeapon", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/DrawWeapon"]}}, {"Line": "_______________________________双剑(LevelSquencer)动作________________________________"}, {"说明": "Rogue大厅开场动作", "Id": "Rogue_Hall_Begin", "Cmds": ["Rogue_Hall_Begin"], "Tags": [{"Tag": "Rogue_Hall_Begin", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_Begin_Hall"]}, "InitAction": true}, {"说明": "Rogue大厅重生动作", "Id": "<PERSON>_<PERSON>_Respawn", "Cmds": ["<PERSON>_<PERSON>_Respawn"], "Tags": [{"Tag": "<PERSON>_<PERSON>_Respawn", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TwinSword_Unarmed_Jump", "TwinSword_Unarmed_Dodge"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_Begin_DeathRespawn_Hall"]}, "InitAction": true}, {"说明": "Rogue房间开始动作", "Id": "Rogue_Room_Start", "Cmds": ["Rogue_Room_Start"], "Tags": [{"Tag": "Rogue_Room_Start", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_Begin_Dungeon"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_01", "Cmds": ["Rogue_SecondRoundEndSeq_01"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {"0": ["Sequence"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_SecondRoundEndSeq_01"]}, "InitAction": true}, {"说明": "Rogue31关BOSS结束动画", "Id": "Rogue_SecondRoundEndSeq_02", "Cmds": ["Rogue_SecondRoundEndSeq_02"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_SecondRoundEndSeq_02"]}, "InitAction": true}, {"说明": "Rogue31关最终结束动画", "Id": "Rogue_FinalSeq", "Cmds": ["Rogue_FinalSeq"], "Tags": [{"Tag": "Sequence", "From": 0}], "BeCancelledTags": {}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/Rogue_FinalSeq"]}, "InitAction": true}, {"Line": "_______________________________双剑(持武器)基础动作________________________________"}, {"Id": "TwinSword_Move", "Cmds": ["TS_Move"], "Tags": [{"Tag": "TS_Move", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"], "1": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/Move", "ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/Move_AimState"]}}, {"Id": "TwinSword_Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["_"], "1": ["TS_Dodge"], "2": ["TwinSword_QS_B"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Hurt_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Hurt_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Hurt_Air", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Hurt_Air"]}}, {"Id": "TwinSword_Blow", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["TwinSword_QS_B"], "1": ["TwinSword_QS_F"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Front", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Up", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Back", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Blow_Front"]}}, {"Id": "TwinSword_Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Dead"]}, "InitAction": true}, {"Id": "TwinSword_Jump", "Cmds": ["Jump"], "Tags": [{"Tag": "TS_Jump", "From": 0}, {"Tag": "TS_Dodge", "From": 0}, {"Tag": "TS_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_Air_Dodge_Step"], "1": []}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/Jump"]}, "CanStopSprint": false, "Priority": 3}, {"Id": "TwinSword_Fall", "Cmds": [], "Tags": [{"Tag": "Jump", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_Air_Dodge_Step"], "1": ["TS_Move"]}, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/BlendSpace/Player/BladeDancer/Fall"]}, "Priority": 1}, {"Id": "TwinSword_AttachOnTarget", "Cmds": [], "Tags": [{"Tag": "TS_Attaching", "From": 0}], "BeCancelledTags": {}, "Priority": 20, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/AttachOnTarget"]}}, {"Id": "TwinSword_JustFall", "BeCancelledTags": {"0": ["TS_Move", "TS_Jump", "TS_Dodge", "Interactive", "TS_InitAttack"]}, "Balance": 1, "CanUseOnFalling": true, "Priority": 1, "Anim": {"StateFunc": "MontageAnimPickFunc.GetAnimByMoveSpeed()", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/JustFall", "ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Movement/JustFallMoving"]}, "CanStopSprint": false, "InitAction": true}, {"Id": "TwinSword_Dodge", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_Dodge", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Move", "TS_Jump", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Dodge_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Dodge_AJ", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_<PERSON>_AJ", "From": 0}], "BeCancelledTags": {"0": ["TS_Move", "TS_Jump"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 10, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Step_F"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Air_Dodge_Step", "Cmds": ["Dodge"], "Tags": [{"Tag": "TS_Air_Dodge_Step", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["TwinSword_Air_Dodge_Step_Second"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Air_Dodge_Dash"]}, "InitAction": true}, {"Id": "TwinSword_Air_Dodge_Step_Second", "Cmds": ["Dodge"], "Tags": [{"Tag": "TwinSword_Air_Dodge_Step_Second", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"]}, "Balance": 6, "CanUseOnFalling": true, "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Air_Dodge_Dash_Second"]}, "Cost": {"AirDodgePoint": 1}, "InitAction": true}, {"Id": "TwinSword_Just_Dodge_Success", "Cmds": ["_"], "Tags": [{"Tag": "TwinSword_Just_Dodge_Success", "From": 0}], "BeCancelledTags": {"0": ["TS_SkillAttack", "TS_Dodge_Step", "TS_Just_Dodge_CounterAtk", "AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/Dodge_F_Just_Success"]}, "Cost": {"SP": 0}, "InitAction": true}, {"Id": "TwinSword_Just_Dodge_Success_CounterAtk", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_Just_Dodge_CounterAtk", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TwinSword_SkillAttack", "TS_Dodge_Step"], "1": ["_"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 6, "CanUseOnFalling": true, "IsInvincibleOnFirstFrame": "true", "Priority": 9, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_JustDodge_CounterAtk"]}, "Cost": {"SP": 0}, "InitAction": true}, {"说明": "受身动作后翻", "Id": "TwinSword_QuickStanding_B", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "TwinSword_QS_B", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_InitAttack", "TwinSword_SkillAttack", "TwinSword_Dodge", "TwinSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/QuickStanding_B"]}, "Cost": {"SP": 0}}, {"说明": "受身双剑动作前翻", "Id": "TwinSword_QuickStanding_F", "Cmds": ["Dodge", "Jump"], "Tags": [{"Tag": "TwinSword_QS_F", "From": 0}], "BeCancelledTags": {"0": ["TwinSword_InitAttack", "TwinSword_SkillAttack", "TwinSword_Dodge", "TwinSword_Jump"]}, "Priority": 10, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Dodge/QuickStanding_F"]}, "Cost": {"SP": 0}}, {"说明": "倒地动作", "Id": "TwinSword_SecWind", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 100, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/SecondWind"]}, "InitAction": true}, {"说明": "倒地后起来的动作", "Id": "TwinSword_RevivedOnSecWind", "BeCancelledTags": {}, "Priority": 999, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/RevivedOnSecondWind"]}}, {"Line": "_______________________________双剑_防御_动作_______________________________"}, {"Id": "TwinSword_Defense", "Cmds": ["Aim"], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "TS_Defense", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Defense"]}}, {"说明": "防御成功", "Id": "TwinSword_Defense_Success", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "TS_Dodge", "TS_Defense", "Unarm_UseItem"]}, "Priority": 9, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Defense_Success"]}}, {"Line": "_______________________________双剑受击(特殊)动作________________________________"}, {"Id": "TwinSword_Frozen", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 51, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Hit/Hurt_Frozen"]}}, {"Line": "_______________________________双剑基础(附加)动作________________________________"}, {"说明": "双剑弹刀动作", "Id": "TwinSword_Bounced", "Cmds": [], "Tags": [], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_Jump", "TS_Dodge", "TS_Aim", "TwinSword_ShealthWeapon", "Unarm_UseItem", "Interactive"]}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Bounced"]}}, {"说明": "瞄准动作", "Id": "TS_Aim", "Cmds": [], "Tags": [{"Tag": "TS_Aim", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge", "TS_InitAttack"], "1": ["Buddy<PERSON><PERSON>r"]}, "CanUseOnFalling": false, "Priority": 8, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/BladeDancer_Aim"]}}, {"Line": "_______________________________法器反击_______________________________"}, {"说明": "法器反击成功了就自动变成这个了(仅用于justblock)", "Id": "FrozenParry_JustBlock", "Cmds": [], "Tags": [{"Tag": "FrozenParry_JustBlock", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/MagicItem/MagicItem_Parry_CounterAtk"]}}], "RogueBattleActions": [{"说明": "捡东西", "Id": "PickUp", "Cmds": ["Interactive"], "Tags": [{"Tag": "Interactive", "From": 0}], "BeCancelledTags": {"0": ["TS_Dodge"]}, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Action/PickUp"]}}, {"Line": "_______________________________双剑_普通攻击_动作_______________________________"}, {"Line": "_______________________________双剑_普攻_地面_______________________________"}, {"说明": "普攻1", "Id": "BladeDancer_LAttack01", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_LAttack2", "TS_LAttack2_T1"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["TS_AttackB1"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash0"]}, "InitAction": true}, {"说明": "普攻2", "Id": "BladeDancer_LAttack02", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_LAttack2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_LAttack3"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash1"]}, "InitAction": true}, {"说明": "普攻3", "Id": "BladeDancer_LAttack03", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_LAttack3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash2"]}, "InitAction": true}, {"说明": "普攻的 JustAttack 1", "Id": "<PERSON><PERSON><PERSON><PERSON>_LAttack_AJ", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AttackAJ1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_SkillAttack", "Unarm_UseItem", "TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 1, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash_AJ2"]}, "InitAction": true}, {"说明": "普攻的 JustAttack 2", "Id": "BladeDancer_LAttack_AJ2", "Cmds": ["_"], "Tags": [{"Tag": "TS_AttackAJ1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_SkillAttack", "Unarm_UseItem", "TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 1, "IsJustAttack": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash_AJ1"]}, "InitAction": true}, {"说明": "分支 普攻 1", "Id": "BladeDancer_AttackB1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AttackB1", "From": 0}], "BeCancelledTags": {"0": ["TS_AttackB2"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_SlashB1"]}, "InitAction": true}, {"说明": "分支 普攻 2", "Id": "BladeDancer_AttackB2", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AttackB2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_AttackB3"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_SlashB2"]}, "InitAction": true}, {"说明": "分支 普攻 3", "Id": "BladeDancer_AttackB3", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AttackB3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_SlashB3_2"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击2", "Id": "BladeDancer_LAttack02_T1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_LAttack2_T1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_LAttack3_T1"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash1_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击3", "Id": "BladeDancer_LAttack03_T1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_LAttack3_T1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_LAttack4_T1"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash2_T1"]}, "InitAction": true}, {"说明": "普通攻击进阶攻击4", "Id": "BladeDancer_LAttack04_T1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_LAttack4_T1", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ2", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 1, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_Slash3_T1"]}, "InitAction": true}, {"Line": "_______________________________双剑_普攻_空中_______________________________"}, {"说明": "空中普攻 1", "Id": "BladeDancer_AirLAttack1", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_Air_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirLAttack1"], "1": ["_"], "2": ["TS_AirSkillAttack", "TS_Air_Dodge_Step"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_Slash0"]}, "InitAction": true}, {"说明": "空中普攻 2", "Id": "BladeDancer_AirLAttack2", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AirLAttack1", "From": 0}], "BeCancelledTags": {"0": ["TS_AirLAttack2"], "1": ["_"], "2": ["TS_AirSkillAttack", "TS_Air_Dodge_Step"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_Slash1"]}, "InitAction": true}, {"说明": "空中普攻 3", "Id": "BladeDancer_AirLAttack3", "Cmds": ["NormalAttack"], "Tags": [{"Tag": "TS_AirLAttack2", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["_"], "2": ["TS_AirSkillAttack", "TS_Air_Dodge_Step"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_Slash2"]}, "InitAction": true}, {"Line": "_______________________________双剑_技能A_地面Action2_______________________________"}, {"说明": "RiseComboSlash，地面砍一下后再升飞攻击并后跳", "Id": "BladeDancer_RiseComboSlash", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_RiseComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "RiseComboSlash击中目标瞬间的Attack Just攻击", "Id": "BladeDancer_RiseComboSlash_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_RiseComboSlashAJ"]}, "Cost": {"MP": 0}}, {"说明": "RiseSlash，单次升飞攻击", "Id": "BladeDancer_RiseSlash", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_RiseSlash"]}, "Cost": {"MP": 0}}, {"说明": "RiseSlash击中目标瞬间的Attack Just攻击", "Id": "BladeDancer_RiseSlash_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_AirInitAttack", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_RiseSlash_AJ"]}, "Cost": {"MP": 0}}, {"说明": "上挑攻击", "Id": "BladeDancer_UpSlash", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "TS_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_UpSlash"]}, "Cost": {"MP": 0}}, {"说明": "RiseSlash击中目标瞬间的Attack Just攻击", "Id": "BladeDancer_UpSlash_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "TS_Dodge_Step"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_UpSlash_AJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能A_空中Action2_______________________________"}, {"Id": "BladeDancer_AirTwiceComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_AirInitAttack"], "1": ["TS_Air_Dodge_Step"], "2": ["TS_AirSkillAttack"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_2ComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能B_地面Action3_______________________________"}, {"说明": "向前冲刺挥出双刃", "Id": "BladeDancer_DashAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击A，也是向前冲一下", "Id": "<PERSON><PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON>_<PERSON>", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃，左砍一刀", "Id": "BladeDancer_DashAttackLeft", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}, {"Tag": "TS_DashAttackLeft", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_DashAttackRight"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_Left"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃，右砍一刀", "Id": "BladeDancer_DashAttackRight", "Cmds": [], "Tags": [{"Tag": "TS_DashAttackRight", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_DashAttackLeft"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_Right"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击B，左砍一刀", "Id": "BladeDancer_DashAttackLeft_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_Left_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击C，右砍一刀", "Id": "BladeDancer_DashAttackRight_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_Right_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺挥出双刃 的 JustAttack 攻击C，先左砍一刀再右砍一刀", "Id": "BladeDancer_DashAttackLeftRight_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 2, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashOneSlash_LeftRight_AJ"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺，刺出剑", "Id": "BladeDancer_DashSting", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashSting"]}, "Cost": {"MP": 0}}, {"说明": "向前冲刺，刺出剑AJ", "Id": "BladeDancer_Dash<PERSON><PERSON>_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step", "TS_Jump"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ3", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashSting_AJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能B_空中Action3_______________________________"}, {"说明": "向下冲刺挥出双刃", "Id": "BladeDancer_AirDashAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["TS_Air_Dodge_Step"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ4"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_DashOneSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "在空中转一圈向下冲刺挥出双刃", "Id": "BladeDancer_AirDashComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 3, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_RiseComboDashSlash"]}, "InitAction": true}, {"Line": "_______________________________双剑_技能C_地面Action4_______________________________"}, {"说明": "双刀乱舞", "Id": "BladeDancer_SwrodDanceComboAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_CircleComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "双刀移动乱舞", "Id": "BladeDancer_SwrodDanceComboMoveAttack", "Cmds": [], "Tags": [{"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_SkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ3"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_ComboSlash"]}, "Cost": {"MP": 0}}, {"说明": "JustAttack双刀乱舞", "Id": "BladeDancer_SwrodDanceDashComboAttack_AJ", "Cmds": [], "Tags": [{"Tag": "TS_AttackAJ4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["_"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 1, "IsJustAttack": true, "CanUseOnFalling": false, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/G_DashComboSlashAJ"]}, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_技能C_空中Action4_______________________________"}, {"Id": "BladeDancer_AirSwrodDanceComboAttack_Fall", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["TS_Air_Dodge_Step"], "3": ["TS_AttackAJ1", "TS_AttackAJ2", "TS_AttackAJ3"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 4, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_FallCircleComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Id": "BladeDancer_AirSwrodDanceComboAttack_Dash", "Cmds": [], "Tags": [{"Tag": "TS_AirInitAttack", "From": 0}, {"Tag": "TS_AirSkillAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "TS_SkillAttack", "Unarm_UseItem"], "1": ["TS_Dodge_Step"], "2": ["TS_Air_Dodge_Step"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Priority": 4, "CanUseOnFalling": true, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/A_DashComboSlash"]}, "InitAction": true, "Cost": {"MP": 0}}, {"Line": "_______________________________双剑_动作连招式_轻重击_______________________________"}, {"说明": "双剑地面普攻1", "Id": "BladeDancer_Attack_GN1", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN1", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN2", "TS_Attack_GH2"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["TS_AttackB1"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN1"]}, "InitAction": true}, {"说明": "双剑地面普攻2", "Id": "BladeDancer_Attack_GN2", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN2", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN3", "TS_Attack_GH3"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "Balance": 3, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN2"]}, "InitAction": true}, {"说明": "双剑地面普攻3", "Id": "BladeDancer_Attack_GN3", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN3", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN4", "TS_Attack_GH4"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN3"]}, "InitAction": true}, {"说明": "双剑地面普攻4", "Id": "BladeDancer_Attack_GN4", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN4", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN5", "TS_Attack_GH5"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN4"]}, "InitAction": true}, {"说明": "双剑地面普攻5", "Id": "BladeDancer_Attack_GN5", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN5", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN6", "TS_Attack_GH6"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN5"]}, "InitAction": true}, {"说明": "双剑地面普攻6", "Id": "BladeDancer_Attack_GN6", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN6", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN7", "TS_Attack_GH7"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN6"]}, "InitAction": true}, {"说明": "双剑地面普攻7", "Id": "BladeDancer_Attack_GN7", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GN7", "From": 0.0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GN7"]}, "InitAction": true}, {"说明": "双剑地面重攻1", "Id": "BladeDancer_Attack_GH1", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH1", "From": 0}, {"Tag": "TS_InitAttack", "From": 0}, {"Tag": "TS_DrawAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_AN1", "TS_Attack_AH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻2", "Id": "BladeDancer_Attack_GH2", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH2", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻3", "Id": "BladeDancer_Attack_GH3", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH3", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻4", "Id": "BladeDancer_Attack_GH4", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH4", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 4, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH4"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻5", "Id": "BladeDancer_Attack_GH5", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH5", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_AN1", "TS_Attack_AH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 5, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH5"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻6", "Id": "BladeDancer_Attack_GH6", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH6", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 6, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH6"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面重攻7", "Id": "BladeDancer_Attack_GH7", "Cmds": [], "Tags": [{"Tag": "TS_Attack_GH7", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack", "Unarm_UseItem"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": false, "Priority": 7, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_GH7"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面空中普攻1", "Id": "BladeDancer_Attack_AN1", "Cmds": [], "Tags": [{"Tag": "TS_Attack_AN1", "From": 0}, {"Tag": "TS_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_AN2", "TS_Attack_AH1", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step", "TS_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_AN1"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面空中普攻2", "Id": "BladeDancer_Attack_AN2", "Cmds": [], "Tags": [{"Tag": "TS_Attack_AN2", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_AN3", "TS_Attack_AH1", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step", "TS_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_AN2"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面空中普攻3", "Id": "BladeDancer_Attack_AN3", "Cmds": [], "Tags": [{"Tag": "TS_Attack_AN3", "From": 0}], "BeCancelledTags": {"0": ["TS_Attack_AH1", "TS_AirSkillAttack", "TS_Air_Dodge_Step"], "1": ["TS_Dodge_Step", "TS_Defense"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_AN3"]}, "InitAction": true, "Cost": {"MP": 0}}, {"说明": "双剑地面空中重攻1", "Id": "BladeDancer_Attack_AH1", "Cmds": [], "Tags": [{"Tag": "TS_Attack_AH1", "From": 0}, {"Tag": "TS_AirInitAttack", "From": 0}], "BeCancelledTags": {"0": ["TS_InitAttack", "Unarm_UseItem", "TS_Attack_GN1", "TS_Attack_GH1"], "1": ["TS_Dodge_Step", "TS_Defense"], "2": ["TS_SkillAttack"], "3": ["_"], "4": ["_"], "5": ["AwakenSkill", "UseArtifact"]}, "CanUseOnFalling": true, "Priority": 3, "Anim": {"StateFunc": "", "Anim": ["ArtResource/ProjectRogue/Anim/Montage/Player/BladeDancer/Battle/Attack_AH1"]}, "InitAction": true, "Cost": {"MP": 0}}]}], "Buff": [], "Aoe": []}