{"Shop": [{"Id": "TownShopKeeperLevel1", "描述": "1级的商店", "FailSFX": "Audio/Sound_Cue/UI/ConfirmKey_No", "Deals": [{"Id": "Potion", "Name": "Name_TradingGoods1_0", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_0", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 22}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 1}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion1", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}, {"Id": "Potion10", "Name": "Name_TradingGoods1_1", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_1", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 185, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 10}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion10", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}], "OnDeal": [], "Currency": [{"Id": "Gold", "Icon": "ArtResource/UI/Icon/icon_money"}]}, {"Id": "TownShopKeeperLevel2", "描述": "2级的商店", "FailSFX": "Audio/Sound_Cue/UI/ConfirmKey_No", "Deals": [{"Id": "Potion", "Name": "Name_TradingGoods1_0", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_0", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 20}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 1}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion1", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}, {"Id": "Potion10", "Name": "Name_TradingGoods1_1", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_1", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 150, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 10}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion10", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}, {"Id": "FireBallScroll", "Name": "Name_TradingGoods1_2", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/Fireball", "Description": "Desc_TradingGoods1_2", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 80, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 1}], "Appearance": {"BluePrintPath": "Core/Shop/FireBall_Pot", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 0.7, "Y": 0.7, "Z": 0.7}}}], "OnDeal": [], "Currency": [{"Id": "Gold", "Icon": "ArtResource/UI/Icon/icon_money"}]}, {"Id": "TownShopKeeperLevel3", "描述": "3级的商店", "FailSFX": "Audio/Sound_Cue/UI/ConfirmKey_No", "Deals": [{"Id": "Potion", "Name": "Name_TradingGoods1_0", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_0", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 15}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 1}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion1", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}, {"Id": "Potion10", "Name": "Name_TradingGoods1_1", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/HP_Poition", "Description": "Desc_TradingGoods1_1", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 100, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "HealingPotion", "Count": 10}], "Appearance": {"BluePrintPath": "Core/Shop/HealingPotion10", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 1, "Y": 1, "Z": 1}}}, {"Id": "FireBallScroll", "Name": "Name_TradingGoods1_2", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/Fireball", "Description": "Desc_TradingGoods1_2", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 70, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 1}], "Appearance": {"BluePrintPath": "Core/Shop/FireBall_Pot", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 0.7, "Y": 0.7, "Z": 0.7}}}, {"Id": "FireBallScroll3", "Name": "Name_TradingGoods1_3", "Icon": "ArtResource/UI/2022Y11M_Versions/Icon/Fireball", "Description": "Desc_TradingGoods1_2", "SFX": "Audio/Sound_Cue/UI/GetItem_Buy_ShoppingConfirm", "VFX": "ArtResource/ProjectRogue/VFX/ParagonZ/P_<PERSON>_Primary_NoHit_Yellow", "Price": [{"Type": "<PERSON><PERSON><PERSON><PERSON>", "Id": "Gold", "Count": 220, "Icon": "ArtResource/UI/Icon/icon_money"}], "Good": [{"Type": "<PERSON><PERSON>", "Id": "FireBallScroll", "Count": 3}], "Appearance": {"BluePrintPath": "Core/Shop/FireBall_Pot3", "Position": {"X": 0, "Y": 0, "Z": 0}, "Rotation": {"Roll": 0, "Yaw": 0, "Pitch": 0}, "Scale": {"X": 0.7, "Y": 0.7, "Z": 0.7}}}], "OnDeal": [], "Currency": [{"Id": "Gold", "Icon": "ArtResource/UI/Icon/icon_money"}]}]}