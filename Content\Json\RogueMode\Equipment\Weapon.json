{"OldWeapon": [{"Id": "BigSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/BigSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"Id": "TestSword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/SmallSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/SmallSword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"Id": "TestSpear", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"Id": "TestShield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Shield_D3", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Shield_D3", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone", "Show": {"Min": 8, "Max": 10}}, {"BluePrintPath": "Core/Characters/Equipment/Shield_D2", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone", "Show": {"Min": 4, "Max": 7}}, {"BluePrintPath": "Core/Characters/Equipment/Shield_D1", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone", "Show": {"Min": 1, "Max": 3}}]}, {"说明": "________________________________________________大剑_______________________________________________________"}, {"说明": "铁制双手剑", "Id": "Iron_GreatSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Iron_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "旧式贵族大剑", "Id": "OldFashionedNoble_GreatSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/OldFashionedNoble_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "精灵大剑", "Id": "Elven_GreatSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Elven_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黑骑士大剑", "Id": "DarkKnight_GreatSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/DarkKnight_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "王之骑士大剑", "Id": "KingsKnight_GreatSword", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/KingsKnight_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "莱格里斯骑士大剑", "Id": "Legolas_Claymore", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Legolas_Claymore", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "________________________________________________长剑_______________________________________________________"}, {"说明": "铁质直剑", "Id": "Iron_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Iron_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Iron_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "哥布林钝刃", "Id": "Goblin_<PERSON>ber", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Goblin_Saber", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Goblin_Saber", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "蜂刃", "Id": "<PERSON><PERSON><PERSON>", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Bee_Sting", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Bee_Sting", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "礼仪用剑", "Id": "Ceremony_Saber", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Ceremony_Saber", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Ceremony_Saber", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "骷髅直剑", "Id": "Skeleton_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Skeleton_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Skeleton_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金长剑", "Id": "Golden_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "莱格里斯骑士剑", "Id": "Legolas_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Legolas_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Legolas_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "________________________________________________盾牌_______________________________________________________"}, {"说明": "木制筝形盾", "Id": "Wooden_Shield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Wooden_Shield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Wooden_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "骷髅圆盾", "Id": "Skeleton_Buckler", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Skeleton_Buckler", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Skeleton_Buckler", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "精灵筝形盾", "Id": "Elven_Shield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Elven_Shield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Elven_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金风筝盾", "Id": "Golden_Shield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Shield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金筝形盾", "Id": "Golden_LargeShield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_LargeShield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_LargeShield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "圣树铁盾", "Id": "HolyTree_Shield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/HolyTree_Shield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/HolyTree_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "________________________________________________长枪_______________________________________________________"}, {"说明": "铁质长枪", "Id": "Iron_Spear", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Iron_Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "鱼叉枪", "Id": "Harpoon", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Harpoon", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "卫兵长戟", "Id": "<PERSON><PERSON><PERSON><PERSON>", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Soldier_Halberd", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "精灵长枪", "Id": "<PERSON><PERSON>_<PERSON>", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Elven_Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金三叉戟", "Id": "Golden_Trident", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Trident", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金十字枪", "Id": "Golden<PERSON><PERSON>_Spear", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/GoldenCross_Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黄金长戟", "Id": "<PERSON>_Halberd", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Golden_Halberd", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "黑尖枪", "Id": "DarkSpear", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/DarkSpear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}], "Weapon": [{"说明": "________________________________________________Rogue_______________________________________________________"}, {"说明": "__________ OneHandSword(剑盾用) __________"}, {"说明": "肉鸽 单手剑剑 遗迹探索者", "Id": "<PERSON>_<PERSON>_<PERSON>uin's_Explorer", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Claymore_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 剑 圣树之证", "Id": "<PERSON>_<PERSON>_<PERSON><PERSON>'s_Proof", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Golden_sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 剑 守卫者的号角", "Id": "Rouge_Sword_Guardian's_Horn", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 剑 鲜血封印", "Id": "Rouge_Sword_Blood_Seal", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Royal_sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 剑 真神恩赐", "Id": "<PERSON>_Sword_Truly_God's_Grace", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Evil_eye_greatsword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "__________ Shield __________"}, {"说明": "肉鸽 盾 遗迹探索者", "Id": "<PERSON>_<PERSON>_<PERSON>uin's_Explorer", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Wooden_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 盾 圣树之证", "Id": "Rouge_<PERSON>_<PERSON><PERSON>'s_Proof", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/SteelShield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 盾 守卫者的号角", "Id": "Rouge_Shield_Guardian's_Horn", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Royal_shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 盾 鲜血封印", "Id": "Rouge_Shield_Blood_Seal", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Royal_round_shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 盾 真神恩赐", "Id": "<PERSON>_Shield_Truly_God's_Grace", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "__________ OneHandSword(双剑用) __________"}, {"说明": "肉鸽 双剑 轻语之刃", "Id": "Rouge_DualSword_Whispering_Blades", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Whispering_Blades", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Whispering_Blades", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 双剑 弃誓者", "Id": "Rouge_DualSword_<PERSON><PERSON>_<PERSON>uitter", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Oath_Quitter", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Oath_Quitter", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 双剑 女王之骸", "Id": "Rouge_DualSword_The_Queen's_Skeleton", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Insect_queen_sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Insect_queen_sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 双剑 黑夜渡鸦", "Id": "Rouge_DualSword_Night_Ravens", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Night_Raven", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Night_Raven", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 双剑 荣光剑", "Id": "Rouge_DualSword_Glory_Swords", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/GloryBlade", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/GloryBlade", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "__________ Pole<PERSON>rm __________"}, {"说明": "肉鸽 枪 辉光之牙", "Id": "<PERSON>_<PERSON><PERSON>_Sheen_<PERSON>s", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Elf_Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 枪 圣约", "Id": "<PERSON>_<PERSON><PERSON>_<PERSON>_<PERSON>nan", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Holy_Covenan", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 枪 降临前兆", "Id": "<PERSON>_<PERSON><PERSON>_Omen_of_Descent", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Omen_of_Descent", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 枪 潮汐之泪", "Id": "<PERSON>_<PERSON><PERSON>_Tear_of_Tidal", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Trident", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 枪 矮人之傲", "Id": "<PERSON>_<PERSON><PERSON>_Pride_of_Dwarf", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Dwarf_BroddI_Spear_Dwarf_Broddi_Spear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 关刀", "Id": "<PERSON>_<PERSON><PERSON>_<PERSON>uan<PERSON>o", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Guandao", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 唐剑", "Id": "<PERSON>_<PERSON>_Tang<PERSON>", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Tang<PERSON><PERSON>", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "__________ BigSword __________"}, {"说明": "肉鸽 大剑 破誓者", "Id": "<PERSON>_BigS<PERSON>_Oath_Breaker", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Royal_Greatsword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 大剑 王之刃", "Id": "Rouge_BigSword_King's_Blade", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/King_Greatsword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 大剑 骑士意志", "Id": "Rouge_BigS<PERSON>_<PERSON>'s_Will", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Claymore_01", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 大剑 精灵之怒", "Id": "Rouge_BigSword_Wrath_of_<PERSON>f", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Elf_GreatSword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽 大剑 黄金誓约", "Id": "Rouge_BigSword_Golden_Oath", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Golden_Oath_Greatsword", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}], "RogueOldWeapon": [{"说明": "________________________________________________Rogue_______________________________________________________"}, {"说明": "肉鸽·莱格里斯骑士大剑", "Id": "Rouge_Legolas_Claymore", "WeaponType": "BigSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Legolas_Claymore", "BindPointId": ["RightWeapon", "Weapon_Socket_Gsword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·剑", "Id": "Rouge_Iron_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Fighter_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Fighter_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·盾", "Id": "Rouge_Elven_Shield", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Elven_Shield", "BindPointId": ["RightWeapon", "RightWeapon"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Elven_Shield", "BindPointId": ["LeftWeapon_Shield", "Weapon_Socket_Shield_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·莱格里斯骑士剑", "Id": "Rouge_Legolas_Sword", "WeaponType": "OneHandSword", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Legolas_Sword", "BindPointId": ["RightWeapon", "Weapon_Socket_Sword_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/Legolas_Sword", "BindPointId": ["LeftWeapon", "Weapon_Socket_Sword_L"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·枪", "Id": "<PERSON>_<PERSON><PERSON>", "WeaponType": "PoleArm", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 1, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Rogue/DarkSpear", "BindPointId": ["RightWeapon_Spear", "Weapon_Socket_Spear_R"], "PartSlot": "RightHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·游侠短弓", "Id": "<PERSON>_<PERSON><PERSON>ow", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [], "OffAppearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/ShortBow", "BindPointId": ["Weapon_Socket_ShortBow", "Weapon_Socket_ShortBow_Sheath"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}]}, {"说明": "肉鸽·法师法杖", "Id": "Ranger_Stave", "WeaponType": "Shield", "CharacterPart": "", "Priority": 0, "MeatType": "Metal", "Durability": 10, "Appearance": [{"BluePrintPath": "Core/Characters/Equipment/Weapons/Stave", "BindPointId": ["Weapon_Socket_Stave", "Weapon_Socket_Stave_Sheath"], "PartSlot": "LeftHand", "AppearanceType": "Normal", "PhysicalBoneName": "PhysicalBone"}], "OffAppearance": []}]}