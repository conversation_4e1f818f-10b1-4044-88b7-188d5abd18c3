{"RogueMobActionValue": [{"范例": {"Id": "Mob的Id", "Actions": [{"Id": "这个Action的Id", "BaseWeight": "<float>这个动作的基础权重，不填的话默认为1", "DistanceWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个距离区间内，这个动作的权重加成"}], "Front": "<float>敌人位于自己正面的权重加成", "Back": "<float>敌人位于背后的权重加成", "HPWeight": [{"MinRange": "<float>这个距离区间的最小值", "MaxRange": "<float>这个距离区间的最大值", "Weight": "<float>这个生命值区间内，这个动作的权重加成"}]}]}}, {"Id": "Rogue_Skeleton_Soilder", "Actions": [{"Id": "NormalAttack_S1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 250, "Weight": 5}, {"MinRange": 250, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-60", "MaxRange": "60", "Weight": "1"}, {"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "NormalAttack_P1", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 0, "MaxRange": 250, "Weight": 5}, {"MinRange": 250, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "OutofCamera": -6, "WaitAction": true, "MinActionCD": 0, "MaxActionCD": 0}, {"Id": "Stare01", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 700, "MaxRange": 99999, "Weight": -9999}], "DegreeWeight": [{"例子": "正面范围", "MinRange": "-60", "MaxRange": "60", "Weight": "-9999"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 50}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 2}, {"Id": "Walk_Left", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 700, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "右面范围", "MinRange": "45", "MaxRange": "135", "Weight": "-99"}], "MinActionCD": 3, "MaxActionCD": 5}, {"Id": "Walk_Right", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 700, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "左面范围", "MinRange": "-135", "MaxRange": "-45", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 50}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 5}, {"Id": "Walk_Front", "BaseWeight": 1, "DistanceWeight": [{"MinRange": 1000, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 0, "MaxActionCD": 3}, {"Id": "Walk_Back", "BaseWeight": 0, "DistanceWeight": [{"MinRange": 700, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [], "BuffWeight": [{"BuffId": "Rogue_MobForceStare", "BuffStack": 1, "Weight": 3}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 2, "Weight": 30}, {"BuffId": "Rogue_MobForceStare", "BuffStack": 3, "Weight": 999}], "MinActionCD": 3, "MaxActionCD": 14}, {"Id": "Action_Defense", "BaseWeight": -99, "DistanceWeight": [{"MinRange": 0, "MaxRange": 500, "Weight": 7}, {"MinRange": 500, "MaxRange": 99999, "Weight": -99}], "DegreeWeight": [{"例子": "背面范围1", "MinRange": "135", "MaxRange": "180", "Weight": "-99"}, {"例子": "背面范围2", "MinRange": "-180", "MaxRange": "-135", "Weight": "-99"}], "MinActionCD": 10, "MaxActionCD": 18}]}]}