{"Dialogs": [{"说明": "Fandral初次对话", "Id": "Rogue_Fandral_FirstDialog", "FirstClip": "FirstDialog", "NpcId": [], "Clips": [{"Id": "FirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_VeryFirstDialog1"}], "Selections": [{"说明": "Rogue_Fandral故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueFandralStoryDialogPicker()", "Actions": []}]}, {"说明": "Rogue_Fandral初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_First_StoryDialog,1)"]}], "EndDialogScript": ["DialogAction.SetRogueSwitch(Fandral_VeryFirst_Dialog,1)"]}, {"说明": "Rogue_Fandral 默认开始对话", "Id": "Rogue_Fandral_DefaultStartDialog", "FirstClip": "StartDialog", "NpcId": [], "Clips": [{"Id": "StartDialog", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_FirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_FirstDialog2"}], "Selections": [{"说明": "Rogue_Fandral故事对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionDialog", "NextEventFunc": "NpcDialogPicker.RogueFandralStoryDialogPicker()", "Actions": []}, {"说明": "Rogue_Fandral结束对话", "Icon": "ArtResource/UI/Icon/Item/potion_green", "Text": "SelectionEndDialog", "NextEventFunc": "", "Actions": []}]}, {"说明": "Rogue_Fandral初次故事对话", "Id": "StoryFirstDialog", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialog4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_First_StoryDialog,1)"]}, {"说明": "Rogue_Fandral初次对话后的重复对话", "Id": "StoryFirstDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_StoryFirstDialogRepeat1"}], "Selections": []}, {"说明": "Rogue_Fandral 第二次的对话", "Id": "StorySecondDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHall1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHall2"}, {"Type": "Speak", "Id": "Step2", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHall3"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_Second_StoryDialog,1)"]}, {"说明": "Rogue_Fandral 第二次的重复对话", "Id": "StorySecondDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallRepeat1"}], "Selections": []}, {"说明": "Rogue_Fandral 第三次的对话", "Id": "StoryThirdDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain3"}, {"Type": "Speak", "Id": "Step3", "NextId": "Step4", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain4"}, {"Type": "Speak", "Id": "Step4", "NextId": "Step5", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain5"}, {"Type": "Speak", "Id": "Step5", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgain6"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_Third_StoryDialog,1)"]}, {"说明": "Rogue_Fandral 第三次的重复对话", "Id": "StoryThirdDialogRepeat", "FirstEvent": "Step0", "FirstEventFunc": "DialogAction.RandomFirstEventId(Step0,Step1)", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgainRepeat1"}, {"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_InHallAgainRepeat2"}], "Selections": []}, {"说明": "Rogue_Fandral 二周目初次的对话", "Id": "WeeklyRoundDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_ObtainSecondGrail1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_ObtainSecondGrail2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_ObtainSecondGrail3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_ObtainSecondGrail4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_WeeklyRound_StoryDialog,1)"]}, {"说明": "Rogue_Fandral二周目重复对话", "Id": "WeeklyRoundDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_ObtainSecondGrailRepeat1"}], "Selections": []}, {"说明": "Rogue_Fandral 最终BOSS后的对话", "Id": "KillFinalBossDialog", "FirstEvent": "Step0", "FirstEventFunc": "", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "Step1", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_DefeatDeathLord1"}, {"Type": "Speak", "Id": "Step1", "NextId": "Step2", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_DefeatDeathLord2"}, {"Type": "Speak", "Id": "Step2", "NextId": "Step3", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_DefeatDeathLord3"}, {"Type": "Speak", "Id": "Step3", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_DefeatDeathLord4"}], "EndClipScript": ["DialogAction.SetRogueSwitch(Fandral_KillFinalBoss_Dialog,1)"]}, {"说明": "Rogue_Fandral 最终BOSS后重复对话", "Id": "KillFinalBossDialogRepeat", "FirstEvent": "Step0", "Dialogs": [{"Type": "Speak", "Id": "Step0", "NextId": "", "TargetType": "Target", "Slot": 0, "Name": "<PERSON><PERSON><PERSON>", "Text": "Fandral_DefeatDeathLordRepeat1"}], "Selections": []}], "EndDialogScript": []}]}