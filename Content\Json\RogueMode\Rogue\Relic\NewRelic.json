{"RelicInfo": [{"id": "Relic0101", "GroupId": "Relic01", "描述": "血80以上，伤害提升", "Desc": "Relic0101_Desc", "RelicType": "Attack", "RecordId": "1", "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_NoInjury(10)"]}, {"id": "Relic0102", "GroupId": "Relic01", "描述": "血80以上，伤害提升", "Desc": "Relic0102_Desc", "Tags": ["Group_Azouk"], "RelicType": "Attack", "RecordId": "1", "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_NoInjury(20)"]}, {"id": "Relic0103", "GroupId": "Relic01", "描述": "血80以上，伤害提升", "Desc": "Relic0103_Desc", "Tags": ["Group_Azouk"], "RelicType": "Attack", "RecordId": "1", "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Fire", "F08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_NoInjury(40)"]}, {"id": "Relic0201", "GroupId": "Relic02", "描述": "低于50%血量攻击力上升", "Desc": "Relic0201_Desc", "RelicType": "Attack", "RecordId": "2", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_BackWater(20)"]}, {"id": "Relic0202", "GroupId": "Relic02", "描述": "自身生命值越低伤害越高", "Desc": "Relic0202_Desc", "RelicType": "Attack", "RecordId": "2", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_BackWater(30)"]}, {"id": "Relic0203", "GroupId": "Relic02", "描述": "自身生命值越低伤害越高", "Desc": "Relic0203_Desc", "RelicType": "Attack", "RecordId": "2", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_BackWater(50)"]}, {"id": "Relic0301", "GroupId": "Relic03", "描述": "造成伤害增加，受到伤害增加", "RelicType": "Attack", "RecordId": "3", "Desc": "Relic0301_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_DamageUp(20)", "Rogue_HurtUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"id": "Relic0302", "GroupId": "Relic03", "描述": "造成伤害增加受到伤害增加", "RelicType": "Attack", "RecordId": "3", "Desc": "Relic0302_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_DamageUp(30)", "Rogue_HurtUp(30)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"id": "Relic0303", "GroupId": "Relic03", "描述": "造成伤害增加，受到伤害增加", "RelicType": "Attack", "RecordId": "3", "Desc": "Relic0303_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Magic", "D08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_DamageUp(50)", "Rogue_HurtUp(50)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(DamageUp,Rogue_DamageUp,Rogue_DamageDown,100,1)", "RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtUp,Rogue_HurtDown,0,-1,Survive)"]}, {"id": "Relic0401", "GroupId": "Relic04", "描述": "暗闪避", "Desc": "Relic0401_Desc", "RelicType": "Attack", "RecordId": "4", "Tags": ["Group_Eminendanis"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Magic", "D06"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["DarkDodgeAoe"]}, {"id": "Relic0402", "GroupId": "Relic04", "描述": "暗闪避", "Desc": "Relic0402_Desc", "RelicType": "Attack", "RecordId": "4", "Tags": ["Group_Eminendanis"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Magic", "D06"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["DarkDodgeAoe(2)"]}, {"id": "Relic0403", "GroupId": "Relic04", "描述": "暗闪避", "Desc": "Relic0403_Desc", "RelicType": "Attack", "RecordId": "4", "Tags": ["Group_Eminendanis"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Magic", "D06"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["DarkDodgeAoe(3)"]}, {"id": "Relic0501", "GroupId": "Relic05", "描述": "风完美闪避", "Desc": "Relic0501_Desc", "RelicType": "Attack", "RecordId": "5", "Tags": ["Group_Zantia", "Word_RogueWind"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Wind", "W06"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Anim_DogeCreateWindAoe"]}, {"id": "Relic0502", "GroupId": "Relic05", "描述": "风完美闪避", "Desc": "Relic0502_Desc", "RelicType": "Attack", "RecordId": "5", "Tags": ["Group_Zantia", "Word_RogueWind"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Wind", "W06"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["<PERSON><PERSON>_DogeCreateWindAoe(2)"]}, {"id": "Relic0503", "GroupId": "Relic05", "描述": "风完美闪避", "Desc": "Relic0503_Desc", "RelicType": "Attack", "RecordId": "5", "Tags": ["Group_Zantia", "Word_RogueWind"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Dodge"], "IconPath": ["Rogue_Wind", "W06"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["<PERSON><PERSON>_DogeCreateWindAoe(3)"]}, {"id": "Relic0601", "GroupId": "Relic06", "描述": "雷暴击aoe", "Desc": "Relic0601_Desc", "RelicType": "Attack", "RecordId": "6", "Tags": ["Group_Poltick", "Word_RogueElectric"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Thunder", "T07"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["<PERSON><PERSON>_<PERSON>oe"]}, {"id": "Relic0602", "GroupId": "Relic06", "描述": "雷暴击aoe", "Desc": "Relic0602_Desc", "RelicType": "Attack", "RecordId": "6", "Tags": ["Group_Poltick", "Word_RogueElectric"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Thunder", "T07"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["<PERSON><PERSON><PERSON>(2)"]}, {"id": "Relic0603", "GroupId": "Relic06", "描述": "雷暴击aoe", "Desc": "Relic0603_Desc", "RelicType": "Attack", "RecordId": "6", "Tags": ["Group_Poltick", "Word_RogueElectric"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Passive"], "IconPath": ["Rogue_Thunder", "T07"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["<PERSON><PERSON><PERSON>(3)"]}, {"id": "Relic0701", "GroupId": "Relic07", "描述": "BreakDown时提升受击伤害提升50%", "Desc": "Relic0701_Desc", "RelicType": "Attack", "RecordId": "7", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(2000)"]}, {"id": "Relic0702", "GroupId": "Relic07", "描述": "BreakDown时提升受击伤害提升50%", "Desc": "Relic0702_Desc", "RelicType": "Attack", "RecordId": "7", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(3000)"]}, {"id": "Relic0703", "GroupId": "Relic07", "描述": "BreakDown时提升受击伤害提升50%", "Desc": "Relic0703_Desc", "RelicType": "Attack", "RecordId": "7", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_Thunder", "T08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_DamageUpWhenTargetBreakDown(5000)"]}, {"id": "Relic0801", "GroupId": "Relic08", "描述": "暴击回血", "Desc": "Relic0801_Desc", "RelicType": "Survive", "RecordId": "8", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["HealHpOnCrit(1)"]}, {"id": "Relic0802", "GroupId": "Relic08", "描述": "暴击回血", "Desc": "Relic0802_Desc", "RelicType": "Survive", "RecordId": "8", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["HealHpOnCrit(2)"]}, {"id": "Relic0803", "GroupId": "Relic08", "描述": "暴击回血", "Desc": "Relic0803_Desc", "RelicType": "Survive", "RecordId": "8", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["HealHpOnCrit(3)"]}, {"id": "Relic0901", "GroupId": "Relic09", "描述": "暴击回法器能量", "Desc": "Relic0901_Desc", "RelicType": "Survive", "RecordId": "9", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["RecoverItemOnCrit(1)"]}, {"id": "Relic0902", "GroupId": "Relic09", "描述": "暴击回法器能量", "Desc": "Relic0902_Desc", "RelicType": "Survive", "RecordId": "9", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["RecoverItemOnCrit(2)"]}, {"id": "Relic0903", "GroupId": "Relic09", "描述": "暴击回法器能量", "Desc": "Relic0903_Desc", "RelicType": "Survive", "RecordId": "9", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Heal"], "IconPath": ["Rogue_Thunder", "T12"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["RecoverItemOnCrit(3)"]}, {"id": "Relic1001", "GroupId": "Relic10", "描述": "光受到伤害降低", "RelicType": "Survive", "RecordId": "10", "Desc": "Relic1001_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Defence"], "IconPath": ["Rogue_Light", "L11"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_HurtDown(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtDown,Rogue_HurtUp,0,1,Survive)"]}, {"id": "Relic1002", "GroupId": "Relic10", "描述": "光受到伤害降低", "RelicType": "Survive", "RecordId": "10", "Desc": "Relic1002_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Defence"], "IconPath": ["Rogue_Light", "L11"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_HurtDown(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtDown,Rogue_HurtUp,0,1,Survive)"]}, {"id": "Relic1003", "GroupId": "Relic10", "描述": "光受到伤害降低", "RelicType": "Survive", "RecordId": "10", "Desc": "Relic1003_Desc", "Tags": ["Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Defence"], "IconPath": ["Rogue_Light", "L11"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_HurtDown(40)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(HurtDown,Rogue_HurtDown,Rogue_HurtUp,0,1,Survive)"]}, {"id": "Relic1101", "GroupId": "Relic11", "描述": "直接伤害增加", "RelicType": "Attack", "RecordId": "11", "Desc": "Relic1101_Desc", "Tags": ["Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_DirectDamageUp(10)"]}, {"id": "Relic1102", "GroupId": "Relic11", "描述": "直接伤害增加", "RelicType": "Attack", "RecordId": "11", "Desc": "Relic1102_Desc", "Tags": ["Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_DirectDamageUp(20)"]}, {"id": "Relic1103", "GroupId": "Relic11", "描述": "直接伤害增加", "RelicType": "Attack", "RecordId": "11", "Desc": "Relic1103_Desc", "Tags": ["Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_DirectDamageUp(40)"]}, {"id": "Relic1201", "GroupId": "Relic12", "描述": "法器攻击力增加20%", "RelicType": "Attack", "RecordId": "12", "Desc": "Relic1201_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_ItemDamageUp(10)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)"]}, {"id": "Relic1202", "GroupId": "Relic12", "描述": "法器攻击力增加20%", "RelicType": "Attack", "RecordId": "12", "Desc": "Relic1202_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_ItemDamageUp(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)"]}, {"id": "Relic1203", "GroupId": "Relic12", "描述": "法器攻击力增加20%", "RelicType": "Attack", "RecordId": "12", "Desc": "Relic1203_Desc", "Tags": ["OtherGods", "Group_Azouk"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Attack"], "IconPath": ["Rogue_None", "H08"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_ItemDamageUp(40)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemDamageUp,Rogue_ItemDamageUp,Rogue_ItemDamageDown,100,1)"]}, {"id": "Relic1301", "GroupId": "Relic13", "描述": "获得金币提高20%", "RelicType": "Other", "RecordId": "13", "Desc": "Relic1301_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_GetCoinUp(10)"]}, {"id": "Relic1302", "GroupId": "Relic13", "描述": "获得金币提高20%", "RelicType": "Other", "RecordId": "13", "Desc": "Relic1302_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_GetCoinUp(20)"]}, {"id": "Relic1303", "GroupId": "Relic13", "描述": "获得金币提高20%", "RelicType": "Other", "RecordId": "13", "Desc": "Relic1303_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Coin"], "IconPath": ["Rogue_None", "H13"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_GetCoinUp(40)"]}, {"id": "Relic1401", "GroupId": "Relic14", "描述": "获得魂晶提高10%", "RelicType": "Other", "RecordId": "14", "Desc": "Relic1401_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_GetSoulUp(10)"]}, {"id": "Relic1402", "GroupId": "Relic14", "描述": "获得魂晶提高20%", "RelicType": "Other", "RecordId": "14", "Desc": "Relic1402_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_GetSoulUp(20)"]}, {"id": "Relic1403", "GroupId": "Relic14", "描述": "获得魂晶提高40%", "RelicType": "Other", "RecordId": "14", "Desc": "Relic1403_Desc", "Tags": ["OtherGods", "Group_OriginalCoin"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Soul"], "IconPath": ["Rogue_None", "H16"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_GetSoulUp(40)"]}, {"id": "Relic1601", "GroupId": "Relic16", "描述": "breakdown时暴击率增加30%", "RecordId": "16", "Desc": "Relic1601_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H07", "Rogue_7"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_CriticalChanceUpWhenTargetLowBreak(1000)"]}, {"id": "Relic1602", "GroupId": "Relic16", "描述": "breakdown时暴击率增加30%", "Desc": "Relic1602_Desc", "RecordId": "16", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H07", "Rogue_7"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_CriticalChanceUpWhenTargetLowBreak(2000)"]}, {"id": "Relic1603", "GroupId": "Relic16", "描述": "breakdown时暴击率增加30%", "Desc": "Relic1603_Desc", "RecordId": "16", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H07", "Rogue_7"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_CriticalChanceUpWhenTargetLowBreak(4000)"]}, {"id": "Relic1701", "GroupId": "Relic17", "描述": "暴击率增加10%", "RelicType": "Attack", "RecordId": "17", "Desc": "Relic1701_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_CriticalChanceUp(1000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtRate(CtRate,Rogue_RelicCastCriticalChance,0.0001,999,100)"]}, {"id": "Relic1702", "GroupId": "Relic17", "描述": "暴击率增加20%", "RelicType": "Attack", "RecordId": "17", "Desc": "Relic1702_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_CriticalChanceUp(2000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtRate(CtRate,Rogue_RelicCastCriticalChance,0.0001,999,100)"]}, {"id": "Relic1703", "GroupId": "Relic17", "描述": "暴击率增加40%", "RelicType": "Attack", "RecordId": "17", "Desc": "Relic1703_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_CriticalChanceUp(4000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtRate(CtRate,Rogue_RelicCastCriticalChance,0.0001,999,100)"]}, {"id": "Relic1801", "GroupId": "Relic18", "描述": "暴击伤害增加10%", "RelicType": "Attack", "RecordId": "18", "Desc": "Relic1801_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_CriticalRateUp(1000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtDmg(CtDmg,Rogue_RelicCastCriticalRate,0.0001,999,100)"]}, {"id": "Relic1802", "GroupId": "Relic18", "描述": "暴击伤害增加20%", "RelicType": "Attack", "RecordId": "18", "Desc": "Relic1802_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_CriticalRateUp(2000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtDmg(CtDmg,Rogue_RelicCastCriticalRate,0.0001,999,100)"]}, {"id": "Relic1803", "GroupId": "Relic18", "描述": "暴击伤害增加40%", "RelicType": "Attack", "RecordId": "18", "Desc": "Relic1803_Desc", "Tags": ["OtherGods", "Group_Ardipeng"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_Crit"], "IconPath": ["Rogue_None", "H09"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_CriticalRateUp(4000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_RelicNumCastCtDmg(CtDmg,Rogue_RelicCastCriticalRate,0.0001,999,100)"]}, {"id": "Relic1901", "GroupId": "Relic19", "描述": "法器充能速度增加20%", "Desc": "Relic1901_Desc", "RelicType": "Other", "RecordId": "19", "Tags": ["OtherGods", "Group_Lunara"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_CD"], "IconPath": ["Rogue_None", "H18"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["RogueItemValueRecover(5)"]}, {"id": "Relic1902", "GroupId": "Relic19", "描述": "法器充能速度增加40%", "Desc": "Relic1902_Desc", "RelicType": "Other", "RecordId": "19", "Tags": ["OtherGods", "Group_Lunara"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_CD"], "IconPath": ["Rogue_None", "H18"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["RogueItemValueRecover(10)"]}, {"id": "Relic1903", "GroupId": "Relic19", "描述": "法器充能速度增加80%", "Desc": "Relic1903_Desc", "RelicType": "Other", "RecordId": "19", "Tags": ["OtherGods", "Group_Lunara"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_CD"], "IconPath": ["Rogue_None", "H18"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["RogueItemValueRecover(20)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffStack(ItemRecoverUp,RogueItemRecover,RogueItemExtend,100,1,Other)"]}, {"id": "Relic2201", "GroupId": "Relic22", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2201_Desc", "RelicType": "Attack", "RecordId": "22", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Skill_1_AddDirectionDamage01"]}, {"id": "Relic2202", "GroupId": "Relic22", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2202_Desc", "RelicType": "Attack", "RecordId": "22", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Skill_1_AddDirectionDamage02"]}, {"id": "Relic2203", "GroupId": "Relic22", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2203_Desc", "RelicType": "Attack", "RecordId": "22", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Skill_1_AddDirectionDamage03"]}, {"id": "Relic2301", "GroupId": "Relic23", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2301_Desc", "RelicType": "Attack", "RecordId": "23", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Skill_2_AddDirectionDamage01"]}, {"id": "Relic2302", "GroupId": "Relic23", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2302_Desc", "RelicType": "Attack", "RecordId": "23", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Skill_2_AddDirectionDamage02"]}, {"id": "Relic2303", "GroupId": "Relic23", "描述": "上挑后2s物理伤害提升30%", "Desc": "Relic2303_Desc", "RelicType": "Attack", "RecordId": "23", "Tags": ["OtherGods", "Group_Erictus"], "MaxNum": 1, "IconKeys": ["Attribute", "RelicType_"], "IconPath": ["Rogue_None", "H03"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Skill_2_AddDirectionDamage03"]}, {"id": "Relic2401", "GroupId": "Relic24", "描述": "生命值增加20%", "Desc": "Relic2401_Desc", "RecordId": "24", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H11", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_MaxHealthPercentUp(1000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(Hp,Rogue_MaxHealthPercentUp,1,Survive)"]}, {"id": "Relic2402", "GroupId": "Relic24", "描述": "生命值增加30%", "Desc": "Relic2402_Desc", "RecordId": "24", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H12", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_MaxHealthPercentUp(2000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(Hp,Rogue_MaxHealthPercentUp,1,Survive)"]}, {"id": "Relic2403", "GroupId": "Relic24", "描述": "生命值增加40%", "Desc": "Relic2403_Desc", "RecordId": "24", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H12", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_MaxHealthPercentUp(4000)"], "OnRelicPreview": ["RelicScript.OnRelicPreview_BuffProp(Hp,Rogue_MaxHealthPercentUp,1,Survive)"]}, {"id": "Relic2501", "GroupId": "Relic25", "描述": "格挡闪避反击伤害提高", "Desc": "Relic2501_Desc", "RecordId": "25", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H11", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_CounterDamageUp(160)"]}, {"id": "Relic2502", "GroupId": "Relic25", "描述": "格挡闪避反击伤害提高", "Desc": "Relic2502_Desc", "RecordId": "25", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H11", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_CounterDamageUp(240)"]}, {"id": "Relic2503", "GroupId": "Relic25", "描述": "格挡闪避反击伤害提高", "Desc": "Relic2503_Desc", "RecordId": "25", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "H11", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_CounterDamageUp(400)"]}, {"id": "Relic2601", "GroupId": "Relic26", "描述": "击杀后回血", "Desc": "Relic2601_Desc", "RecordId": "26", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "D12", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["HealHp<PERSON>n<PERSON>ill(3)"]}, {"id": "Relic2602", "GroupId": "Relic26", "描述": "击杀后回血", "Desc": "Relic2602_Desc", "RecordId": "26", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "D12", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["HealHp<PERSON>n<PERSON>ill(5)"]}, {"id": "Relic2603", "GroupId": "Relic26", "描述": "击杀后回血", "Desc": "Relic2603_Desc", "RecordId": "26", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "D12", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["<PERSON>al<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(10)"]}, {"id": "Relic2701", "GroupId": "Relic27", "描述": "反击伤害后回血", "Desc": "Relic2701_Desc", "RecordId": "27", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "L12", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["HealHpOnCounter(1)"]}, {"id": "Relic2702", "GroupId": "Relic27", "描述": "反击伤害后回血", "Desc": "Relic2702_Desc", "RecordId": "27", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "L12", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["HealHpOnCounter(2)"]}, {"id": "Relic2703", "GroupId": "Relic27", "描述": "反击伤害后回血", "Desc": "Relic2703_Desc", "RecordId": "27", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "L12", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["HealHpOn<PERSON>ounter(4)"]}, {"id": "Relic2801", "GroupId": "Relic28", "描述": "造成break伤害提升", "Desc": "Relic2801_Desc", "RecordId": "28", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "T09", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_HitBreakUp(10)"]}, {"id": "Relic2802", "GroupId": "Relic28", "描述": "造成break伤害提升", "Desc": "Relic2802_Desc", "RecordId": "28", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "T09", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_HitBreakUp(20)"]}, {"id": "Relic2803", "GroupId": "Relic28", "描述": "造成break伤害提升", "Desc": "Relic2803_Desc", "RecordId": "28", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "T09", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_HitBreakUp(40)"]}, {"id": "Relic2901", "GroupId": "Relic29", "描述": "强力背水", "Desc": "Relic2901_Desc", "RecordId": "29", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "F08", "Rogue_2"], "Value": 200, "RelicLevel": 1, "EffectBuff": ["Rogue_BackWaterPower(10)"]}, {"id": "Relic2902", "GroupId": "Relic29", "描述": "强力背水", "Desc": "Relic2902_Desc", "RecordId": "29", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "F08", "Rogue_2"], "Value": 400, "RelicLevel": 2, "EffectBuff": ["Rogue_BackWaterPower(20)"]}, {"id": "Relic2903", "GroupId": "Relic29", "描述": "强力背水", "Desc": "Relic2903_Desc", "RecordId": "29", "Tags": ["OtherGods", "Group_Luana"], "MaxNum": 1, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "F08", "Rogue_2"], "Value": 600, "RelicLevel": 3, "EffectBuff": ["Rogue_BackWaterPower(40)"]}]}