{"Mob": [{"Id": "Rogue_MineTeamCaptain", "Tag": ["Npc"], "BpPath": "Core/Characters/NPC/GuardLeader/GuardLeader_New", "AI": ["StopAIBuff", "InfantryArm", "NormalInfantryBasicBattle", "InfantryTurnToStimulate", "InfantryUnarm"], "AIOrder": [], "StartFightingWillLevel": 1, "MaxFightingWillLevel": 4, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "MineTeamCaptain_Name", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 5000, "SightZRadius": 1500, "SightHalfAngleDregee": 135, "LoseSightRadius": 6000, "LoseSightHalfAngleDregee": 140, "NoiseRadius": 500, "HearingRadius": 3000, "LoseHearingRadius": 4000}, "Potential": {"HP": 10, "PAtk": 5, "Balance": 10, "MoveSpeed": [175, 350, 525], "BeStrikeRate": 0.75}, "Buff": [], "Equipments": [{"Id": "InfantryGreatSword", "Rate": 1}], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Unarmed_Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Move", "UnArmed": "Unarmed_Move"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "DialogAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/GuardLeader/Move_TwoHandedSword"]}, "InitAction": true}, {"Id": "Unarmed_Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/GuardLeader/Move"]}}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "MontageAnimPickFunc.Random(1)", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Dead1", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Dead2"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Front_SwordShield", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Back", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Air", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/Hurt_Air"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/BlowFromBack", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/GuardLeader/Hurt/BlowFromBack"]}}, {"说明": "拔刀动作", "Id": "EquipmentWeapon", "Cmds": ["EquipmentWeapon"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/EquipWeapon_TwoHandedSword"]}, "InitAction": true}, {"说明": "收刀动作", "Id": "UnequipmentWeapon", "Cmds": ["UnequipmentWeapon"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/UnequipWeapon_TwoHandedSword"]}, "InitAction": true}, {"说明": "近距离攻击1", "Id": "NormalAttack_S1", "Cmds": ["NormalAttack_S1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S1"]}, "InitAction": true}, {"说明": "近距离攻击2", "Id": "NormalAttack_S2", "Cmds": ["NormalAttack_S2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S2"]}, "InitAction": true}, {"说明": "近距离攻击3", "Id": "NormalAttack_S3", "Cmds": ["NormalAttack_S3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S3"]}, "InitAction": true}, {"说明": "近距离攻击4", "Id": "NormalAttack_S4_Combo", "Cmds": ["NormalAttack_S4_Combo"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S4_Combo"]}, "InitAction": true}, {"说明": "近距离攻击5", "Id": "NormalAttack_S5_Combo", "Cmds": ["NormalAttack_S5_Combo"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 11, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S5_Combo"]}, "InitAction": true}, {"说明": "近距离攻击6", "Id": "NormalAttack_S6_Counter", "Cmds": ["NormalAttack_S6_Counter"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S6_Counter"]}, "InitAction": true}, {"说明": "近距离攻击7", "Id": "NormalAttack_S7", "Cmds": ["NormalAttack_S7"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_S7"]}, "InitAction": true}, {"说明": "中距离攻击1", "Id": "NormalAttack_M1", "Cmds": ["NormalAttack_M1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_M1"]}, "InitAction": true}, {"说明": "中距离攻击2", "Id": "NormalAttack_M2", "Cmds": ["NormalAttack_M2"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_M2"]}, "InitAction": true}, {"说明": "中距离攻击3", "Id": "NormalAttack_M3", "Cmds": ["NormalAttack_M3"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_M3"]}, "InitAction": true}, {"说明": "远距离攻击1", "Id": "NormalAttack_L1", "Cmds": ["NormalAttack_L1"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Attack/NormalAttack_L1"]}, "InitAction": true}, {"说明": "前跳", "Id": "Dodge_DashStep_Front", "Cmds": ["Dodge_DashStep_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Front"]}, "InitAction": true}, {"说明": "左跳", "Id": "Dodge_DashStep_Left", "Cmds": ["Dodge_DashStep_Left"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Left"]}, "InitAction": true}, {"说明": "右跳", "Id": "Dodge_DashStep_Right", "Cmds": ["Dodge_DashStep_Right"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Right"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back", "Cmds": ["Dodge_DashStep_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Back"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back_End", "Cmds": ["Dodge_DashStep_Back_End"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Back_End"]}, "InitAction": true}, {"说明": "后跳", "Id": "Dodge_DashStep_Back_Tired", "Cmds": ["Dodge_DashStep_Back_Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Dodge_DashStep_Back_Tired"]}, "InitAction": true}, {"说明": "向前走", "Id": "Walk_Front", "Cmds": ["Walk_Front"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Walk_Front"]}, "InitAction": true}, {"说明": "向后走", "Id": "Walk_Back", "Cmds": ["Walk_Back"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Walk_Back"]}, "InitAction": true}, {"说明": "疲劳发呆动作", "Id": "Tired", "Cmds": ["Tired"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Dodge/Tired"]}, "InitAction": true}, {"说明": "原地发呆动作", "Id": "IdleDaze", "Cmds": ["IdleDaze"], "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {"0": ["NormalAction", "InitAttack"]}, "Balance": 10, "CanUseOnFalling": false, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/TwoHandedSword/Idle_Daze"]}, "InitAction": true}, {"说明": "受伤躺在地上", "Id": "InjureLieDown", "Tags": [{"Tag": "InitAttack", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 102, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_Injure_Liedown"]}, "InitAction": true}, {"说明": "短对话动作伸出右手", "Id": "ShortDialog", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Dialog_Calm_Short_01"]}, "InitAction": true}, {"说明": "长对话动作自信+展示", "Id": "LongDialog1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeaderDialog_Calm_Long_04"]}, "InitAction": true}, {"说明": "夸张", "Id": "Exaggeration", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeaderDialog_Calm_Long_04"]}, "InitAction": true}, {"说明": "鼓掌", "Id": "Clip", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_Clip"]}, "InitAction": true}, {"说明": "无奈/惋惜", "Id": "Pity", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_DontKnow"]}, "InitAction": true}, {"说明": "愤怒/激动", "Id": "Angry", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Angry"]}, "InitAction": true}, {"说明": "伤心/丧气", "Id": "Sad", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_Sad"]}, "InitAction": true}, {"说明": "中对话+自信", "Id": "MediumDialog1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Medium_02"]}, "InitAction": true}, {"说明": "中对话+自信", "Id": "MediumDialog2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Medium_04"]}, "InitAction": true}, {"说明": "中对话+自信", "Id": "MediumDialog3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Medium_05"]}, "InitAction": true}, {"说明": "短对话+自信", "Id": "ShortDialog1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Short_02"]}, "InitAction": true}, {"说明": "短对话+自信", "Id": "ShortDialog2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Short_03"]}, "InitAction": true}, {"说明": "短对话+自信", "Id": "ShortDialog3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Short_04"]}, "InitAction": true}, {"说明": "短对话", "Id": "ShortDialog4", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Short_05"]}, "InitAction": true}, {"说明": "笑", "Id": "<PERSON><PERSON>", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Laugh"]}, "InitAction": true}, {"说明": "鼓舞", "Id": "Encouragement", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_TA_ANG_AP_01"]}, "InitAction": true}, {"说明": "说服1", "Id": "Persuade1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Intense_Short_01"]}, "InitAction": true}, {"说明": "说服2", "Id": "Persuade2", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Intense_Short_02"]}, "InitAction": true}, {"说明": "说服3", "Id": "Persuade3", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Intense_Short_03"]}, "InitAction": true}, {"说明": "敬礼", "Id": "Salute", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_Salute"]}, "InitAction": true}, {"说明": "自信", "Id": "Confidence", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_TA_ANG_02"]}, "InitAction": true}, {"说明": "摇头", "Id": "Nope", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/Dialog_Calm_Medium_01"]}, "InitAction": true}, {"说明": "欢呼", "Id": "Victory", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_Victory"]}, "InitAction": true}, {"说明": "激动", "Id": "Excited", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Excited"]}, "InitAction": true}, {"说明": "示威1", "Id": "Demonstrate1", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Demonstrate"]}, "InitAction": true}, {"说明": "加油", "Id": "Fighting", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Dialog/GuardLeader_Fighting"]}, "InitAction": true}, {"说明": "向左转", "Id": "GuardLeader_TurnLeft", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_TurnLeft180"]}, "InitAction": true}, {"说明": "向右转", "Id": "GuardLeader_TurnRight", "Tags": [{"Tag": "DialogAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardLeader/Action/GuardLeader_TurnRight180"]}, "InitAction": true}]}]}