{"Mob": [{"Id": "PatrolNpc1", "Tag": ["Npc"], "BpPath": "Core/Characters/NPC/PatrolNpc/PatrolNpc1_New", "AI": ["StopAI", "NPCRunToNearestPossiblePathPoint", "NormalInfantryBasicBattle", "OrcTurnToStimulate", "MoveToProtectedSceneItemAround"], "AIOrder": [], "StartFightingWillLevel": 1, "MaxFightingWillLevel": 4, "SoundBase": "", "SoundSocket": "head", "Flyable": false, "MountsTypeRotate": false, "Name": "PatrolNpc1_Name", "Portrait": "icon_happy_luozi", "MoveProp": {"StopRangeWhenMoveFollow": 300, "MinDisWhenMoveAround": 200, "MaxDisWhenMoveAround": 500, "bNegativeDegreeWhenMoveAround": true}, "PerceptionProp": {"ViewRadius": 500, "SightRadius": 2000, "SightHalfAngleDregee": 45, "LoseSightRadius": 3000, "LoseSightHalfAngleDregee": 50, "NoiseRadius": 500, "HearingRadius": 1000, "LoseHearingRadius": 1500}, "Potential": {"HP": 50, "Attack": 5, "Balance": 10, "MoveSpeed": [145, 290, 435], "BeStrikeRate": 0.75}, "Buff": [], "Equipments": [], "Part": [{"Id": "Head", "Meat": {"Physical": 1}, "Part": "Head", "Durability": [100], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"Id": "Body", "Meat": {"Physical": 1}, "Part": "Body", "Durability": [150], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "虽然有2个手臂，但是因为只考虑肉质，也并不需要精确到2个手臂各自挨打多少才会进入OnPartBroken，所以用一条数据就行了", "Id": "Arm", "Meat": {"Physical": 1}, "Part": "Arm", "Durability": [190], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}, {"说明": "原理同手臂", "Id": "Leg", "Meat": {"Physical": 1}, "Part": "Leg", "Durability": [170], "CanBeDestroy": false, "Type": "Meat", "OnPartBroken": []}], "NotInitChaParts": [], "SpecState": {"Invincible": false, "Untouchable": false, "Stop": false, "Immortal": false}, "ControlState": {"CanMove": 0, "CanRotate": 0, "CanJump": 0, "CanChangeAction": 0}, "StateActions": {"Ground": {"Armed": "Move", "Unarmed": "Move"}, "Flying": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Falling": {"Armed": "Standard_Flying", "Unarmed": "Standard_Flying"}, "Attached": {"Armed": "Standard_Ride", "Unarmed": "Standard_Ride"}}, "PreorderActionKeys": {"Hurt": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Blow": {"Armed": "<PERSON><PERSON>", "UnArmed": "<PERSON><PERSON>"}, "Bounced": {"Armed": "Hurt", "UnArmed": "Hurt"}, "Dead": {"Armed": "Dead", "UnArmed": "Dead"}, "Landing": {"Armed": "Hurt", "UnArmed": "Hurt"}, "SecondWind": {"Armed": "Hurt", "UnArmed": "Hurt"}, "GetUp": {"Armed": "Hurt", "UnArmed": "Hurt"}}, "Actions": [{"Id": "Move", "Cmds": ["AI_Move"], "Tags": [{"Tag": "Move", "From": 0}], "BeCancelledTags": {"0": ["InitAttack", "NormalAction"]}, "Priority": 0, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Guard/Move"]}, "InitAction": true}, {"Id": "KeepShortStay", "Cmds": ["KeepShortStay"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Guard/Move"]}, "InitAction": true}, {"Id": "InterruptHurt", "Cmds": ["InterruptHurt"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Action/GuardA_Bow"]}, "InitAction": true}, {"Id": "KeepLongStay", "Cmds": ["KeepLongStay"], "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/BlendSpace/NPC/Guard/Move"]}, "InitAction": true}, {"说明": "对话", "Cmds": ["GuardA_LongSpeech"], "Id": "GuardA_LongSpeech", "Tags": [{"Tag": "NormalAction", "From": 0}], "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 10, "Anim": {"StateFunc": "", "Anim": ["ArtResource/Anim/Montage/NPC/GuardA/Dialog/GuardA_ShortSpeech1"]}, "InitAction": true}, {"Id": "Dead", "BeCancelledTags": {}, "Balance": 10, "CanUseOnFalling": true, "Priority": 101, "Anim": {"StateFunc": "MontageAnimPickFunc.Random(1)", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/Hurt/Dead1", "ArtResource/Anim/Montage/NPC/Guard/Hurt/Dead2"]}, "InitAction": true}, {"Id": "Hurt", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceFrontBehind()", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtFromFront", "ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtFromBehind", "ArtResource/Anim/Montage/NPC/Guard/Hurt/AirHurt", "ArtResource/Anim/Montage/NPC/Guard/Hurt/AirHurt"]}}, {"Id": "<PERSON><PERSON>", "Cmds": [], "Tags": [], "BeCancelledTags": {}, "Priority": 50, "CanUseOnFalling": true, "Anim": {"CheckOnTick": false, "StateFunc": "MontageAnimPickFunc.PowerSourceForBlow()", "Anim": ["ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromBack", "ArtResource/Anim/Montage/NPC/Guard/Hurt/HurtUp", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromFront", "ArtResource/Anim/Montage/NPC/Guard/Hurt/BlowFromBack"]}}], "AiActionDetails": [{"AiActionId": "KeepShortStay", "ActionDurationPolicy": "ParamPolicyScript.FloatFromRange(1,2)", "ActionType": "HasDuration", "Duration": 1}, {"AiActionId": "KeepLongStay", "ActionDurationPolicy": "ParamPolicyScript.FloatFromRange(7,10)", "ActionType": "HasDuration", "Duration": 8}]}]}