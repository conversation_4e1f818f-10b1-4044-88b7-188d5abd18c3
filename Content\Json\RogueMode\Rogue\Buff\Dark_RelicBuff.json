{"说明": "遗物相关的Buff", "Buff": [{"分割": "-------------------------------------------AnimState-----------------------------------------"}, {"说明": "动画中造成伤害时给对方一个侵蚀", "Id": "Anim_CorruptionalHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 3, "TickTime": 0, "OnHit": ["BuffUtils.AddBuffToHitTarget(Rogue_Corruption,1,10,true,false)"]}, {"说明": "动画中引发暗噬", "Id": "Anim_DarkHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(1,<PERSON>_Corruption,<PERSON>_DarkDevour,1,0,0.01,false,true,true)"]}, {"说明": "动画中引发无消耗暗噬", "Id": "Anim_DarkHitNoCost", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(1,<PERSON>_Corruption,<PERSON>_DarkDevour,1,1.5,0.01,false,true,true,false)"]}, {"说明": "动画中概率引发暗噬", "Id": "Anim_ChanceDarkHit", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.BuffChanceCastById_StackToStack(0.5,<PERSON>_Corruption,<PERSON>_DarkDevour,1,1.5,0.01,false,true,true)"]}, {"说明": "动画中引发黑暗领域", "Id": "Anim_DarkHit2", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnHit": ["RogueBuff.CreateAoeOnHitTarget(Rogue_Aoe_Eminendanis13,3.5,pelvis,<PERSON><PERSON><PERSON><PERSON>,true)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_DarkHit2,1,0,true)"]}, {"说明": "动画中产生复数黑影", "Id": "Anim_JustDodgeDark", "Tag": ["Relic", "Hit", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["RogueBuff.CreateAoeOnJustDodgeSocket(Rogue_Aoe_Eminendanis11,1,root)", "BuffUtils.RemoveSelfBuffStackOnHit(Anim_JustDodgeDark,1,0,true)"]}, {"说明": "动画中挨打概率持续给侵蚀", "Id": "<PERSON><PERSON>_<PERSON>", "Tag": ["Relic", "BeHurt", "NotSave"], "Priority": 0, "MaxStack": 1, "OnBeHurt": ["BuffUtils.AddBuffToHurtSourceByChance(1,Rogue_Corruption,1,5,true,false)"]}, {"分割": "-------------------------------------------Eminendanis-----------------------------------------"}, {"说明": "造成伤害时给对方一个侵蚀", "Id": "CorruptionalHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState", "FXRelic_NAtk_Dark"], "Priority": 0, "MaxStack": 5, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_CorruptionalHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_CorruptionalHit,1,0,true)"]}, {"说明": "造成伤害时20%概率给对方一个暗噬", "Id": "CorruptionOfErminda_2_Effect", "Tag": ["Relic", "Hit", "NotSave", "Rogue_NormalAttackState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_ChanceDarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_ChanceDarkHit,1,0,true)"]}, {"说明": "下砸技能时，被击中的目标引发“暗噬”效果。", "Id": "Smash_DarkHit", "Tag": ["Relic", "NotSave", "Rogue_SmashState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "位移技能时，在位移的终点，产生一个黑暗残像。", "Id": "Dash_DarkShadow", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashEnd", "FXRelic_Dash_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocket(Rogue_Aoe_Eminendanis6,5,root)"]}, {"说明": "位移技能时，在位移的过程中触碰到的敌人，以及攻击到的目标，会引发“暗噬”效果。", "Id": "Dash_DarkCast", "Tag": ["Relic", "Hit", "NotSave", "Rogue_DashState", "FXRelic_Dash_Dark"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "玩家蓄力时被打，反给一个侵蚀。", "Id": "Power_DarkShield", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(<PERSON><PERSON>_<PERSON>,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(<PERSON><PERSON>_<PERSON>Dark,1,0,true)"]}, {"说明": "玩家蓄力到最大层数时，这个蓄力的攻击动作/法术会引发“暗噬”效果。", "Id": "Power_DarkHit", "Tag": ["Relic", "Hit", "NotSave", "Rogue_PowerFinalState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHitNoCost,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHitNoCost,1,0,true)"]}, {"说明": "玩家闪避时，会在结束位置产生一个黑影。", "Id": "DarkDodgeAoe", "Tag": ["Relic", "NotSave", "Rogue_Dodge"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiy": ["BuffUtils.CreateAoeOnSocketByCoolDown(1,<PERSON>_<PERSON><PERSON>_Eminendanis10,5,None)"]}, {"说明": "玩家完美闪避时，在自身位置产生n个黑影", "Id": "Anim_DogeCreateDarkShadow", "Tag": ["Relic", "Rogue_JustDodge", "NotSave"], "Priority": 0, "MaxStack": 1, "OnAnimNotfiy": ["RogueBuff.CreateAoeOnJustDodgeSocketOnAnimNotify(Rogue_Aoe_Eminendanis11,1,root)"], "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_JustDodgeDark,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_JustDodgeDark,1,0,true)"]}, {"说明": "击飞技能时，击中有”侵蚀“层数的单位时，引发“暗噬”效果。", "Id": "Rise_DarkCastHit", "Tag": ["Relic", "NotSave", "Rogue_BelowState"], "Priority": 0, "MaxStack": 1, "TickTime": 0, "OnAnimNotfiyBegin": ["BuffUtils.AddBuffObj(Anim_DarkHit,1,0,true)"], "OnAnimNotfiyEnd": ["BuffUtils.RemoveSubBuffObj(Anim_DarkHit,1,0,true)"]}, {"说明": "造成暗元素伤害的时候概率对暗元素的主动道具进行额外充能", "Id": "AddItemRecover_Darkness", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnHit": ["RogueBuff.ChanceAddItemRecoverOnSameElementHit(1,0.01,Darkness)"]}, {"说明": "每过TickTime秒生成围绕的骷髅头", "Id": "Launch_DarkTickTIme", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 1, "TickTime": 10, "OnTick": ["BuffUtils.CreateAoeOnCarrierOnTick(Rogue_Aoe_Eminendanis17,0,0.1,pelvis,0,true)"]}, {"说明": "直接命中击杀敌人时产生一个追踪的黑暗子弹", "Id": "<PERSON>_<PERSON><PERSON><PERSON>", "Tag": ["Relic", "NotSave"], "Priority": 0, "MaxStack": 100, "TickTime": 0, "OnKill": ["RogueBuff.CreateAoeOnHitTargetOnCrit(Rogue_Aoe_Eminendanis18,0.1,root)"]}]}