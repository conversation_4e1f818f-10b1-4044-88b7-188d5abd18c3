{"RelicInfo": [{"分割": "-------------------------------------------Other-----------------------------------------"}, {"id": "BetterPotion", "Desc": "BetterPotion_Desc", "Tags": ["OtherGods", "NotInPool", "NotInFormal"], "MaxNum": 99, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "", "Rogue_1"], "Value": 180, "RelicRarity": "Normal", "EffectBuff": ["Rogue_RelicPotionHealLevelUp(10)"]}, {"id": "Pray_BetterPotion", "Desc": "Pray_BetterPotion_Desc", "Tags": ["NotInPool", "NotInFormal"], "MaxNum": 99, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "", "Rogue_1"], "EffectBuff": ["Rogue_RelicPotionHealLevelUp(10)"]}, {"id": "AnotherPotionBag", "Desc": "AnotherPotionBag_Desc", "Tags": ["OtherGods", "NotInPool", "NotInFormal"], "MaxNum": 999, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "", "Rogue_1"], "Value": 150, "RelicRarity": "Normal", "EffectBuff": ["Rogue_PotionNumUp(1)"]}, {"id": "Pray_AnotherPotionBag", "Desc": "<PERSON>ray_AnotherPotionBag_Desc", "Tags": ["NotInPool", "NotInFormal"], "MaxNum": 99, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "", "Rogue_1"], "EffectBuff": ["Rogue_PotionNumUp(1)"]}, {"id": "Rogue_Apple", "Desc": "Rogue_Apple_Desc", "Tags": ["NotInPool", "NotInFormal", "NotInPool"], "MaxNum": 99, "IconKeys": ["Attribute", "Relic", "SerialNumber"], "IconPath": ["Rogue_None", "", "Rogue_1"], "Value": 100, "RelicRarity": "Normal", "EffectBuff": ["Rogue_MaxHealthUp(10)"]}]}