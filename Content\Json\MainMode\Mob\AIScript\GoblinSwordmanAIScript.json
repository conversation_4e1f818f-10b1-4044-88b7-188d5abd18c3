{"AIScript": [{"说明": "小跳捶地", "Id": "Goblin_SmallJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,400,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,400,-60,60,SmallJumpAttack)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "前跳二连击", "Id": "Goblin_JumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,700,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,700,-60,60,JumpAttack)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "前跳三连击（愤怒）", "Id": "Goblin_RageJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,700,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,700,-60,60,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "加BUFF", "Id": "GoblinSwordman_AddBuff", "Condition": ["MobAIScript.CheckStimulateByView()", "MobAIScript.CheckFightingWillLevelEquals(1)", "MobAIScript.CheckFightingWillValueLess(0.2)"], "OnReady": [], "Action": ["MobAIScript.AIDoAction(AddBuff)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "前踏斩", "Id": "Goblin_FrontStepAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,500,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,500,-60,60,FrontStepAttack)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "前踏三连击（愤怒）", "Id": "Goblin_RageFrontStepAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,500,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,500,-60,60,<PERSON><PERSON><PERSON>tStepAttack)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "大跳震地", "Id": "Goblin_BigJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,1000,-60,60)", "MobAIScript.CheckFightingWillLevelGreater(0)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,1000,-60,60,<PERSON><PERSON><PERSON><PERSON><PERSON>ck)", "GoblinAIScript.ComboDaze01()"]}, {"说明": "大跳震地（愤怒）", "Id": "Goblin_RageBigJumpAttack", "Condition": ["MobAIScript.CheckHasViewedEnemyInDisAndDegreeRange(0,1000,-60,60)", "MobAIScript.CheckFightingWillLevelEquals(2)"], "OnReady": [], "Action": ["MobAIScript.AITurnToClosetViewedEnemyInDisAndDegreeRangeDoAction(0,1000,-60,60,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)", "GoblinAIScript.ComboDaze01()"]}]}